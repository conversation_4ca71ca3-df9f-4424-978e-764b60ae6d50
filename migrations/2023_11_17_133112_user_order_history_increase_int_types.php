<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_order_history', function (Blueprint $table) {
            $table->smallInteger('total_orders')->change();
            $table->smallInteger('total_paid_orders')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_order_history', function (Blueprint $table) {
            $table->tinyInteger('total_orders')->change();
            $table->tinyInteger('total_paid_orders')->change();
        });
    }
};
