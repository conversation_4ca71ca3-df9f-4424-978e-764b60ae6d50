<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn($table->getTable(), 'deleted_at_unix')) {
                $table->dropColumn(['deleted_at_unix']);
            }
            $table->bigInteger('deleted_at')->default(0)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['deleted_at']);
        });
        Schema::table('users', function (Blueprint $table) {
            $table->dateTime('deleted_at')->nullable();
            $table->bigInteger('deleted_at_unix')->default(0);
        });
    }
};
