<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products_subscriptions', function (Blueprint $table) {
            $table->unsignedInteger('credits')->default(0)->after('lifetime');
            $table->unsignedTinyInteger('active')->default(1)->after('credits');
            $table->unsignedTinyInteger('trial')->default(0)->after('active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products_subscriptions', function (Blueprint $table) {
            $table->dropColumn(['active', 'trial']);
        });
    }
};
