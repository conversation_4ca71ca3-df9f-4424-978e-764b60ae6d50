<?php

use App\Database\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

class AlterPaymentProviderConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::disableForeignKeyConstraints();

        Schema::table('payment_providers_config', function (Blueprint $table) {
            $table->dropForeignIfExists('fk_ppc_payment_providers_id_pp_id');
        });

        Schema::table('payment_providers_config', function (Blueprint $table) {
            $table->dropColumn('payment_provider_id');
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('payment_providers_config', function (Blueprint $table) {
            $table->uuid('payment_provider_id')->nullable()->after('id');
            $table->foreign('payment_provider_id', 'fk_ppc_payment_providers_id_pp_id')
                ->references('id')
                ->on('payment_providers');
        });
    }
}
