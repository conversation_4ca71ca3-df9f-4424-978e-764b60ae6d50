<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('domain_payment_methods', function (Blueprint $table) {
            $table->decimal('chargeback_fee', 8, 2)->default(0.00)->after('payment_provider_config_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('domain_payment_methods', function (Blueprint $table) {
            $table->dropColumn('chargeback_fee');
        });
    }
};
