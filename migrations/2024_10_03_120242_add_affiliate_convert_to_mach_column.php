<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private const TABLE = 'affiliates';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table(self::TABLE, function (Blueprint $table) {
            $table->boolean('mach3_enabled')->default(true);
        });

        DB::table(self::TABLE)->whereIn('alias', [
            'fabtrk',
            'fabapi',
            'fabrest',
            'nlstrk',
            'madmoney',
        ])->update(['mach3_enabled' => false]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('affiliates', function (Blueprint $table) {
            $table->dropColumn('mach3_enabled');
        });
    }
};
