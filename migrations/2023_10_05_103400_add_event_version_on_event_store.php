<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    public function up()
    {
        \Illuminate\Support\Facades\Schema::table('stored_events', function (Blueprint $table) {
            $table->unsignedTinyInteger('event_version')->default(1)->after('aggregate_version');
        });
    }

    public function down()
    {
        Schema::table('stored_events', function (Blueprint $table) {
            $table->dropColumn('event_version');
        });
    }
};
