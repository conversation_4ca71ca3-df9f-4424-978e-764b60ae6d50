<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('scheduled_task_stat_history', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('mutex_name');
            $table->timestamp('started_at');
            $table->timestamp('finished_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->timestamp('aborted_at')->nullable();
            $table->boolean('skipped')->default(false);
            $table->text('output')->nullable();
            $table->float('runtime')->nullable();
            $table->float('memory_usage')->nullable();
            $table->foreignUuid('scheduled_task_stats_id')->constrained('scheduled_task_stats');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scheduled_task_stat_history');
    }
};
