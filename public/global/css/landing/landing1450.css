body,
html {
    color: black;
    background-color: rgb(255, 255, 255);
    display: flex;
    height: 100%;
    align-items: center !important;
    justify-content: center !important;
    z-index: -1;
    text-align: left;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.background-image {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
    z-index: -1;
    display: none;
}
.landing-content {
    max-width: 570px;
}
.col-md-6,
.col-6 {
    width: 100% !important;
}
.step1 .background-image {
    display: block;
}
.top-text {
    color: white;
    text-align: center;
}
.top-text div {
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 50px;
}
.container {
    padding: 0px !important;
}
.landing-container {
    padding: 10px !important;
    margin: 0px;
}
.progress {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    margin: 0px;
    height: 10px;
    z-index: 1;
}
.progress-bar {
    background-color: #e10745;
    height: 10px;
}
.progress-bar-percentage {
    display: none;
}
.form-control {
    text-align: left !important;
    border: 0px solid #ced4da;
    border-bottom: 1px solid #ced4da;
    border-radius: 0px;
}
.step-birthday .form-control {
    text-align: center !important;
}
.age-range .form-control {
    border: 1px solid #ced4da;
}
.step1 .main-title {
    text-align: center;
}
#google-signin-btn{
    border-radius: 50rem!important;
    background-image: linear-gradient(to right, #0793e1, #0793e1)!important;
    color: white!important;
}
#google-signin-btn .google-logo{
    background-color: white;
    padding:5px;
    border-radius:5px;
}
#google-signin-btn:not(.unstyled){
    box-shadow: 0px 0px 0px grey!important;;
}
#google-signin-btn:not(.unstyled) .google-signin-label {
    padding-left: 10px!important;
}
.step-google .btn-secondary {
    background-color: rgb(240, 240, 240);
    border: grey 2px solid;
    color: grey;
}
.btn {
    border-radius: 50rem !important;
    border-width: 0px;
    width: 100%;
}
.btn i {
    display: none;
}
.step-gender .btn-secondary {
    background-color: rgb(255, 255, 255);
    border: grey 2px solid;
    color: grey;
}
.step-city input,
.step-username input,
.step-password input,
.step-email input {
    max-width: 100%;
}
.age-range .text-center {
    font-size: 20px;
}
.main-title {
    margin-bottom: 40px !important;
}
.form-check-label {
    cursor: pointer;
    margin-bottom: 50px;
}
.form-check a {
    color: #0983a9;
    text-decoration: none;
}
.form-check-input:checked {
    background-color: #e10745;
    border-color: #e10745;
}
.step input,
.step select {
    border-radius: 0px;
}
.model-a {
    position: fixed;
    left: 200px;
    bottom: 0;
    height: 80%;
    display: none;
}
.step1 .model-a {
    display: block;
}
.model-b {
    position: fixed;
    right: 200px;
    bottom: 0;
    height: 80%;
    display: none;
}
.step1 .model-b {
    display: block;
}
.btn-primary:hover {
    opacity: 0.8;
}
.btn-secondary,
.btn-primary {
    box-shadow: none !important;
}
.age-range .form-control {
    border-radius: 50rem;
}
.btn {
    font-weight: bold;
}
.btn-primary.disabled,
.btn-primary:disabled {
    background-image: linear-gradient(to right, #d5d5d5, #d5d5d5);
    border: 1px solid #000000;
}
.btn-primary,
.btn-primary:active,
.btn-primary:focus,
.btn-primary:visited {
    background-color: #e10745;
    background-image: linear-gradient(to right, #e10745, rgb(199, 14, 66));
}
.btn-check + .btn-male,
.btn-check:active + .btn-male,
.btn-check:checked + .btn-male,
.btn-male.active,
.btn-male:active,
.show > .btn-male.dropdown-toggle {
    color: #fff;
    background-color: #1674a7;
    border-color: #1674a7;
}
.btn-check + .btn-female,
.btn-check:active + .btn-female,
.btn-check:checked + .btn-female,
.btn-female.active,
.btn-female:active,
.show > .btn-female.dropdown-toggle {
    color: #fff;
    background-color: #cb2e65;
    border-color: #cb2e65;
}
.btn-secondary:focus .btn-check:focus + .btn-secondary,
.btn-check:active + .btn-secondary:focus,
.btn-check:checked + .btn-secondary:focus,
.btn-secondary.active:focus,
.btn-secondary:active:focus,
.show > .btn-secondary.dropdown-toggle:focus {
    box-shadow: none;
}

.landing-content{
    width:100%;
    max-width:570px;
}

.step{
    padding: 35px 55px;
    margin: 0px;
    background-color: white;
    border-radius: 10px;
}

.title-thank-you{
    color: white;
    text-align: center;
    display:none;
    font-weight: bold;
}

.step1 .title-thank-you{
    display: block;
}

.step-error h1 {
    display: block;
}

@media only screen and (max-width: 1500px) {
    .model-a {
        left: 5px;
    }
    .model-b {
        right: 5px;
    }
}
@media only screen and (max-width: 479px) {
    .step{
        padding: 20px;
        background-color: rgba(0, 0, 0, 0) !important;
    }
    .step1 {
        color: white;
    }
    .background-full {
        display: none;
    }
    .model-a,
    .model-b {
        display: none !important;
    }
}
@media only screen and (min-width: 480px) {
    .step1 .background-image {
        display: none;
    }
}

.alert{
    border: none;
    background: none;
    padding: 0;
}
