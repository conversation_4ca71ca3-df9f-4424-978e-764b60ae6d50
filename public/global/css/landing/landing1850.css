@import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css");

:root {
    --fontstyle-1: "Mulish", sans-serif;
    --color-1: #ececec;
    --color-2: #142550;
    --color-3: #ffbbc2;
    --color-5: #ff112a;
    --color-6: black;
    --color-7: #ff354a;
    --color-8: rgb(10, 19, 41);
    --color-9: white;
    --color-10: #0a1227;
    --color-11: rgba(10, 19, 41, 0.8);
    --color-12: rgba(237, 237, 237, 0.5);
    --element-width-1: 790px;
}

.alert-danger {
    color: var(--color-1);
    background-color: var(--color-5);
    border: none;
}

html,
body {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-family: var(--fontstyle-1);
    color: var(--color-1);
    height: 100%;
}

.background-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}

.background-container img {
    width: 100%;
}

.landing-container {
    background-color: var(--color-11);
}

.landing-content {
    max-width: 100%;
    margin-bottom: 20px;
}

.container {
    min-width: auto !important;
    max-width: var(--element-width-1);
}

.logo {
    max-height: 80px;
    max-width: 100%;
}


h1 {
    color: var(--color-3);
    font-weight: bold;
    font-size: 60px;
}

h2 {
    font-weight: bold;
    font-size: 1.2rem;
}

h5 {
    font-weight: bold;
}

.btn-primary {
    background-color: var(--color-7) !important;
    border: none !important;
    -webkit-animation: button-animation 0.5s;
    animation: button-animation 0.5s;
}

.btn-primary:hover {
    background-color: var(--color-5) !important;
}

@keyframes button-animation {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }

    50% {
        transform: scale(1.1);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}

select {
    cursor: pointer;
}

.progress-icons {
    background-color: var(--color-1);
}

.icon {
    width: 100%;
    color: var(--color-6);
    height: 40px;
    font-size: 1.2rem;
}

.icon.icon-selected {
    background-color: var(--color-3);
    color: var(--color-9);
}

.timer {
    color: var(--color-3);
    font-weight: bold;
    font-size: 2.0rem;
}

.step-number {
    background-color: var(--color-10);
}

.btn-select-input {
    display: none;
}

footer {
    background-color: var(--color-12);
    color: var(--color-6);
    line-height: 18px;
    font-size: 14px;
}
.lander_description {
    margin-bottom: 1.5rem;
}

.step-city input,
.step-username input,
.step-password input,
.step-email input {
    max-width: 100% !important;
}

.step_error {
    max-width: 450px;
    margin-left: auto;
    margin-right: auto;
}

.form-check-label {
    cursor: pointer;
}

.desktop-only {
    display: block !important;
}

.main-title {
    color: #fff !important;
    font-size: 1.5rem !important;
    font-weight: 500;
    margin-bottom: 10px;
}

.btn-primary {
    padding: 1px 30px;
}

.step {
    text-align: left;
}

.alert {
    padding: 0.2rem 0.4rem;
}

input.form-control {
    text-align: center;
}

.btn-check:checked+.btn-light {
    background-color: var(--color-1);
    color: var(--color-2);
}

.btn-light {
    background-color: var(--color-8);
    border: var(--color-1) 1px solid;
    color: var(--color-1);
}

.btn-light:hover {
    background-color: var(--color-6);
    border: var(--color-9) 1px solid;
    color: var(--color-1);
}

.btn-check {
    display: none;
}

.alert a{
    font-weight: 600;
    font-style: italic;
    color:white;
    text-decoration: none;
}