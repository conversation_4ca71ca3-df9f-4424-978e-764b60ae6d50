body {
    color: #ffffff;
    background-color: black;
}

.background-image {
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background: url('/global/img/landing3/bg-main-5.png') no-repeat center top fixed;
    animation: zoomin-out 4s infinite;
    -webkit-animation: zoomin-out 4s infinite;
}

@keyframes zoomin-out{
    0%   {background-size:auto 105%;}
    37%  {background-size:auto 135%;}
    40%  {background-size:auto 135%;}
    100% {background-size:auto 105%;}
}

@keyframes fadein {
    from {opacity: 0%;}
    to {opacity: 100%;}
}
@-webkit-keyframes zoomin-out{
    0%   {background-size:auto 105%;}
    37%  {background-size:auto 135%;}
    40%  {background-size:auto 135%;}
    100% {background-size:auto 105%;}
}

@keyframes zoomin-out-2{
    0% {transform: scale(1.1);}
    45% {transform: scale(1.5);}
    55% {transform: scale(1.5);}
    100% {transform: scale(1.1);}
}
@-webkit-keyframes zoomin-out-2{
    0% {transform: scale(1.1);}
    45% {transform: scale(1.5);}
    55% {transform: scale(1.5);}
    100% {transform: scale(1.1);}
}

a {
    color: #ea1d5d;
    text-decoration: underline;
}

.field-title {
    font-size: 20px;
}

.step-title {
    margin-bottom: 30px;
}

.step-button {
    margin-top:20px;
}

.main-title {
    font-size: 2em;
    font-weight: 700;
}

.sub-title {
    font-size: 1.5em;
    font-weight: 300;
}

label.btn-lg,
.btn-lg {
    width: 100%;
    font-weight: bold;
    font-size: 28px;
}

.btn {
    border: none;
}

.btn-secondary {
    color: white;
    background: #444;
    background: -moz-linear-gradient(bottom,#444 0%,#888 100%);
    background: -webkit-linear-gradient(bottom,#444 0%,#888 100%);
    background: -o-linear-gradient(bottom,#444 0%,#888 100%);
    background: -ms-linear-gradient(bottom,#444 0%,#888 100%);
    background: linear-gradient(to top,#444 0%,#888 100%);
}

.btn-primary {
    background: -moz-linear-gradient(top,#27861a 0%,#27861a 100%);
    background: -webkit-linear-gradient(top,#27861a 0%,#27861a 100%);
    background: -o-linear-gradient(top,#27861a 0%,#27861a 100%);
    background: -ms-linear-gradient(top,#27861a 0%,#27861a 100%);
    background: linear-gradient(to bottom,#27861a 0%,#27861a 100%);
    background-image: linear-gradient(to bottom, #27861a, #27861a);
}

.btn-light {
    color: white;
    background-color: #afafaf;
}

.btn-check:checked+.btn-light {
    background-color: #ea1d5d;
}

.btn-light:hover,
.btn-light:hover:active {
    background-color: #ea1d5d;
}

.button-next,
#gSignInWrapper
{
    -webkit-animation-name: fadeInUp;
    animation-name: fadeInUp;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
}

button.button-continue {
    width: 315px;
    font-size: 20px;
    padding: 15px 0;
}

@-webkit-keyframes fadeInUp {
    0% {
        opacity:0;
        -webkit-transform:translateY(20px);
        transform:translateY(20px)
    }
    100% {
        opacity:1;
        -webkit-transform:translateY(0);
        transform:translateY(0)
    }
}

@keyframes fadeInUp {
    0% {
        opacity:0;
        -webkit-transform:translateY(20px);
        -ms-transform:translateY(20px);
        transform:translateY(20px)
    }
    100% {
        opacity:1;
        -webkit-transform:translateY(0);
        -ms-transform:translateY(0);
        transform:translateY(0)
    }
}

@media (max-width:900px) {
    .step-terms .step-title,
    .step-email .step-title {
        margin-bottom: 10px;
    }
}