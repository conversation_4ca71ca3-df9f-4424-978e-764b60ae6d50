.container{
    padding-top:0px;
    font-family: <PERSON><PERSON>,sans-serif;
}

.background-image{
    position: fixed;
    top:0;
    bottom:0;
    left:0;
    right:0;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
}

.background-image::after{
    content: "";
    display: block;
    background-color: rgba(38,44,58,.6);
    left:0;
    right:0;
    top:0;
    bottom:0;
    position: fixed;
}

.landing-container {
    position:relative;
}

.landing-content {
    background-color: rgba(25,34,51,.8);
    padding:10px;
    color:white;
    margin-bottom: 0px;
    border-radius: 1.1rem;
}

.avatar{
    width:70px;
    border:2px solid white;
    border-radius: 50%;
}

.online-dot{
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background-color: yellowgreen;
    position:absolute;
    bottom:8px;
    right:4px;
    border: 1px white solid;
}

.text-box{
    text-align: left;
    padding: 10px;
    animation: fade-in 0.6s;
    -webkit-animation: fade-in 0.6s;
    transform-origin:left center;
}

@keyframes fade-in{
    0% {transform: scale(0);}
    100% {transform: scale(1);}
}
@-webkit-keyframes fade-in{
    0% {transform: scale(0);}
    100% {transform: scale(1);}
}

.text-box .main-title{
    background: #e66419;
    border-radius: 2.3rem 2.3rem 2.3rem .5rem;
    display: inline-block;
    font-size: 1.1rem;
    padding: 1.2rem 1.6rem;
    font-weight: 500;
    margin-bottom: 1.2rem;
}

.image-button{
    position: relative;
    font-size: 1.6rem;
    cursor:pointer;
    text-align: center;
    font-weight: bold;
    text-shadow: rgba(25,34,51,.8) 1px 1px;
}

.image-button img{
    border-radius: 12px;
    width:100%;
    margin-top:10px;
}

.image-button div{
    position:absolute;
    bottom:0px;
    text-align: center;
    width: 100%;    
}

.progress-bar{
    background-color: #85c121;
    color: white;
}

.progress{
    margin:10px;
    height: 10px;
}

.progress-bar-percentage{
    display:none;
}

.btn-primary{
    border-radius: 50px;
    background-color: #85c121!important;
    border-width:0px;
    text-transform: uppercase;
    font-size: 1.0em;
    font-weight: bold;
}

.button-next:hover {
    -webkit-filter: brightness(1.15)!important;
    filter: brightness(1.15)!important;
}

.btn i{
    display:none;
}

.summary{
    text-align: left;
}

.form-check-label{
    cursor:pointer;
}

.show-first{
    animation: show-first 6s;
    -webkit-animation: show-first 6s;
    animation-fill-mode: forwards;
}

.show-last{
    animation: show-last 6s;
    -webkit-animation: show-last 6s;
    animation-fill-mode: forwards;
}

@keyframes show-first{
    0% {
        opacity : 1;
    }
    50% {
        opacity : 1;
    }
    51% {
        opacity : 0;
        height : 0;
    }
    100% {
        opacity : 0;
        height : 0;
    }
}

@keyframes show-last{
    0% {
        opacity : 0;
        height : 0;
    }
    50% {
        opacity : 0;
        height : 0;
    }
    51% {
        opacity : 1;
        height : auto;
    }
    100% {
        opacity : 1;
        height : auto;
    }
}

.blinking{
    animation:blinking 1s infinite;
    -webkit-animation:blinking 1s infinite;
}

@keyframes blinking{
    0% {
        opacity : 0.2;
    }
    50% {
        opacity : 1;
    }
    100% {
        opacity : 0.2;
    }
}

.alert-danger{
    padding:5px 10px;
    border-radius: 30px;
    color:red;
    background-color: white;
    font-weight: bold;
    margin-bottom: 3px;
}

.step{
    justify-content: center !important;
}

.answer-box{
    animation: show-answer 6s;
    -webkit-animation: show-answer 6s;
    animation-fill-mode: forwards;
}

@keyframes show-answer{
    0% {
        opacity : 0;
        height : 0px;
    }
    70% {
        opacity : 0;
        height : 0px;
    }
    100% {
        opacity : 1;
        height : 100%;
    }
}

/* 
.avatar */