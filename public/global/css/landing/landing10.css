.background-image{
    display: block;
    position: fixed;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom:0px;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
    -moz-box-shadow: inset 0 0 180px 150px rgba(0,0,0,0.9);
    -webkit-box-shadow: inset 0 0 180px 150px rgba(0,0,0,0.9);
    box-shadow: inner 0 0 180px 150px rgba(0,0,0,0.9);
}
.landing-content{
    background-color: rgba(255,255,255,0.8);
    padding: 20px;
    border-width:5px;
    border-color:rgba(0,0,0,0.5);
    border-style:solid;
    border-radius:10px;
}

h3 {
    color: purple;
    font-weight: 600;
    font-size: 20px;
    margin-bottom: 20px;
}

.landing-content h3 {
    display: none;
}

.step1 .landing-content h3 {
    display: block;
}

.logo-spacing {
    height: 30px;
}

.main-title {
    margin-bottom: 3rem!important;
    font-size: 2.3rem;
}

.btn-lg, .btn-lg:hover, .btn-lg:hover:active {
    padding: 8px 16px;
    text-transform: uppercase;
    font-size: 1.2rem;
    font-weight: 700;
}

.btn-primary{
    background-color: purple!important;
    border-width:0px;
    border-width:2px;
    border-color:purple!important;
    border-style:solid;
    border-radius: 8px;
}
.btn-secondary{
    color: grey;
    background-color:white;
    border-width:2px;
    border-color:rgba(0,0,0,0.5);
    border-style:solid;
    border-radius: 8px;
}
.progress{
    position:fixed;
    top:0px;
    left:0px;
    right:0px;
}
.field-title{
    text-align: left;
    color: purple;
    font-size: 18px;
    font-weight: bold;
}
.landing-logo{

}
.btn-light{
    border-color:rgb(182, 182, 182);
    border-style:solid;
    border-width:1px;
}
.btn-light:hover, .btn-light:hover:active {
    background-color: purple;
    color: #ffffff;
}
h1{
    margin-bottom:30px;
}
.button-countdown{
    display:none;
}
.landing-container{
    padding:10px;
}
@media screen and (max-width: 800px) {
    .background-image{
        -moz-box-shadow: inset 0 0 130px 20px rgba(0,0,0,0.9);
        -webkit-box-shadow: inset 0 0 130px 20px rgba(0,0,0,0.9);
        box-shadow: inner 0 0 130px 20px rgba(0,0,0,0.9);
    }
}

.step-google .btn-secondary{
    color: #5a5a5a!important;
    background-color: #fff!important;
    border-color: #5a5a5a!important;
    background-image: linear-gradient(to bottom, #fff, #fff)!important;
}
.results-box-new-list-item i,
.results-box-new-list-item>span,
.results-box-new-subtitle-2 {
    color: purple;
}
