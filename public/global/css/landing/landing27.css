body {
    color: #474747;
    background: linear-gradient(to bottom left, #ffe2ef 0%, #ffe2ef 50%, #e79dc1 100%);
    background-attachment:fixed;
}

a {
    color: linear-gradient(to bottom, #e97a7a, #e97a7a);
    text-decoration: underline;
}

.field-title {
    font-size: 20px;
}

.step {
    background-color: rgba(0,0,0,0.25);
    padding: 20px 10px;
    border-radius: 10px;
    animation: moveUp 1.0s;
    -webkit-animation: moveUp 1.0s;
    color:white;
}

form{
    margin-top:30px;
}

.step input[type="checkbox"] {
    width: auto;
}

.step-title {
    margin-bottom: 30px;
}

.step-button {
    margin-top:20px;
}

label.btn,
button.btn {
    width: 100%;
    font-weight: bold;
}

.btn-lg {
    font-size: 28px;
}

.btn {
    border: none;
}

.btn-lg {
    padding: 15px 25px;
}

.btn-secondary {
    color: white;
    background:#757575;
}

.btn-secondary:hover {
    color: #fff;
    background-color: #5a5a5a;
}

.btn-primary {
    background-image: linear-gradient(to bottom, #e97a7a, #e97a7a);
}

.btn-primary:hover {
    background-image: linear-gradient(to bottom, #f79b9b, #f79b9b);
}

.btn-light {
    color: #868686;
    background-color: #fff;
}

.btn-check:checked+.btn-light {
    background-image: linear-gradient(to bottom, #e97a7a, #e97a7a);
    color: #ffffff;
}

.btn-light:hover,
.btn-light:hover:active {
    background-image: linear-gradient(to bottom, #e97a7a, #e97a7a);
    color: #ffffff;
}

.button-next,
#gSignInWrapper
{
    -webkit-animation-name: fadeInUp;
    animation-name: fadeInUp;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
}

@-webkit-keyframes fadeInUp {
    0% {
        opacity:0;
        -webkit-transform:translateY(20px);
        transform:translateY(20px)
    }
    100% {
        opacity:1;
        -webkit-transform:translateY(0);
        transform:translateY(0)
    }
}

@keyframes fadeInUp {
    0% {
        opacity:0;
        -webkit-transform:translateY(20px);
        -ms-transform:translateY(20px);
        transform:translateY(20px)
    }
    100% {
        opacity:1;
        -webkit-transform:translateY(0);
        -ms-transform:translateY(0);
        transform:translateY(0)
    }
}

button .fa-arrow-right{
    animation:animateArrow 2s infinite;
	-webkit-animation:animateArrow 2s infinite
}

@keyframes animateArrow{
    0% {opacity: 60%;}
    50% {opacity: 100%;}
    100% {opacity: 60%;}
}

button.button-continue {
    width: 315px;
    font-size: 20px;
    padding: 15px 0;
}

.progress{
    background-color: rgba(255,255,255,0.7);
    position: fixed;
    top:0;
    left:0;
    right:0;
}

.background-images{
    position:fixed;
}

.landing-logo{
    display:none;
}

.sub-title{
    font-size: 1.3em;
}

.button-countdown{
    display:none;
}

@media (max-width:900px) {
    .title-moms {
        font-size: 1.5em;
    }

    .step-terms .step-title,
    .step-email .step-title {
        margin-bottom: 10px;
    }
}

.title-moms{
    color: #e97a7a;
    font-weight: bold;
    text-shadow: 2px 2px 2px rgba(255, 255, 255, 0.9);
}