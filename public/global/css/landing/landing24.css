:root {
    --locations-list-height: 192px;
}
textarea,
button,
input.text,
input[type="text"],
input[type="button"],
input[type="submit"],
.input-checkbox {
    -webkit-appearance: none;
    border-radius: 0;
}
select, label{
    cursor:pointer;
}

.background-video{
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
    background-color:white;
    -moz-box-shadow: inset 0 0 80px 30px rgba(0,0,0,1);
    -webkit-box-shadow: inset 0 0 80px 30px rgba(0,0,0,1);
    box-shadow: inner 0 0 80px 30px rgba(0,0,0,1);
    z-index:-1;
}

.background-video video{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}

#background-video-desktop{
    display:block;
}

#background-video-mobile{
    display:none;
}

.video-blur{
    -webkit-filter: blur(15px);
    -moz-filter: blur(15px);
    -o-filter: blur(15px);
    -ms-filter: blur(15px);
    filter: blur(15px);
    -webkit-transition: 1s -webkit-filter linear;
    -o-transition: 1s -o-filter linear;
    transition: 1s filter linear;
}

.website-container{
    display:block;
    position:relative;
    margin-left:auto;
    margin-right:auto;
    width:100%;
    max-width:700px;
    text-align:center;
    color:white;
    padding:30px 20px 10px 20px;
    z-index:10;
    text-shadow: 2px 2px 1px rgba(14, 13, 13, 0.7);
}

.website-title{
    display:block;
    position:relative;
    width:100%;
    margin-bottom:0px;
}

.website-title img{
    display:block;
    position:relative;
    width:100%;
    max-width:400px;
    margin-left:auto;
    margin-right:auto;
}

.website-text-1{
    display:block;
    position:relative;
    width:100%;
    font-size:30px;
    border-top-width: 2px;
    border-top-style: dotted;
    border-top-color: white;
    margin-top: 75px;
    padding-top: 10px;
    padding-bottom: 20px;
}

.website-text-1 span{
    font-weight:650;
    color:#f2e7ac;
}

.website-text-terms{
    display:block;
    position:relative;
    width:100%;
    font-size:30px;
    text-decoration:underline;
}
.website-answer{
    display:block;
    position:relative;
    width:100%;
    margin-top:30px;
}
.button-type-1{
    display:inline-block;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3) inset;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    border-style:solid;
    border-width:1px;
    padding:15px 35px 15px 25px;
    border-radius:5px;
    font-size:28px;
    cursor:pointer;
    font-weight:700;
    margin-top: 10px;
    -webkit-transition-duration:.2s;
    transition-duration:.2s;
    color:#fff;
    background-color:#e1712e;
    border-color:#e1712e;
}
.button-small-text{
    font-size:26px;
}
.step{
    animation-name: fadein;
    animation-duration: 1.0s;
}
.step_0 .button-type-1 i{
    animation: move-arrow 2.5s infinite;
    -webkit-animation: move-arrow 2.5s infinite;
}
.step_1 .button-type-1 i{
    animation: move-arrow 2.5s infinite;
    -webkit-animation: move-arrow 2.5s infinite;
}
.step_2 .button-type-1 i{
    animation: move-arrow 2.0s infinite;
    -webkit-animation: move-arrow 2.0s infinite;
}

.step_3 .button-type-1 i{
    animation: move-arrow 2.0s infinite;
    -webkit-animation: move-arrow 2.0s infinite;
}
.step_5 .button-type-1 i{
    animation: move-arrow 1.5s infinite;
    -webkit-animation: move-arrow 1.5s infinite;
}
.step_6 .button-type-1 i{
    animation: move-arrow 1.5s infinite;
    -webkit-animation: move-arrow 1.5s infinite;
}
.step_7 .button-type-1 i{
    animation: move-arrow 1.5s infinite;
    -webkit-animation: move-arrow 1.5s infinite;
}
.step_8 .button-type-1 i{
    animation: move-arrow 0.8s infinite;
    -webkit-animation: move-arrow 0.8s infinite;
}
.step_9 .button-type-1 i{
    animation: move-arrow 0.8s infinite;
    -webkit-animation: move-arrow 0.8s infinite;
}

.step_10 .button-type-1 i{
    animation: move-arrow 0.8s infinite;
    -webkit-animation: move-arrow 0.8s infinite;
}

.step-looking-for{
    display:block;
    width:100%;
    margin-top:10px;
}
.step-looking-for-left{
    display:block;
    width:50%;
    padding:10px;
    float:left;
}
.step-looking-for-right{
    display:block;
    width:50%;
    padding:10px;
    float:right;
}
.step-looking-for-left > span, .step-looking-for-right > span{
    display:block;
    position:relative;
    margin-bottom:6px;
    font-size:24px;
    font-weight:600;
}
.step-looking-for-left span, .step-looking-for-right span{
    width:100%;
}
.step-looking-for-left .btn-primary{
    padding:15px 35px 15px 25px;
    font-size:28px;
    cursor:pointer;
    margin-top: 10px;
    background-color:#00000047;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3) inset;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    color:white;
    border-color:white;
    border-style:solid;
    border-width:1px;
    border-radius:5px;
    font-weight:700;
    -webkit-transition-duration:.2s;
    transition-duration:.2s;
}
.step-looking-for-right .btn-primary{
    padding:15px 35px 15px 25px;
    font-size:28px;
    cursor:pointer;
    margin-top: 10px;
    background-color:#00000047;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3) inset;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    color:#ffa0b2;
    border-color:#ffa0b2;
    border-style:solid;
    border-width:1px;
    border-radius:5px;
    font-weight:700;
    -webkit-transition-duration:.2s;
    transition-duration:.2s;
}
.input-1 {
    display: block;
    position: relative;
    width: 100%;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 20px;
    margin-bottom: 0px;
}
.input-1 input {
    display:block;
    position:relative;
    text-align: center;
    width: 100%;
    padding: 10px;
    font-size:20px;
    border-color: grey;
    border-style: solid;
    border-width: thin;
    margin-bottom: 2px;
    border-radius:0.25rem;
    background-color: rgba(255,255,255,0.7);
}

.form-control {
    background-color: rgba(255,255,255,0.7);
}

.age-selector{
    display: block;
    position: relative;
    width: 100%;
    margin-left:auto;
    margin-right:auto;
    margin-top: 10px;
    padding:0px;
    margin-bottom:10px;
}
.age-selector > div > select{
    width: calc(33.33333% - 12px);
    margin-right: 18px;
    float: left;
    padding: 10px;
    border-radius:0.25rem;
    border-width: thin;
    border-color: grey;
    border-style: solid;
    background-color: rgba(255,255,255,0.7);
}
.age-selector > div:last-of-type > select {
    margin-right: 0px!important;
}
.alert-danger {
    background-color: #f8d7daba;
    text-shadow:none;
}

.terms_and_conditions{
    display: block;
    position: relative;
    margin-left: auto;
    margin-right: auto;
    padding: 10px;
    font-size: 11px;
    margin-top: 5px;
    font-weight: normal;
    width: 100%;
    margin-bottom: 5px;
    text-align:center;
    color:white;
    text-shadow: 1px 1px 1px rgba(14, 13, 13, 0.7);
}
.terms_and_conditions a{
    color:#ffa0b2;
    text-decoration:underline;
}
#results-container {
    background-color: rgba(255,255,255,0.8);
    color: #2b2b2b;
    overflow-y: auto;
}
#results-container ul.results {
    list-style: none;
    text-align: left;
    padding: 0;
    margin: 0;
    text-shadow:none;
}
#results-container ul.results li {
    padding: 0.5rem;
    cursor: pointer;
    border-bottom-color:grey;
    border-bottom-style:dotted;
    border-bottom-width:1px;
}

#results-container ul.results li:last-of-type {
    border-bottom-width:0px;
}

#results-container ul.results li:hover {
    background: white;
}
.error-title{
    width:100%;
    position:relative;
    padding-top:3px;
    padding-right:10px;
    padding-left:10px;
    font-size:20px;
    color: #ffa0b2;
    text-align:left;
    font-weight:600;
}

.alert{
    animation-name: fadein;
    animation-duration: 1s;
}

.instructions{
    display:block;
    position:relative;
    width:100%;
    padding-bottom:80px;
    padding-top:20px;
}

.avatar{
    display:block;
    position:relative;
    float:left;
    width:110px;
    height:110px;
}

.avatar > img{
    display:block;
    position:absolute;
    border-color:white;
    border-width:1px;
    border-style:solid;
    width:110px;
    border-radius:50%;
    height:110px;
    box-shadow: 2px 2px rgba(0,0,0,0.2);
}

.title-guide{
    text-align:left;
    padding:0px 8px 4px 8px;
    width:calc(100% - 110px);
    position:relative;
    float:left;
    font-weight:bold;
}

.instructions-animated{
    display:block;
    position:relative;
    float:left;
    width:calc(100% - 120px);
    height:85px;
    margin-top:0px;
}

.instructions-animated > div{
    display:block;
    position:absolute;
    top:0px;
    left:0px;
    width:100%;
    text-align:left;
    font-size:20px;
    font-weight:600;
    padding:8px;
    border-top-width:2px;
    border-top-style:dotted;
    border-top-color: white;
    border-radius:5px;
    color: white;
    font-style:italic;
}

.instructions-animated > div > span{
    font-weight:bold;
    text-decoration:underline;
    font-size:22px;
    color:#aadadf;
    font-style:normal;
}

.countdown{
    font-size:20px;
    font-weight:600;
}

@media (min-aspect-ratio: 16/9) {
    .background-video video {
        width: 100%;
        height: auto;
        height: 300%;
        top: -100%;
    }
}
@media (max-aspect-ratio: 16/9) {
    .background-video video {
        width: auto;
        height: 100%;
        width: 300%;
        left: -100%;
    }
}
@media only screen and (orientation: portrait) {
    #background-video-desktop{
        display:none;
    }
    #background-video-mobile{
        display:block;
    }
}



@-webkit-keyframes move-arrow {
    0% {
        -webkit-transform:translateX(0px);
        transform:translateX(0px)
    }
    50% {
        -webkit-transform:translateX(20px);
        transform:translateX(20px)
    }
    100% {
        -webkit-transform:translateX(0px);
        transform:translateX(0px)
    }
}
@keyframes move-arrow {
    0% {
        -webkit-transform:translateX(0px);
        -ms-transform:translateX(0px);
        transform:translateX(0px)
    }
    50% {
        -webkit-transform:translateX(20px);
        -ms-transform:translateX(20px);
        transform:translateX(20px)
    }
    100% {
        -webkit-transform:translateX(0px);
        -ms-transform:translateX(0px);
        transform:translateX(0px)
    }
}

@keyframes fadein {
    from {opacity: 0%;}
    to {opacity: 100%;}
}

@media (max-width:900px) {
    .age-selector > div > select{
        width: calc(33.33333% - 4px);
        margin-right: 6px;
    }
    .website-text-1{
        line-height:40px;
    }
    .background-cover{
        -moz-box-shadow: inset 0 0 110px 10px rgba(0,0,0,0.7)!important;
        -webkit-box-shadow: inset 0 0 110px 10px rgba(0,0,0,0.7)!important;
        box-shadow: inner 0 0 110px 10px rgba(0,0,0,0.7)!important;
        opacity: 0.7!important;
    }
}

.background-cover{
    display: block;
    position: fixed;
    width: 100%;
    height: 100%;
    top:0px;
    left:0px;
    background-repeat: repeat;
    background-color: rgba(0, 0, 0, 0.08);
    -moz-box-shadow: inset 0 0 80px 10px rgba(0,0,0,0.9);
    -webkit-box-shadow: inset 0 0 80px 10px rgba(0,0,0,0.9);
    box-shadow: inner 0 0 80px 10px rgba(0,0,0,0.9);
    opacity: 0.50;
}
.step-looking-for-left > label, .step-looking-for-right > label{
    display:block;
    position:relative;
    margin-bottom:6px;
    color:#ffbad5;
    font-size:20px;
}
.step-looking-for-left label, .step-looking-for-right label{
    width:100%;
}
.check-with-label{
    display:none;
}
.interest-box label{
    display:inline-block;
    background-color: #afafaf;
    padding:8px;
    border-radius: 6px;
    margin:3px;
    font-size:15px;
    font-weight:bold;
    color:white;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor:pointer;
}
.check-with-label:checked + .label-for-checking  {
    background-color: #ea1d5d;
}
.terms_and_conditions {
    font-size:12px!important;
    cursor:pointer;
}
.info-additional{
    display:block;
    position:relative;
    width:100%;
    font-size:18px;
    padding:10px;
    text-align:center;
    font-weight:bold;
    line-height: 24px;
}
.info-additional ul{
    list-style-type: none;
    margin-left:0px;
    padding-left:0px;
    font-weight:normal;
}
.info-additional ul li i{
    color: green;
}
.answer-box{
    display:block;
    position:relative;
}
.answer-box label{
    display:block;
    position:relative;
    width:calc(50% - 4px);
    float:left;
    margin:2px;
    background-color: #afafaf;
    border-radius:6px;
    cursor:pointer;
    padding:10px;
    color:white;
    font-size:16px;
}
.answer-box label:hover{
    background-color: #ea1d5d;
}

/* new code 1 */
.progress{
    display:block;
    position:fixed;
    z-index:100;
    top:0px;
    left:0px;
    right:0px;
    width:100%;
    background-color:#e9ecefb3;
}
.progress-bar-percentage{
    display:block;
    font-weight:bold;
    font-size:12px;
}

/* button fix */
.step-looking-for-left .btn-primary {
    padding: 15px 35px 15px 25px;
    font-size: 28px;
    cursor: pointer;
    margin-top: 10px;
    background-color: #00000047;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3) inset;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    color: white;
    border-color: white;
    border-style: solid;
    border-width: 1px;
    border-radius: 5px;
    font-weight: 700;
    -webkit-transition-duration: .2s;
    transition-duration: .2s;
}
.step-looking-for-right .btn-primary {
    padding: 15px 35px 15px 25px;
    font-size: 28px;
    cursor: pointer;
    margin-top: 10px;
    background-color: #00000047;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3) inset;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    color: white;
    border-color: white;
    border-style: solid;
    border-width: 1px;
    border-radius: 5px;
    font-weight: 700;
    -webkit-transition-duration: .2s;
    transition-duration: .2s;
}
.step-looking-for-right .btn-primary.preferred-button, .step-looking-for-left .btn-primary.preferred-button{
    padding:15px 35px 15px 25px;
    font-size:28px;
    cursor:pointer;
    margin-top: 10px;
    background-color:#00000047;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3) inset;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    color:#ffa0b2;
    border-color:#ffa0b2;
    border-style:solid;
    border-width:1px;
    border-radius:5px;
    font-weight:700;
    -webkit-transition-duration:.2s;
    transition-duration:.2s;
}
/* orange */
.progress{
    display:block;
    position:fixed;
    z-index:1000;
    top:0px;
    left:0px;
    right:0px;
    width:100%;
}
.progress-bar-percentage{
    display:block;
    font-weight:bold;
    font-size:12px;
}
.step-looking-for-right .btn-primary.preferred-button, .step-looking-for-left .btn-primary.preferred-button{
    color:#fff;
    background-color:#e1712e!important;
    border-color:#e1712e!important;
}
.answer-box label:hover{
    background-color: #e1712e!important;
}
.ui-menu .ui-menu-item-wrapper {
    border-bottom: 1px solid #dddddd!important;
    padding: 10px 15px;
}

/* css for results page - begin */
.results-box-new{
    margin:0px auto 20px;
    text-align:center;
}
.result-box-new-content{
    padding:10px;
}
.results-box-new-title{
    display:block;
    position:relative;
    font-size:30px;
    margin-bottom:20px;
    font-weight:bold;
}
.results-box-new-subtitle-1{
    display:block;
    position:relative;
    font-size:20px;
    margin-bottom:20px;
    animation: blinking 1.5s infinite!important;
    -webkit-animation: blinking 1.5s infinite!important;
    font-weight:bold;
}
.results-box-new-list-item{
    font-size:20px;
    animation: fadein 1.2s;
    -webkit-animation: fadein 1.2s;
}
.results-box-new-list-item i{
    color: #ff5400;
}
.results-box-new-list-item > span{
    color: #ff5400;
    font-weight: bold;
    text-decoration:underline;
    font-style:italic;
    animation: blinking 1.5s infinite!important;
    -webkit-animation: blinking 1.5s infinite!important;
}
.results-box-new-subtitle-2{
    display:block;
    position:relative;
    font-weight:bold;
    font-size:25px;
    line-height:20px;
    margin-bottom:35px;
    color: #ff5400;
    animation: blinking 1.5s infinite!important;
    -webkit-animation: blinking 1.5s infinite!important;
}
.results-box-new-subtitle-2b{
    display:block;
    position:relative;
    font-size:24px;
    margin-bottom:20px;
    line-height:20px;
}
.results-box-new-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
}
.results-box-new-list li{
    font-size:18px;
    margin-bottom:14px;
    font-style:italic;
    line-height:17px;
    font-weight:bold;
}
.radar{
    display:block;
    position:relative;
    width:120px;
    height:120px;
    background-color:rgba(0,0,0,0.2);
    margin-left:auto;
    margin-right:auto;
    border-radius:50%;
    border-color:rgba(255,255,255,0.4);
    border-style:solid;
    border-width:2px;
    margin-bottom:10px;
}
.radar .line{
    display:block;
    position:absolute;
    width:4px;
    height:50%;
    top:50%;
    left:calc(50% - 2px);
    background-color:rgba(255,255,255,0.9);
    -webkit-animation:spin 4s linear infinite;
    -moz-animation:spin 4s linear infinite;
    animation:spin 4s linear infinite;
    transform-origin: top center;
}
.radar .cross1{
    display:block;
    position:absolute;
    width:2px;
    height:100%;
    top:0%;
    left:calc(50% - 1px);
    background-color:rgba(255,255,255,0.4);
}
.radar .cross2{
    display:block;
    position:absolute;
    height:2px;
    width:100%;
    left:0%;
    top:calc(50% - 1px);
    background-color:rgba(255,255,255,0.4);
}
.radar .circle1{
    display:block;
    position:absolute;
    height:66%;
    width:66%;
    left:calc(50% - 33%);
    top:calc(50% - 33%);
    transform-origin: top center;
    border-color:rgba(255,255,255,0.4);
    border-style:solid;
    border-width:2px;
    border-radius:50%;
}
.radar .circle2{
    display:block;
    position:absolute;
    height:33%;
    width:33%;
    left:calc(50% - 16.5%);
    top:calc(50% - 16.5%);
    transform-origin: top center;
    border-color:rgba(255,255,255,0.4);
    border-style:solid;
    border-width:2px;
    border-radius:50%;
}
.hide{
    display:none;
    animation: fadein 1.2s;
    -webkit-animation: fadein 1.2s;
}
@-moz-keyframes spin { 100% { -moz-transform: rotate(360deg); } }
@-webkit-keyframes spin { 100% { -webkit-transform: rotate(360deg); } }
@keyframes  spin { 100% { -webkit-transform: rotate(360deg); transform:rotate(360deg); } }

.website-title {
    display:none!important;
}

.or {
    font-size: 18px;
}

a.nextbutton {
    font-size: 24px;
}
