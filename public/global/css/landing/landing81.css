body {
    color: #492c49;
    background-color: #e0dfe5;
}

a {
    color: purple;
    text-decoration: underline;
}

.field-title {
    font-size: 20px;
}

.landing-content {
    margin-bottom: 100px;
    max-width: 650px;
}

.step input[type="checkbox"] {
    width: auto;
}

.step-title {
    margin-bottom: 30px;
}

.step-button {
    margin-top:20px;
}

/* Buttons */
label.btn,
button.btn {
    width: 100%;
    font-weight: bold;
}

.btn-lg {
    font-size: 28px;
    padding: 15px 25px;
}

.btn {
    border: none;
}

.btn-secondary {
    color: white;
    background: #444;
    background: -moz-linear-gradient(bottom,#444 0%,#888 100%);
    background: -webkit-linear-gradient(bottom,#444 0%,#888 100%);
    background: -o-linear-gradient(bottom,#444 0%,#888 100%);
    background: -ms-linear-gradient(bottom,#444 0%,#888 100%);
    background: linear-gradient(to top,#444 0%,#888 100%);
}

.btn-primary {
    background: -moz-linear-gradient(top,#27861a 0%,#27861a 100%);
    background: -webkit-linear-gradient(top,#27861a 0%,#27861a 100%);
    background: -o-linear-gradient(top,#27861a 0%,#27861a 100%);
    background: -ms-linear-gradient(top,#27861a 0%,#27861a 100%);
    background: linear-gradient(to bottom,#27861a 0%,#27861a 100%);
    background-image: linear-gradient(to bottom, #27861a, #27861a);
}

.btn-light {
    color: white;
    background-color: #afafaf;
}

.btn-check:checked+.btn-light {
    background-color: #ea1d5d;
    color: #ffffff;
}

.btn-light:hover,
.btn-light:hover:active {
    background-color: #ea1d5d;
    color: #ffffff;
}

.button-next,
#gSignInWrapper
{
    -webkit-animation-name: fadeInUp;
    animation-name: fadeInUp;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
}

@-webkit-keyframes fadeInUp {
    0% {
        opacity:0;
        -webkit-transform:translateY(20px);
        transform:translateY(20px)
    }
    100% {
        opacity:1;
        -webkit-transform:translateY(0);
        transform:translateY(0)
    }
}

@keyframes fadeInUp {
    0% {
        opacity:0;
        -webkit-transform:translateY(20px);
        -ms-transform:translateY(20px);
        transform:translateY(20px)
    }
    100% {
        opacity:1;
        -webkit-transform:translateY(0);
        -ms-transform:translateY(0);
        transform:translateY(0)
    }
}

button .fa-arrow-right{
    animation:animateArrow 2s infinite;
	-webkit-animation:animateArrow 2s infinite
}

@keyframes animateArrow{
    0% {opacity: 60%;}
    50% {opacity: 100%;}
    100% {opacity: 60%;}
}

button.button-continue {
    width: 315px;
    font-size: 20px;
    padding: 15px 0;
}

.terms .main-title {
    font-size: 1.2em;
}

