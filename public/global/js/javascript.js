$(function() {
	lightBox(); 
	smallGallery(); 
	typeAhead(); 
	flirts();
	nav();
	getStatus();
    $(".flirtPop").popover({
        html : true, 
        content: function() {
          return $(this).next().html();
        }
    });
	$('.flirtPop').on('shown.bs.popover', function () {
	  flirts();
	})    
});

$(document).on('click', 'div.register button.register', function(){
	$(this).html('Loading...');
	$(this).attr('disabled', true);
	$('div.register form').submit();
});

function getStatus()
{
	var status = $('td#status');
	var url = status.attr('data-href');

	if(status.length > 0)  
	{

		$.ajax({
			type: "GET",
			url: url,
			success: function(data) {
				if(data) {
					$('td#status').html(data);
				}
			}
		});
	}
}

function nav() 
{
	$('button.navbar-toggle').click(function(){
		var status = $(this).attr('data-status');
		if(status == 'closed') {
			$(this).addClass('active');
			$(this).attr('data-status', 'open');
			$('.nav').css({'left' : 0});
			$('body').append('<div class="white-overlay"></div>');
			$('div#c-wrap').css({'left' : '80%'});
			$('body').css({'position' : 'fixed'});
		} else {
			$(this).removeClass('active');
			$('div.white-overlay').remove();
			$(this).attr('data-status', 'closed');
			$('.nav').css({'left' : '-80%'});
			$('div#c-wrap').css({'left' : 0});
			$('body').css({'position' : 'relative'});
		}
	});
}

function flirts()
{
	$(document).on('click', 'a.flirt', function(e){
		e.stopImmediatePropagation();
		e.preventDefault();
		var checkbox = $(this).next();
		checkbox.prop('checked', true);
		$('form#addFlirt').submit();
	});
}

// Lightbox
function lightBox() 
{ 
	$('a#profile-image').click(function(e){
		e.preventDefault();
		$('div#popup-wrap').fadeIn();
		$('div.popup h3.title a.close, div.overlay').click(function(e){
			e.preventDefault();
			$('div#popup-wrap').hide();
		});
	});

}

// Small gallery
function smallGallery() 
{
	$('ul.more-images li a').click(function(e){
		e.preventDefault();
		if($(this).attr('href')){
			document.location.href=$(this).attr('href');
		}
		if($(this).attr('data-large'))
		{
			$('div.private').hide();
			$('ul.more-images li').find('a').attr('data-status', '');
			$(this).attr('data-status', 'open');
			var img = $(this).attr('data-large');
			if(img !== $('a#profile-image img').attr('src') && img !== 'false')
			{
				$('a#profile-image img').fadeOut(function(){
					$('a#profile-image img').attr('src', img);
				});
				$('a#profile-image img').load(function(){
				$('a#profile-image img').fadeIn();
					$('div.pr-img div.thumb').find('div.private').html($('div.side').find('div.private').html());
					$('div.popup.image img.profile-image').attr('src', img);
				});
			}
		}
		if($(this).attr('class') == 'private') {
			$('div.pr-img div.thumb').find('div.private').html($('div.side').find('div.private').html());
			$('div.private').show();
		} 
	});
}


// Autocomplete search field
function typeAhead() 
{ 
	$("input.typeahead").typeahead({
	    onSelect: function(item) {
	        window.location.href = '/profile/' + item.text;
	    },
	    ajax: {
	        url: "/search",
	        timeout: 100,
	        displayField: "username",
	        triggerLength: 2,
	        method: "get"
	    }
	});
}
