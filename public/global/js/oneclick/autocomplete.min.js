$(function(){$("#geoAutocomplete").length>0&&($("#geoAutocomplete").autocomplete({minLength:2,source:function(t,e){$.ajax({url:"/geoAutocomplete",dataType:"json",data:{term:t.term,country_id:$("#country_id").val(),use_region:$("#use_region_hidden").val()},success:function(t){e(t)}})},focus:function(t,e){return $("#geoAutocomplete").val(e.item.city),!1},select:function(t,e){if($("#geoAutocomplete").val(e.item.city),$("#locationInput").val(e.item.country+"-"+e.item.region+"-"+e.item.city),$("input[name='locationRegion']").length>0&&$("input[name='locationRegion']").val(e.item.region),$("[data-next]").length>0){var n=$(this).closest("[data-step]");$("[data-next]",n).show()}return $(".step4").length>0&&$(".step5").length>0&&($(".step4").css("display","none"),$(".step5").css("display","block")),!1}}).autocomplete("instance")._renderItem=function(t,e){return $("<li>").append("<div>"+e.city+"<br><small><i>"+e.region+"</i></small></div>").appendTo(t)});const t=$("#popup-location #results-container"),e=$("#popup-location ul.results");function n(o){if(0==o.length)return void t.animate({height:0},150);$("#results-container .results").empty(),o.forEach(function(t){const e=`\n        <li data-value="${t.country}-${t.region}-${t.city}">\n            <span class="city">${t.city}</span>\n            <br>\n            <small>${t.region}, ${t.country}</small>\n        </li>\n        `;$("#results-container .results")[0].innerHTML+=e}),$("#results-container .results li").click(function(){$("#geoAutocompleteCustom").val($(this).find(".city").text()),$("#locationInput").val($(this).attr("data-value")),n([])});let i=getComputedStyle(document.documentElement).getPropertyValue("--locations-list-height");i=e.height()>200?`${i.trim()}`:e.height(),t.animate({height:i},150)}if($("#geoAutocompleteCustom").length){let t=null;$("#geoAutocompleteCustom").on("keyup",function(e){const o=$("#geoAutocompleteCustom").val();o.length<=2?n([]):(clearTimeout(t),t=setTimeout(function(){$.ajax({url:"/geoAutocomplete",dataType:"json",data:{term:o},success:function(t){n(t)}})},400))})}});