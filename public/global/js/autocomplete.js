$(function () {
    $(document).ready(function () {

        $("#search").autocomplete({
            minLength: 3,
            source: function (request, response) {
                $.ajax({
                    url: "/search",
                    method: 'post',
                    dataType: "json",
                    data: {
                        search: request.term,
                    },
                    success: function (data) {
                        response(data);
                    }
                });
            },
            select: function (event, ui) {
                window.location.href = ui.item.url;
            }
        }).autocomplete("instance")._renderItem = function (ul, item) {
            return $('<li>').addClass('search-result')
                .append('<div><img src="' + item.image + '"/><span class="profile-text">' + item.username + '<br/><small>' + item.age + ' years old</small></span><div style="clear:both;"></div></div>')
                .appendTo(ul);
            };

        $("#search").on('keyup', function (e) {
            if (e.which == 13 && $('#search').val().length > 0) {
                $('#header-search-form').submit();
            }
        });

        $("#submit-search").on('click', function (e) {
            e.preventDefault();
            if ($('#search').val().length > 0) {
                $('#header-search-form').submit();
            }
        })

        $("#submit-search-mobile").on('click', function (e) {
            e.preventDefault();
            if ($('#search-mobile').val().length > 0) {
                location.href = $('#header-search-form').prop('action') + '?search=' + $('#search-mobile').val();
            }
        });

    });
});