APP_NAME=Laravel
APP_TIMEZONE=UTC
APP_ENV=local
APP_ENV_TAG=GEN2
APP_KEY=
APP_MAIL_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US
APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database
BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_CONNECTION=log
CACHE_STORE=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=cookie
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null
SESSION_SECURE_COOKIE=false

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=1

MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"
MAIL_FASTLANE_HOST=fastlane.enem.nl
MAIL_FASTLANE_PORT=25

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

VERIFY_EMAIL_CLIENT_BASE_URL="https://verify.enem.nl"
VERIFY_EMAIL_CLIENT_API_KEY=

RECAPTCHA_URL="https://www.google.com/recaptcha/api"

PUSHCREW_URL="https://pushcrew.com/api/v1"
PUSHCREW_API_KEY=

APP_LOCATION=UK

MODERATION_API_URL="http://deliver.moderationinterface.com/api/v1/thirdparty"
MODERATION_API_KEY=
MODERATION_API_OPERATOR_ID=4
MODERATION_API_DOMAIN="chattoolapi.com"
MODERATION_API_CHATTER_PASSWORD=
ADMIN_API_KEY=
OPERATORS_BEARER_TOKEN=

WONDERPUSH_ACCESS_TOKEN=
WONDERPUSH_APPLICATION_ID=
WONDERPUSH_WEB_KEY=

CAMPAIGNS_BASE_URI=
CAMPAIGNS_AUTH_TOKEN=
CAMPAIGNS_WEBHOOK_TOKEN=
CAMPAIGNS_WEBHOOK_GEO=
CAMPAIGNS_WEBHOOK_CLUSTER=

USEBOUNCER_KEY=

RECAPTCHA_RESPONSE=true

SENTRY_LARAVEL_DSN=
SENTRY_TRACES_SAMPLE_RATE=0.0
SENTRY_PROFILES_SAMPLE_RATE=0.0
SENTRY_RELEASE=

NODE_VERSION=20.x
PHP_IMAGE_NAME="php:8.2-fpm"
PHP_SUPERVISOR_IMAGE_NAME="php:8.2-cli"
# If you use wsl, you must be fill your user name in local user
DOCKER_LOCAL_USER=
# If you use wsl, you must be fill this path in workdir "home/${DOCKER_LOCAL_USER}", otherwise keep it empty
DOCKER_LOCAL_WORKDIR=
WWWGROUP=1000
WWWUSER=1000

AUTH_PASSWORD_RESET_TOKEN_TABLE=password_resets

MONGODB_DATABASE=platform
MONGODB_DSN=mongodb+srv://<username>:<password>@cluster.server.com/

PUBSUB_PROJECT_ID=
PUBSUB_QUEUE=default
PUBSUB_KEY_FILE_PATH=

MIRZACLES_CELERISPAY_API_TOKEN=

AUTH_PASSWORD_RESET_TOKEN_TABLE=password_resets

PASTEBIN_API_DEV_KEY=
PASTEBIN_API_USER_KEY=
