image: php:7.4-cli


pipelines:
  branches:
    develop:
      - step:
          name: PHP code quality
          script:
            - apt-get update && apt-get install -y unzip git rsync zip libzip-dev libicu-dev
            - docker-php-ext-install zip
            - docker-php-ext-enable zip
            - docker-php-ext-install intl
            - docker-php-ext-enable intl
            - mkdir database/seeds
            - mkdir database/factories
            - mkdir database/migrations
            - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
            - composer install --no-interaction --no-scripts --ignore-platform-reqs --dev
            - ./vendor/bin/php-cs-fixer fix --quiet
            - git add .
            - git commit -m 'Code styling' || true
            - git push || true
          caches:
            - composer
