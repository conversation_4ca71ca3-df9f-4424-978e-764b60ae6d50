image: php:8.2-fpm

definitions:
  docker:
    memory: 2048
  # caches:
  #   vendor-bundler-cache:
  #     key:
  #       files:
  #         - composer.lock
  #     path: vendor/**

pipelines:
  branches:
    develop:
      - step:
          name: Install vendor
          services:
            - docker
          caches:
            - docker
            # - vendor-bundler-cache
          script:
            - /bin/bash ./scripts/install_vendor.sh
          artifacts:
            - vendor/**
      - parallel:
          steps:
            - step:
                name: Code Quality
                services:
                  - docker
                caches:
                  - docker
                  # - vendor-bundler-cache
                script:
                  - apt-get update && apt-get install -y git curl
                  - mkdir database/seeds
                  - mkdir database/factories
                  - mkdir database/migrations
                  - cp .env.example .env
                  - php artisan key:generate
                  - ./vendor/bin/pint -q
                  - git remote set-url origin ${BITBUCKET_GIT_SSH_ORIGIN}
                  - git add -A
                  - git diff-index --quiet HEAD || git commit -m '[skip ci] Code styling'
                  - git push
            - step:
                name: Unit tests
                services:
                  - docker
                caches:
                  - docker
                  # - vendor-bundler-cache
                script:
                  - apt-get update
                  - docker-php-ext-install pdo_mysql
                  - mkdir database/seeds
                  - mkdir database/factories
                  - mkdir database/migrations
                  - cp .env.example .env
                  - vendor/bin/pest --filter=Unit