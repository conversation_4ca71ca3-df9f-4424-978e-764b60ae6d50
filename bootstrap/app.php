<?php

use Google\Cloud\Core\Exception\DeadlineExceededException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Sentry\Laravel\Integration;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders([
        \Sentry\Laravel\ServiceProvider::class,
        \Mirzacles\PubSub\ServiceProvider::class,
        \Mirzacles\Helpers\HelpersServiceProvider::class,
    ])
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        // channels: __DIR__.'/../routes/channels.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->redirectUsersTo('/members');

        $middleware->encryptCookies(except: [
            'wingify_push_subscriber_id',
        ]);
        $middleware->validateCsrfTokens(except: [
            'enem/*',
            'webhooks/*',
            'celerispay/notification',
        ]);

        $middleware->append(\App\Http\Middleware\Domain::class);

        $middleware->web([
            \Illuminate\Session\Middleware\AuthenticateSession::class,
            \App\Http\Middleware\AuthenticateByToken::class,
            \App\Http\Middleware\ViewData::class,
            \App\Http\Middleware\EmailsStatTracking::class,
            \App\Http\Middleware\Robots::class,
        ]);

        $middleware->api(\App\Http\Middleware\Api::class);

        $middleware->replace(\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance::class, \App\Http\Middleware\PreventRequestsDuringMaintenance::class);
        $middleware->replace(\Illuminate\Http\Middleware\TrustProxies::class, \App\Http\Middleware\TrustProxies::class);

        $middleware->alias([
            'activation' => \App\Http\Middleware\Activation::class,
            'admin.auth' => \App\Http\Middleware\AuthenticateAdminApi::class,
            'affiliate' => \App\Http\Middleware\Affiliate::class,
            'auth' => \App\Http\Middleware\Authenticate::class,
            'auth.partner' => \App\Http\Middleware\AuthenticatePartners::class,
            'credit_check' => \App\Http\Middleware\CreditCheck::class,
            'domain' => \App\Http\Middleware\Domain::class,
            'groups' => \App\Http\Middleware\Groups::class,
            'landing' => \App\Http\Middleware\Landing::class,
            'preferences' => \App\Http\Middleware\Preferences::class,
            'premium' => \App\Http\Middleware\Premium::class,
            'push_notification' => \App\Http\Middleware\PushNotification::class,
            'reward' => \App\Http\Middleware\RewardMiddleware::class,
            'segpay' => \App\Http\Middleware\Segpay::class,
            'track.emails' => \App\Http\Middleware\EmailsStatTracking::class,
            'user.touch' => \App\Http\Middleware\TouchUser::class,
            'verification' => \App\Http\Middleware\Verification::class,
            'signup.after' => \App\Http\Middleware\AfterSignup::class,
        ]);

        $middleware->priority([
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\AuthenticateByToken::class,
            \App\Http\Middleware\EmailsStatTracking::class,
            \App\Http\Middleware\Authenticate::class,
            \Illuminate\Routing\Middleware\ThrottleRequests::class,
            \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \Illuminate\Auth\Middleware\Authorize::class,
            \App\Http\Middleware\TouchUser::class,
            \App\Http\Middleware\Verification::class,
            \App\Http\Middleware\Activation::class,
            \App\Http\Middleware\Preferences::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->dontReport([
            DeadlineExceededException::class,
        ]);

        Integration::handles($exceptions);
    })->create();
