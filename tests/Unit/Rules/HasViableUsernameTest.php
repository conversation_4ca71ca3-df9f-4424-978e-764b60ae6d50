<?php

use App\Repository\UserRepository;
use App\Rules\HasViableUsername;
use Illuminate\Translation\ArrayLoader;
use Illuminate\Translation\Translator;

beforeEach(function () {
    // Bind a basic translator instance so that the __() helper works.
    $translator = new Translator(new ArrayLoader, 'en');
    app()->instance('translator', $translator);

    // Bind a mocked UserRepository instance.
    $this->userRepositoryMock = Mockery::mock(UserRepository::class);
    app()->instance(UserRepository::class, $this->userRepositoryMock);

    // Create the rule instance (it will use the bound UserRepository).
    $this->rule = new HasViableUsername;
});

afterAll(function () {
    // Close Mockery to prevent memory leaks.
    Mockery::close();
});

test('passes when repository returns a viable username', function () {
    // Expect the repository to return a valid username (not false).
    $this->userRepositoryMock
        ->should<PERSON><PERSON><PERSON>ve('generateUsernameWithSuffix')
        ->once()
        ->with('testvalue')
        ->andReturn('testvalue1');

    $failCalled = false;
    $fail = function ($message) use (&$failCalled) {
        $failCalled = true;
    };

    // Validate with a value that the repository can process.
    $this->rule->validate('username', 'testvalue', $fail);
    expect($failCalled)->toBeFalse();
});

test('fails when repository returns false', function () {
    // Expect the repository to return false, meaning no viable username could be generated.
    $this->userRepositoryMock
        ->shouldReceive('generateUsernameWithSuffix')
        ->once()
        ->with('invalid')
        ->andReturnFalse();

    $failMessage = null;
    $fail = function ($message) use (&$failMessage) {
        $failMessage = $message;
    };

    // Validate with a value that fails.
    $this->rule->validate('username', 'invalid', $fail);
    expect($failMessage)->toBe(__('Cannot generate a username, please change your username'));
});
