<?php

uses(Tests\TestCase::class)->in(__DIR__);

/** We are testing if:
 * A valid birthdate string passes.
 * A future date fails.
 * An underage date fails with the underage error message.
 * An invalid date string fails.
 * A valid birthdate array (with correctly formatted fields) passes.
 * An array missing one or more required keys fails.
 * An array with incorrect field lengths fails.
 * An empty value (string) fails.
 */

use App\Rules\ValidBirthdate;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

it('passes for a valid birthdate string (over 18 and not in the future)', function () {
    $rule = new ValidBirthdate;
    $validDate = Carbon::now()->subYears(20)->toDateString();

    $validator = Validator::make(['birthdate' => $validDate], [
        'birthdate' => [$rule],
    ]);

    expect($validator->fails())->toBeFalse();
});

it('fails for a birthdate string in the future', function () {
    $rule = new ValidBirthdate;
    $futureDate = Carbon::now()->addDay()->toDateString();

    $validator = Validator::make(['birthdate' => $futureDate], [
        'birthdate' => [$rule],
    ]);

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->first('birthdate'))
        ->toBe('Please provide a valid date of birth.');
});

it('fails for an underage birthdate string', function () {
    $rule = new ValidBirthdate;
    $underageDate = Carbon::now()->subYears(17)->toDateString();

    $validator = Validator::make(['birthdate' => $underageDate], [
        'birthdate' => [$rule],
    ]);

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->first('birthdate'))
        ->toBe('Registration is only allowed when you are at least 18 years old');
});

it('fails for an invalid date string', function () {
    $rule = new ValidBirthdate;
    $invalidDate = 'not-a-date';

    $validator = Validator::make(['birthdate' => $invalidDate], [
        'birthdate' => [$rule],
    ]);

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->first('birthdate'))
        ->toBe('Please provide a valid date of birth.');
});

it('passes for a valid birthdate array (over 18 and not in the future)', function () {
    $rule = new ValidBirthdate;
    $birthArray = [
        'birth_year' => Carbon::now()->subYears(20)->year,
        'birth_month' => str_pad(Carbon::now()->subYears(20)->month, 2, '0', STR_PAD_LEFT),
        'birth_day' => str_pad(Carbon::now()->subYears(20)->day, 2, '0', STR_PAD_LEFT),
    ];

    $validator = Validator::make(['birthdate' => $birthArray], [
        'birthdate' => [$rule],
    ]);

    expect($validator->fails())->toBeFalse();
});

it('fails for a birthdate array with missing keys', function () {
    $rule = new ValidBirthdate;
    // Missing 'birth_day'
    $birthArray = [
        'birth_year' => Carbon::now()->subYears(20)->year,
        'birth_month' => str_pad(Carbon::now()->subYears(20)->month, 2, '0', STR_PAD_LEFT),
    ];

    $validator = Validator::make(['birthdate' => $birthArray], [
        'birthdate' => [$rule],
    ]);

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->first('birthdate'))
        ->toBe('Please provide a valid date of birth.');
});

it('fails for a birthdate array with incorrect field lengths', function () {
    $rule = new ValidBirthdate;
    // For example, 'birth_month' is not 2 digits.
    $birthArray = [
        'birth_year' => '2000', // OK: 4 digits.
        'birth_month' => '1',    // Not 2 digits.
        'birth_day' => '05',   // OK: 2 digits.
    ];

    $validator = Validator::make(['birthdate' => $birthArray], [
        'birthdate' => [$rule],
    ]);

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->first('birthdate'))
        ->toBe('Please provide a valid date of birth.');
});

it('fails when birthdate is empty (string)', function () {
    $rule = new ValidBirthdate;
    $validator = Validator::make(['birthdate' => ''], [
        'birthdate' => [$rule],
    ]);

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->first('birthdate'))
        ->toBe('Please provide a valid date of birth.');
});

it('fails when birthdate array is empty', function () {
    $rule = new ValidBirthdate;
    $validator = Validator::make(['birthdate' => []], [
        'birthdate' => [$rule],
    ]);

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->first('birthdate'))
        ->toBe('Please provide a valid date of birth.');
});
