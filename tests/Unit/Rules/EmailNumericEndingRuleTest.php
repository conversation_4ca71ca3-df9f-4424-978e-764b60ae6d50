<?php

use App\Rules\EmailNumericEndingRule;
use Illuminate\Translation\ArrayLoader;
use Illuminate\Translation\Translator;

beforeEach(function () {
    // Bind a basic translator instance so that __() works correctly.
    $translator = new Translator(new ArrayLoader, 'en');
    app()->instance('translator', $translator);

    $this->emailNumericRule = app()->make(EmailNumericEndingRule::class);
});

test('valid email does not trigger failure', function () {
    $failCalled = false;
    $fail = function ($message) use (&$failCalled) {
        $failCalled = true;
    };

    // "<EMAIL>" has no numeric-only value and does not contain 5+ consecutive digits before "@"
    $this->emailNumericRule->validate('email', '<EMAIL>', $fail);
    expect($failCalled)->toBeFalse();
});

test('email with five consecutive digits before "@" triggers failure', function () {
    $failMessage = null;
    $fail = function ($message) use (&$failMessage) {
        $failMessage = $message;
    };

    // "<EMAIL>" contains "12345" before "@" which should trigger a failure.
    $this->emailNumericRule->validate('email', '<EMAIL>', $fail);
    expect($failMessage)->toBe(__('Unfortunately the given e-mail address is not supported. Please try another e-mail address.'));
});

test('numeric value triggers failure', function () {
    $failMessage = null;
    $fail = function ($message) use (&$failMessage) {
        $failMessage = $message;
    };

    // A purely numeric value should trigger a failure.
    $this->emailNumericRule->validate('email', '123456789', $fail);
    expect($failMessage)->toBe(__('Unfortunately the given e-mail address is not supported. Please try another e-mail address.'));
});
