<?php

use App\Rules\EmailContainingNumberRule;
use Illuminate\Translation\ArrayLoader;
use Illuminate\Translation\Translator;

beforeEach(function () {
    // Bind a simple translator instance to the container so that __() works properly.
    $translator = new Translator(new ArrayLoader, 'en');
    app()->instance('translator', $translator);

    $this->emailRule = app()->make(EmailContainingNumberRule::class);
});

test('validating an email with acceptable number of digits does not trigger failure', function () {
    $failCalled = false;
    $fail = function ($message) use (&$failCalled) {
        $failCalled = true;
    };

    // This email has one digit ("<EMAIL>") and should pass.
    $this->emailRule->validate('email', '<EMAIL>', $fail);
    expect($failCalled)->toBeFalse();

    // This email has no digits and should also pass.
    $failCalled = false;
    $this->emailRule->validate('email', '<EMAIL>', $fail);
    expect($failCalled)->toBeFalse();
});

test('validating an email with too many digits triggers failure with literal message', function () {
    $failMessage = null;
    $fail = function ($message) use (&$failMessage) {
        $failMessage = $message;
    };

    // This email has two digits ("<EMAIL>") and should trigger a failure.
    $this->emailRule->validate('email', '<EMAIL>', $fail);
    expect($failMessage)->toBe(__('E-mail address already taken.'));
});
