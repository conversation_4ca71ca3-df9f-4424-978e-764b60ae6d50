<?php

use App\Repository\GeoRepository;
use App\Rules\LocationExists;
use Illuminate\Translation\ArrayLoader;
use Illuminate\Translation\Translator;

beforeEach(function () {
    // Bind a basic translator instance so the __() helper works.
    $translator = new Translator(new ArrayLoader, 'en');
    app()->instance('translator', $translator);

    // Bind a mocked GeoRepository instance.
    $this->geoRepositoryMock = Mockery::mock(GeoRepository::class);
    app()->instance(GeoRepository::class, $this->geoRepositoryMock);

    // Create the rule instance.
    $this->rule = new LocationExists;
});

afterAll(function () {
    Mockery::close();
});

test('passes when location exists', function () {
    // Expect the repository to return true for a valid location.
    $this->geoRepositoryMock
        ->shouldReceive('doesLocationMatch')
        ->once()
        ->with('valid_location')
        ->andReturn(true);

    $failCalled = false;
    $fail = function ($message) use (&$failCalled) {
        $failCalled = true;
    };

    $this->rule->validate('location', 'valid_location', $fail);
    expect($failCalled)->toBeFalse();
});

test('fails when location does not exist', function () {
    // Expect the repository to return false for an invalid location.
    $this->geoRepositoryMock
        ->shouldReceive('doesLocationMatch')
        ->once()
        ->with('invalid_location')
        ->andReturn(false);

    $failMessage = null;
    $fail = function ($message) use (&$failMessage) {
        $failMessage = $message;
    };

    $this->rule->validate('location', 'invalid_location', $fail);
    expect($failMessage)->toBe(__('Invalid location'));
});
