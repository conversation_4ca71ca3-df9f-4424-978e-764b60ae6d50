<?php

use App\Models\Domain;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Services\Payment\SegPayPaypalPaymentService;
use App\ValueObjects\Payments\SegPayPaypal;

// Load framework because service uses facades
uses(\Illuminate\Foundation\Testing\TestCase::class);

it('redirects to the correct payment link', function () {
    $order = Mockery::mock(Order::class);
    $order->shouldReceive('getAttribute')->with('order_id')->andReturn('12345');
    $order->shouldReceive('getAttribute')->with('amount')->andReturn(100.00);

    $user = Mockery::mock(User::class);
    $user->shouldReceive('getAttribute')->with('id')->andReturn('ECD91C9D-AA66-4D19-8628-94DA04DBFCE6');
    $user->shouldReceive('getAttribute')->with('username')->andReturn('pest');
    $user->shouldReceive('getAttribute')->with('email')->andReturn('<EMAIL>');
    $user->shouldReceive('getAttribute')->with('click_request_id')->andReturn('click123');

    $order->shouldReceive('getAttribute')->with('user')->andReturn($user);
    $order->shouldReceive('isForDefaultProduct')->andReturn(true);

    $product = Mockery::mock(Product::class);
    $product->shouldReceive('getAttribute')->with('name')->andReturn('ProductName');
    $product->shouldReceive('getAttribute')->with('credits')->andReturn(100);
    $product->shouldReceive('toPay')->andReturn(100.00);

    $order->shouldReceive('getAttribute')->with('product')->andReturn($product);

    $domain = Mockery::mock(Domain::class);
    $domain->shouldReceive('getAttribute')->with('currency')->andReturn('usd');
    $domain->shouldReceive('getAttribute')->with('country_code')->andReturn('US');
    $domain->shouldReceive('getAttribute')->with('full_url')->andReturn('http://pest.local.test');
    $domain->shouldReceive('getAttribute')->with('name')->andReturn('example.com');
    $domain->shouldReceive('getAttribute')->with('lang')->andReturn('en');

    // Mock the SegPayPaypal object
    $payment = Mockery::mock(SegPayPaypal::class, function ($mock) {
        $mock->shouldReceive('getPackageId')->andReturn('package123');
        $mock->shouldReceive('getPricePointId')->andReturn('price123');
        $mock->shouldReceive('getPaymentLink')->andReturn('https://example.com/payment');
    });

    // Instantiate the service
    $service = new SegPayPaypalPaymentService($payment);
    $service->setDomain($domain);

    // Call the generate method
    $response = $service->generate($order);

    // Assert the response is a redirect to the correct URL
    $expectedUrl = 'https://example.com/payment';
    expect($response->getTargetUrl())->toContain($expectedUrl);
});
