<?php

use App\Models\Domain;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Services\Payment\TruevoGooglePayPaymentService;
use App\ValueObjects\Payments\Truevo;

// Load framework because service uses facades
uses(\Illuminate\Foundation\Testing\TestCase::class);

it('generates the correct payment details and returns them as JSON', function () {
    $order = Mockery::mock(Order::class);
    $order->shouldReceive('getAttribute')->with('order_id')->andReturn('12345');
    $order->shouldReceive('getAttribute')->with('id')->andReturn(1);

    $user = Mockery::mock(User::class);
    $user->shouldReceive('getAttribute')->with('email')->andReturn('<EMAIL>');

    $order->shouldReceive('getAttribute')->with('user')->andReturn($user);

    $product = Mockery::mock(Product::class);
    $product->shouldReceive('toPay')->andReturn(100.00);

    $order->shouldReceive('getAttribute')->with('product')->andReturn($product);

    $domain = Mockery::mock(Domain::class);
    $domain->shouldReceive('getAttribute')->with('currency')->andReturn('USD');
    $domain->shouldReceive('getAttribute')->with('country_code')->andReturn('US');
    $domain->shouldReceive('getAttribute')->with('url')->andReturn('pest.local.test');
    $domain->shouldReceive('getAttribute')->with('name')->andReturn('Example');

    // Mock the Truevo object
    $payment = Mockery::mock(Truevo::class, function ($mock) {
        $mock->shouldReceive('getMid')->andReturn('mid123');
        $mock->shouldReceive('getTid')->andReturn('tid123');
        $mock->shouldReceive('getToken')->andReturn('token123');
        $mock->shouldReceive('getMerchantId')->andReturn('merchant123');
        $mock->shouldReceive('getGatewayMerchantId')->andReturn('gateway123');
    });

    // Instantiate the service
    $service = new TruevoGooglePayPaymentService($payment);
    $service->setDomain($domain);

    $response = $service->generate($order);

    // Assert the response contains the correct details
    $expectedDetails = (object) [
        'transactionType' => 'final',
        'storeName' => 'pest.local.test',
        'countryCode' => 'US',
        'merchantReference' => '12345',
        'IdempotencyKey' => 1,
        'paymentReference' => 'Example',
        'url' => 'pest.local.test',
        'amount' => '100',
        'currencyAlphaCode' => 'USD',
        'mid' => 'mid123',
        'tid' => 'tid123',
        'token' => 'Bearer token123',
        'cancelUrl' => config('app.url') . '/credits/cancel?order_number=12345',
        'email' => '<EMAIL>',
        'merchantId' => 'merchant123',
        'gateway' => strtolower(get_class($payment)),
        'gatewayMerchantId' => 'gateway123',
    ];

    $this->assertEquals((object) ['details' => $expectedDetails], $response->getData());
});
