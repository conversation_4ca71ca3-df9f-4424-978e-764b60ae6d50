<?php

use App\Models\User;
use App\Services\DisablePreviousAccount;
use Carbon\Carbon;

it('does not disable previous user when affiliate alias is not in the rules', function () {
    $previousUser = Mockery::mock(User::class);

    expect(DisablePreviousAccount::disable($previousUser, 'mediatrk'))->toBeFalse();
});

it('does not disable previous user when affiliate alias is in the rules but subs are not', function () {
    $previousUser = Mockery::mock(User::class);

    expect(DisablePreviousAccount::disable($previousUser, 'mason', 'mason_not_included_in_sub'))->toBeFalse();
});

it('does disable previous user when affiliate alias and subs are in the rules', function () {
    $previousUser = Mockery::mock(User::class);
    $previousUser->shouldReceive('getAttribute')->with('active')->andReturn(0);
    $previousUser->shouldReceive('getAttribute')->with('credits')->andReturn(0);
    $previousUser->shouldReceive('getAttribute')->with('created_at')->andReturn(Carbon::parse('-2 months'));
    $previousUser->shouldReceive('getAttribute')->with('updated_at')->andReturn(Carbon::parse('-2 months'));
    $previousUser->shouldReceive('getAttribute')->with('username')->andReturn('testuser');
    $previousUser->shouldReceive('update')->with(Mockery::on(function ($arg) {
        return isset($arg['status']) && $arg['status'] === false
            && isset($arg['username']) && str_starts_with($arg['username'], 'testuser_rm')
            && isset($arg['unsubscribed_at'])
            && isset($arg['deleted_at']);
    }));

    expect(DisablePreviousAccount::disable($previousUser, 'mason', 'mason_4_included_sub_clean'))->toBeTrue();
});

it('does not disable if user has any credits', function () {
    $previousUser = Mockery::mock(User::class)->makePartial();
    $previousUser->active = 1;
    $previousUser->credits = 10;

    expect(DisablePreviousAccount::disable($previousUser, 'fabtrk'))->toBeFalse();
});

it('does not disable if user is active last 30 days', function () {
    $previousUser = Mockery::mock(User::class);
    $previousUser->shouldReceive('getAttribute')->with('active')->andReturn(1);
    $previousUser->shouldReceive('getAttribute')->with('credits')->andReturn(0);
    $previousUser->shouldReceive('getAttribute')->with('created_at')->andReturn(Carbon::parse('-2 months'));
    $previousUser->shouldReceive('getAttribute')->with('updated_at')->andReturn(Carbon::parse('-3 days'));

    expect(DisablePreviousAccount::disable($previousUser, 'fabtrk'))->toBeFalse();
});

it('does disable previous user if not active in last 30 day', function () {
    $previousUser = Mockery::mock(User::class);
    $previousUser->shouldReceive('getAttribute')->with('active')->andReturn(0);
    $previousUser->shouldReceive('getAttribute')->with('credits')->andReturn(0);
    $previousUser->shouldReceive('getAttribute')->with('created_at')->andReturn(Carbon::parse('-2 months'));
    $previousUser->shouldReceive('getAttribute')->with('updated_at')->andReturn(Carbon::parse('-2 months'));
    $previousUser->shouldReceive('getAttribute')->with('username')->andReturn('testuser');
    $previousUser->shouldReceive('update')->with(Mockery::on(function ($arg) {
        return isset($arg['status']) && $arg['status'] === false
            && isset($arg['username']) && str_starts_with($arg['username'], 'testuser_rm')
            && isset($arg['unsubscribed_at'])
            && isset($arg['deleted_at']);
    }));

    expect(DisablePreviousAccount::disable($previousUser, 'fabtrk'))->toBeTrue();
});
