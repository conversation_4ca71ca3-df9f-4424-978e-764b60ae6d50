<?php

use App\Services\Leadfilter;
use Illuminate\Http\Client\Response;
use Mo<PERSON>y\MockInterface;

test('handles the api response correctly', function () {
    // Prepare
    $errorResponse = $this->mock(Response::class, function (MockInterface $mock) {
        $mock->shouldReceive('getStatusCode')->andReturn(400);
        $mock->shouldReceive('clientError')->andReturn(true);
        $mock->shouldReceive('serverError')->andReturn(false);
    });

    expect(Leadfilter::successFullResponse($errorResponse))->toBeFalse();

    $errorResponse = $this->mock(Response::class, function (MockInterface $mock) {
        $mock->shouldReceive('getStatusCode')->andReturn(500);
        $mock->shouldReceive('serverError')->andReturn(true);
        $mock->shouldReceive('clientError')->andReturn(false);
    });

    expect(Leadfilter::successFullResponse($errorResponse))->toBeFalse();

    $successResponse = $this->mock(Response::class, function (MockInterface $mock) {
        $mock->shouldReceive('getStatusCode')->andReturn(500);
        $mock->shouldReceive('clientError')->andReturn(false);
        $mock->shouldReceive('serverError')->andReturn(false);
        $mock->shouldReceive('json')->andReturn(true);
    });

    expect(Leadfilter::successFullResponse($successResponse))->toBeTrue();

    $successResponse = $this->mock(Response::class, function (MockInterface $mock) {
        $mock->shouldReceive('getStatusCode')->andReturn(500);
        $mock->shouldReceive('clientError')->andReturn(false);
        $mock->shouldReceive('serverError')->andReturn(false);
        $mock->shouldReceive('json')->andReturn(['prediction' => true]);
    });

    expect(Leadfilter::successFullResponse($successResponse))->toBeTrue();
});
