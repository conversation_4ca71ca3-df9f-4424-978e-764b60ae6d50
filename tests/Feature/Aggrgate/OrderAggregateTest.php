<?php

use App\Aggregates\OrderAggregate;
use App\Events\EventSourcing\OrderCreated;
use App\Events\EventSourcing\OrderPaid;
use App\Exceptions\EventSourcing\OrderException;
use App\Models\User;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use Mirzacles\Models\Order;
use Spatie\EventSourcing\Facades\Projectionist;

beforeEach(function () {
    $domain = $this->getDomainFromAppUrl();
    $this->user = User::factory()->create([
        'site_id' => $domain->site_id,
        'label_id' => $domain->label_id,
    ]);
});

test('creates an order', function () {
    $order = Order::factory()->create([
        'user_id' => $this->user->id,
        'site_id' => $this->user->site_id,
        'status' => 'pending',
    ]);

    Event::fake();
    Queue::fake();
    Projectionist::withoutEventHandlers();

    OrderAggregate::fake()
        ->when(fn (OrderAggregate $aggregate) => $aggregate->createOrder($order))
        ->assertRecorded(new OrderCreated($order->id));
});

test('it throws an exception when an order is already created', function () {
    $order = Order::factory()->create([
        'user_id' => $this->user->id,
        'site_id' => $this->user->site_id,
        'status' => 'pending',
    ]);

    Event::fake();
    Projectionist::withoutEventHandlers();

    OrderAggregate::fake()
        ->given([new OrderCreated($order)])
        ->when(fn (OrderAggregate $aggregate) => $aggregate->createOrder($order))
        ->assertNotRecorded(new OrderCreated($order->id));
})->throws(OrderException::class);

test('sets an order as paid', function (?string $locationInput, $excepted) {
    $order = Order::factory()->create([
        'user_id' => $this->user->id,
        'site_id' => $this->user->site_id,
        'status' => 'pending',
        'offer_batch_id' => '',
    ]);

    Event::fake();
    Queue::fake();
    Projectionist::withoutEventHandlers();

    OrderAggregate::fake()
        ->given([new OrderCreated($order->id)])
        ->when(function (OrderAggregate $aggregate) use ($locationInput, $order) {
            if ($locationInput) {
                $aggregate->markAsPaid($order, $locationInput);

                return;
            }

            $aggregate->markAsPaid($order);
        })
        ->assertRecorded(new OrderPaid($order->id, $excepted));
})->with([
    ['CelerispayController::return', 'CelerispayController@return'],
    [null, null],
]);

test('throws an exception if an order is already paid', function () {
    $order = Order::factory()->create([
        'user_id' => $this->user->id,
        'site_id' => $this->user->site_id,
        'status' => 'paid',
        'offer_batch_id' => '',
    ]);

    Event::fake();
    Queue::fake();
    Projectionist::withoutEventHandlers();

    OrderAggregate::fake()
        ->given([new OrderCreated($order->id), new OrderPaid($order->id)])
        ->when(fn (OrderAggregate $aggregate) => $aggregate->markAsPaid($order, 'notification'))
        ->assertNotRecorded(new OrderPaid($order->id, 'notification'));
})->throws(OrderException::class);
