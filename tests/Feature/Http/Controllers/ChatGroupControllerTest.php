<?php

use App\Models\ChatGroup;
use App\Models\ChatGroupPost;
use App\Models\User;
use Database\Seeders\ChatGroupSeeder;
use Illuminate\Support\Str;
use Mirzacles\Models\Module;

return;

beforeEach(function () {
    $this->domain = $this->getDomainFromAppUrl();

    // Check if chat_groups module exists
    $chatGroupsModule = Module::where('name', 'chat_groups')->first();

    if (!$chatGroupsModule) {
        $this->markTestSkipped('Chat groups module is not available');
    }

    $this->domain->modules()->attach($chatGroupsModule);

    $this->user = User::factory()->create([
        'label_id' => $this->domain->label_id,
        'site_id' => $this->domain->site_id,
        'status' => true,
        'active' => true,
        'confirmed' => true,
        'initial_setup' => true,
        'credits' => 500,
    ]);

    $this->seed(ChatGroupSeeder::class);

    $this->artisan('cache:clear');
});

test('showing tribes', function () {
    // Prepare
    $user = $this->user;

    // Execute
    $this->assertGuest()->get(route('groups'))->assertRedirect();
    $userResponse = $this->actingAs($user)->get(route('groups'));

    // Assert
    $userResponse->assertSuccessful()->assertViewHas([
        'chat_groups' => ChatGroup::where('site_id', $user->site_id)->get(),
    ]);
});

test('showing single tribe', function () {
    // Prepare
    $user = $this->user;

    $chatGroup = ChatGroup::where('site_id', $user->site_id)->firstOrFail();

    // Execute
    $this->assertGuest()->get(route('groups'))->assertRedirect();

    $url = route('groupPosts', ['section' => 'contributions', 'id' => $chatGroup->id]);
    $userResponse = $this->actingAs($user)->get($url);

    // Assert
    $userResponse->assertSuccessful();
});

test('joining a tribe', function () {
    // Prepare
    $user = $this->user;
    $chatGroup = ChatGroup::where('site_id', $user->site_id)->firstOrFail();

    expect($user->groups()->where('chat_group_id', $chatGroup->id)->exists())->toBeFalse();

    // Execute
    $url = route('joinGroup', ['id' => $chatGroup->id]);
    $this->assertGuest()->post($url)->assertRedirect();
    $response = $this->actingAs($user)->post($url);

    // Assert
    $response->assertSuccessful();
    expect($user->groups()->where('chat_group_id', $chatGroup->id)->exists())->toBeTrue();
});

test('leaving a tribe', function () {
    // Prepare
    $user = $this->user;
    $chatGroup = ChatGroup::where('site_id', $user->site_id)->firstOrFail();

    joinChatGroup($user, $chatGroup);

    expect($user->groups()->where('chat_group_id', $chatGroup->id)->exists())->toBeTrue();

    // Execute
    $url = route('leaveGroup', ['id' => $chatGroup->id]);
    $this->assertGuest()->post($url)->assertRedirect();
    $response = $this->actingAs($user)->post($url);

    // Assert
    $response->assertSuccessful();
    expect($user->groups()->where('chat_group_id', $chatGroup->id)->exists())->toBeFalse();
});

test('creating a chat group post', function () {
    // Prepare
    $user = $this->user;
    $chatGroup = ChatGroup::where('site_id', $user->site_id)->firstOrFail();

    $this->assertDatabaseMissing(ChatGroupPost::newModelInstance()->getTable(), [
        'user_id' => $user->id,
    ]);

    joinChatGroup($user, $chatGroup);

    // Execute
    $url = route('saveGroupPost', $chatGroup);
    $this->assertGuest()->post($url)->assertRedirect();

    $response = $this->actingAs($user)->post($url, [
        'title' => Str::random(),
        'message' => Str::random(),
    ]);

    // Assert
    $response->assertRedirect();
    $response->assertSessionDoesntHaveErrors();

    $this->assertDatabaseHas(ChatGroupPost::newModelInstance()->getTable(), [
        'user_id' => $user->id,
    ]);
});

test('creating a chat group post with invalid input', function () {
    // Prepare
    $user = $this->user;
    $chatGroup = ChatGroup::where('site_id', $user->site_id)->firstOrFail();

    $this->assertDatabaseMissing(ChatGroupPost::newModelInstance()->getTable(), [
        'user_id' => $user->id,
    ]);

    joinChatGroup($user, $chatGroup);

    // Execute
    $url = route('saveGroupPost', $chatGroup);
    $this->assertGuest()->post($url)->assertRedirect();

    $response = $this->actingAs($user)->post($url, []);

    // Assert
    $response->assertRedirect();
    $response->assertSessionHasErrors();

    $this->assertDatabaseMissing(ChatGroupPost::newModelInstance()->getTable(), [
        'user_id' => $user->id,
    ]);
});

test('chat group message', function () {
    // Prepare
    $user = $this->user;
    $chatGroup = ChatGroup::where('site_id', $user->site_id)->firstOrFail();

    $this->assertDatabaseMissing(ChatGroupPost::newModelInstance()->getTable(), [
        'user_id' => $user->id,
    ]);

    joinChatGroup($user, $chatGroup);

    // Execute
    $url = route('saveGroupPost', $chatGroup);
    $this->assertGuest()->post($url)->assertRedirect();

    $message = 'I was sucking my boyfriend’s dick once and decided I would try to go deeper. I ended up having my jaw locked in place for about 10-15 seconds :flushed:';

    $this->actingAs($user)->post($url, [
        'message' => $message,
    ]);

    /** @var ChatGroupPost $chatGroupPost */
    $chatGroupPost = $chatGroup->posts()->where('message', $message)->firstOrFail();

    $this->assertNotEquals($message, $chatGroupPost->message);
    expect(Str::contains($chatGroupPost->message, ':flushed:'))->toBeFalse();
});

function joinChatGroup(User $user, $chatGroup): void
{
    $user->groups()->attach($chatGroup->id);
}
