<?php

use App\Models\Favorite;
use App\Models\Profile;
use App\Models\User;
use App\Support\FlashMessage;

beforeEach(function () {
    $this->domain = $this->getDomainFromAppUrl();
    $this->user = User::factory()->create([
        'label_id' => $this->domain->label_id,
        'site_id' => $this->domain->site_id,
        'status' => true,
        'active' => true,
        'confirmed' => true,
        'initial_setup' => true,
    ]);

    $this->profile = Profile::factory()->create([
        'site_id' => $this->user->site_id,
    ]);
});

test('adding a profile to favorites', function () {
    $user = $this->user;
    $profile = $this->profile;

    $route = route('addFavorite', $profile->username);

    $this->assertDatabaseMissing('favorites', [
        'user_id' => $user->id,
        'profile_id' => $profile->id,
        'site_id' => $user->site_id,
    ]);

    // Execute
    $this->assertGuest()->get($route)->assertRedirect('login');
    $response = $this->actingAs($user)->getAjax($route);

    // Assert
    $response->assertSuccessful();
    $response->assertJson([
        'status' => FlashMessage::STATUS_SUCCESS,
    ]);

    $response->assertJsonStructure([
        'status',
        'message',
    ]);

    $this->assertDatabaseHas('favorites', [
        'user_id' => $user->id,
        'profile_id' => $profile->id,
        'site_id' => $user->site_id,
    ]);
});

test('remove adding profile to favorites', function () {
    $user = $this->user;
    $profile = $this->profile;

    addToFavorites($user, $profile);

    $route = route('removeFavorite', $profile->username);

    // Execute
    $this->assertGuest()->get($route)->assertRedirect('login');
    $validResponse = $this->actingAs($user)->getAjax($route);

    // Assert
    $validResponse->assertSuccessful();
    $validResponse->assertJson([
        'status' => FlashMessage::STATUS_SUCCESS,
    ]);

    $validResponse->assertJsonStructure([
        'status',
        'message',
    ]);

    $this->assertDatabaseMissing($this->getTable(Favorite::class), [
        'user_id' => $user->id,
        'profile_id' => $profile->id,
        'site_id' => $user->site_id,
    ]);
});

test('showing favorite profiles', function () {
    $domain = $this->domain;
    $user = $this->user;
    $profiles = Profile::factory(20)->create([
        'site_id' => $user->site_id,
    ]);

    $profiles->each(function (Profile $profile) use ($user) {
        addToFavorites($user, $profile);
    });

    $route = route('favorites');

    $this->assertGuest()->get($route)->assertRedirect('login');
    $validResponse = $this->actingAs($user)->get($route);

    // Assert
    $validResponse->assertSuccessful();
});

function addToFavorites(User $user, Profile $profile): void
{
    $user->favorites()->create([
        'profile_id' => $profile->id,
        'site_id' => $user->site_id,
    ]);

    test()->assertDatabaseHas(test()->getTable(Favorite::class), [
        'user_id' => $user->id,
        'profile_id' => $profile->id,
        'site_id' => $user->site_id,
    ]);
}
