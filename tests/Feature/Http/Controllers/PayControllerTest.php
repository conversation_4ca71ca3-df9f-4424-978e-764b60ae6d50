<?php

use App\Models\Product;
use App\Models\User;
use Mirzacles\Models\Order;

beforeEach(function () {
    $this->domain = $this->getDomainFromAppUrl();
});

it('tests products', function () {
    $user = User::factory()->create([
        'site_id' => $this->domain->site_id,
        'label_id' => $this->domain->label_id,
        'status' => true,
        'active' => true,
        'confirmed' => true,
        'initial_setup' => true,
    ]);

    Product::factory()->create(['site_id' => $this->domain->site_id]);

    $this->artisan('cache:clear');

    $this->actingAs($user)
        ->get(route('pay'))
        ->assertStatus(200)
        ->assertViewIs('theme.' . $this->domain->theme . '.payment.credits')
        ->assertViewHas('products');
});

it('tests transaction', function () {
    $user = User::factory()->create([
        'status' => true,
        'confirmed' => true,
        'active' => true,
        'credits' => 0,
        'site_id' => $this->domain->site_id,
        'label_id' => $this->domain->label_id,
        'initial_setup' => true,
    ]);

    $product = Product::factory()->create(['site_id' => $this->domain->site_id]);
    $order = Order::factory()->create([
        'product_id' => $product->id,
        'product_type' => Product::class,
        'user_id' => $user->id,
        'status' => 'pending',
        'payment_method_id' => $this->domain->getPaymentMethodFromType('credit_card')->id,
    ]);

    $this->artisan('cache:clear');

    $response = $this->actingAs($user)
        ->post('credits/transaction', ['order_id' => $order->id]);

    $response->assertStatus(302)
        ->assertRedirect();
});

// Add more tests for other methods like getReturn, exchange, offer, etc.
