<?php

use App\Events\MessageSentToOperator;
use App\Models\Profile;
use App\Models\User;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Illuminate\Testing\TestResponse;
use Mirzacles\Models\Flirt;
use Mirzacles\Models\TrackRecord;
use Mirzacles\Models\Userlog;

beforeEach(function () {
    Event::fake();
    Http::fake([
        'https://operators.test/api/incoming-message' => Http::response(''),
    ]);

    $this->domain = $this->getDomainFromAppUrl();
    $this->flirt = Flirt::query()->firstOrFail();
    if (!$this->flirt) {
        throw new \Exception('No Flirt found for testing. Please seed flirts or use a factory.');
    }

    $this->flirtPrice = $this->flirt->default ?? 1;
    $this->profile = Profile::factory()->create(['site_id' => $this->domain->site_id]);
    $this->route = route('addFlirt', $this->profile->username);
});

it('redirects guest user to login when attempting to send flirt', function () {
    $this->assertGuest()
        ->post($this->route, ['flirt' => $this->flirt->name])
        ->assertRedirect('login');
});

it('redirects user without credits to payment page when attempting to send flirt', function () {
    /** @var User $userWithoutCredits */
    $userWithoutCredits = User::factory()->create([
        'status' => true,
        'confirmed' => true,
        'active' => true,
        'credits' => 0,
        'site_id' => $this->domain->site_id,
        'label_id' => $this->domain->label_id,
        'initial_setup' => true,
    ]);

    $response = $this->actingAs($userWithoutCredits)
        ->post($this->route, ['flirt' => $this->flirt->name]);

    $expectedUrl = route('pay', ['profile' => '']);
    $response->assertRedirect($expectedUrl);
});

it('allows user with credits to send multiple flirts and deducts credits correctly', function () {
    $initialAmountOfCredits = 500;

    /** @var User $userWithCredits */
    $userWithCredits = User::factory()->create([
        'status' => true,
        'confirmed' => true,
        'active' => true,
        'credits' => $initialAmountOfCredits,
        'site_id' => $this->domain->site_id,
        'label_id' => $this->domain->label_id,
        'initial_setup' => true,
    ]);

    // ---- FIRST FLIRT ----
    $firstFlirtResponse = checkFlirt($userWithCredits, $initialAmountOfCredits, $this->route, $this->flirt, $this->flirtPrice);
    $firstFlirtResponse->assertRedirect('/')->assertSessionHas('success', __('Your flirt has been sent!'));

    $creditsAfterFirst = User::find($userWithCredits->id)->credits;
    expect($creditsAfterFirst)->toBe($initialAmountOfCredits - $this->flirtPrice);

    $this->assertDatabaseHas('user_tracking', ['user_id' => $userWithCredits->id, 'event' => TrackRecord::SPENT_CREDIT]);
    $this->assertDatabaseHas('logs', ['user_id' => $userWithCredits->id, 'profile_id' => $this->profile->id, 'category' => Userlog::CATEGORY_OTHER]);
    Event::assertNotDispatched(MessageSentToOperator::class);

    // ---- SECOND FLIRT ----
    $creditsBeforeSecond = $creditsAfterFirst;
    $secondFlirtResponse = checkFlirt($userWithCredits, $creditsBeforeSecond, $this->route, $this->flirt, $this->flirtPrice);
    $secondFlirtResponse->assertRedirect('/')->assertSessionHas('success', __('Your flirt has been sent!'));

    $creditsAfterSecond = User::find($userWithCredits->id)->credits;
    expect($creditsAfterSecond)->toBe($initialAmountOfCredits - (2 * $this->flirtPrice));

    // ---- THIRD FLIRT (with go-to-chat) ----
    $creditsBeforeThird = $creditsAfterSecond;
    $thirdFlirtResponse = checkFlirt($userWithCredits, $creditsBeforeThird, $this->route, $this->flirt, $this->flirtPrice, ['go-to-chat' => true]);

    $conversationRoute = route('conversation', ['type' => 'conversation', 'username' => $this->profile->username]);
    $thirdFlirtResponse->assertRedirect($conversationRoute);
    expect(User::find($userWithCredits->id)->credits)->toBe($initialAmountOfCredits - (3 * $this->flirtPrice));
});

function checkFlirt(User $user, int $expectedStartingCredits, string $route, Flirt $flirt, int $flirtPrice, array $data = []): TestResponse
{
    $userId = $user->id;

    test()->assertDatabaseHas('users', ['id' => $userId, 'credits' => $expectedStartingCredits]);

    $requestData = array_merge(['flirt' => $flirt->name], $data);
    $response = test()->actingAs($user)->post($route, $requestData);

    if ($response->exception) {
        // Optionally rethrow or handle more explicitly if a test should fail on exception
        // For example, you might want to fail the test immediately:
        // test()->fail("Exception during POST in checkFlirt: " . $response->exception->getMessage());
        // Or rethrow: throw $response->exception;
    }

    $expectedCreditsAfterPost = $expectedStartingCredits - $flirtPrice;
    test()->assertDatabaseHas('users', ['id' => $userId, 'credits' => $expectedCreditsAfterPost]);

    $siteId = User::find($userId)->site_id;
    test()->assertDatabaseHas('messages_' . $siteId, [
        'user_id' => $userId,
        'message' => $flirt->name,
    ]);

    return $response;
}
