<?php

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

beforeEach(function () {
    $this->domain = $this->getDomainFromAppUrl();
    $this->user = User::factory()->create([
        'label_id' => $this->domain->label_id,
        'site_id' => $this->domain->site_id,
        'status' => true,
        'active' => true,
        'confirmed' => true,
        'initial_setup' => true,
    ]);
});

test('resets the password with valid data', function () {
    /** @var User $user */
    $token = Password::createToken($this->user);
    $password = Str::random();

    insertPasswordResetToken($token);

    $route = route('postResetPassword', [
        'token' => $token,
        'email' => $this->user->email,
        'password' => $password,
        'password_confirmation' => $password,
    ]);

    $response = $this->post($route);

    $response->assertRedirect()->assertSessionHas('success');
});

test('does not reset the password with invalid token', function () {
    /** @var User $user */
    $token = Str::random();
    $password = Str::random();

    insertPasswordResetToken(Password::createToken($this->user));

    $route = route('postResetPassword', [
        'token' => $token,
        'email' => $this->user->email,
        'password' => $password,
        'password_confirmation' => $password,
    ]);

    $response = $this->post($route);

    $response->assertRedirect()->assertSessionMissing('success');
});

test('shows the reset password form with valid token', function () {
    $token = Password::createToken($this->user);

    insertPasswordResetToken($token);

    $route = route('reset_password', $token);

    $response = $this->get($route);

    $response->assertSuccessful();
});

function insertPasswordResetToken(string $token): void
{
    DB::table('password_resets')->insert([
        'email' => test()->user->email,
        'token' => $token,
    ]);

    test()->assertDatabaseHas('password_resets', [
        'email' => test()->user->email,
        'token' => $token,
    ]);
}
