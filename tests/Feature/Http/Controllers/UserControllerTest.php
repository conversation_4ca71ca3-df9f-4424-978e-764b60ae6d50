<?php

use App\Http\Controllers\UserController;
use App\Models\Domain;
use App\Models\Profile;
use App\Models\User;
use App\Repository\ApprovalRepository;
use App\Repository\ChatGroupRepository;
use App\Repository\FlirtRepository;
use App\Repository\MessageRepository;
use App\Repository\ProfileRepository;
use App\Repository\RewardInternRepository;
use App\Repository\SettingsRepository;
use App\Repository\TrackRecordRepository;
use App\Repository\UploadRepository;
use App\Repository\UserRepository;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\View;
use Mirzacles\Models\Region;

beforeEach(function () {
    $this->domain = $this->getDomainFromAppUrl();

    // Ensure domain is available through the helper function
    request()->attributes->set('domain', $this->domain);

    $this->app->instance(Domain::class, $this->domain);

    $region = Region::first();
    if (!$region) {
        $this->markTestSkipped('No regions found in the database');
    }

    $this->filters = [
        'online' => [
            'column' => 'online',
            'filter_key' => 'online',
            'values' => [
                [
                    'request_value' => 'on',
                    'column_value' => true,
                ],
            ],
        ],
        'hair_colors' => [
            'column' => 'info.hair_color',
            'filter_key' => 'hair_colors',
            'values' => [
                [
                    'request_value' => 'black',
                    'column_value' => 'black',
                ],
            ],
        ],
        'ethnicity' => [
            'column' => 'info.ethnicity',
            'filter_key' => 'ethnicity',
            'values' => [
                [
                    'request_value' => 'white',
                    'column_value' => 'white',
                ],
            ],
        ],
        'region_id' => [
            'column' => 'info.region_id',
            'filter_key' => 'region_id',
        ],
    ];

    // Create a real user instead of a mock
    $this->user = User::factory()->create([
        'label_id' => $this->domain->label_id,
        'site_id' => $this->domain->site_id,
        'status' => true,
        'active' => true,
        'confirmed' => true,
        'initial_setup' => true,
        'gender' => 'male',
        'seek' => 'female',
    ]);

    // Update user info with valid region_id
    $this->user->info->update([
        'looking_for_age_min' => 18,
        'looking_for_age_max' => 99,
        'birthdate' => '1990-01-01',
        'about' => 'Test bio',
        'region_id' => $region->id,
    ]);

    // Store the region ID for later use
    $this->regionId = $region->id;

    // Mock repositories
    $this->approvalRepository = $this->mock(ApprovalRepository::class);
    $this->messageRepository = $this->mock(MessageRepository::class);
    $this->profileRepository = $this->mock(ProfileRepository::class);
    $this->rewardInternRepository = $this->mock(RewardInternRepository::class);
    $this->userRepository = $this->mock(UserRepository::class);
    $this->uploadRepository = $this->mock(UploadRepository::class);
    $this->settingsRepository = $this->mock(SettingsRepository::class);
    $this->flirtRepository = $this->mock(FlirtRepository::class);
    $this->trackRecordRepository = $this->mock(TrackRecordRepository::class);
    $this->chatGroupRepository = $this->mock(ChatGroupRepository::class);

    $this->userRepository->shouldReceive('setDomain')->andReturnSelf();
    $this->profileRepository->shouldReceive('setDomain')->andReturnSelf();

    // Set up controller
    $this->controller = new UserController(
        $this->approvalRepository,
        $this->messageRepository,
        $this->profileRepository,
        $this->rewardInternRepository,
        $this->userRepository,
        $this->uploadRepository,
        $this->settingsRepository,
        $this->flirtRepository,
        $this->trackRecordRepository,
        $this->chatGroupRepository
    );

    // Set up the domain in the request
    $this->app->bind('domain', function () {
        return $this->domain;
    });

    // Mock thumb function
    $this->app->bind('thumb', function () {
        return 'thumbnail.jpg';
    });
});

it('displays a user profile correctly', function () {
    // Mock user repository method
    $this->userRepository->shouldReceive('getUserInfo')
        ->once()
        ->with($this->user)
        ->andReturn([
            'about' => 'Test bio',
            'region_id' => $this->regionId,
        ]);

    // Create an Eloquent Collection for uploads
    $uploads = new EloquentCollection;

    // Mock upload repository method
    $this->uploadRepository->shouldReceive('getUploadsOfUser')
        ->once()
        ->with($this->user, ['upload'], 6, true)
        ->andReturn($uploads);

    // Mock profile repository method
    $this->profileRepository
        ->shouldReceive('nearbyProfiles')
        ->once()
        ->andReturn(new EloquentCollection([]));

    // Mock request with old() method
    $request = $this->mock(Request::class);
    $request->shouldReceive('old')->andReturn([]);
    $request->shouldReceive('url')->andReturn('/profile');
    $request->shouldReceive('method')->andReturn('GET');
    $request->shouldReceive('user')->andReturn($this->user);

    // Ensure domain is available in the request
    $request->shouldReceive('attributes->get')->with('domain')->andReturn($this->domain);

    // Mock Auth facade
    Auth::shouldReceive('user')
        ->andReturn($this->user);

    // Create a mock View instance instead of returning a string
    $mockView = Mockery::mock(\Illuminate\Contracts\View\View::class);

    // Mock View facade
    View::shouldReceive('make')
        ->once()
        ->with('theme.' . $this->domain->theme . '.user.profile', Mockery::on(function ($arg) {
            return $arg['page_title'] === __('My Profile')
                && isset($arg['info'])
                && isset($arg['uploads'])
                && isset($arg['nearbyProfiles']);
        }))
        ->andReturn($mockView);

    // Execute
    $response = $this->controller->get($request);

    // Assert
    expect($response)->toBe($mockView);
});

it('searches a user with matching username', function () {
    $username = 'testuser123';

    // profile mock setup
    $mockProfile = $this->mock(Profile::class);
    $mockProfile->shouldReceive('getAttribute')->with('id')->andReturn(2);
    $mockProfile->shouldReceive('getAttribute')->with('username')->andReturn($username);
    $mockProfile->shouldReceive('getAttribute')->with('name')->andReturn('Test User');
    $mockProfile->shouldReceive('getAttribute')->with('profile_image')->andReturn('profile.jpg');

    $profileInfo = (object) ['age' => 30];
    $mockProfile->shouldReceive('getAttribute')->with('info')->andReturn($profileInfo);

    // Add __get method for magic property access
    $mockProfile->shouldReceive('__get')->with('id')->andReturn(2);
    $mockProfile->shouldReceive('__get')->with('username')->andReturn($username);
    $mockProfile->shouldReceive('__get')->with('name')->andReturn('Test User');
    $mockProfile->shouldReceive('__get')->with('info')->andReturn($profileInfo);
    $mockProfile->shouldReceive('__get')->with('profile_image')->andReturn('profile.jpg');

    // Also add setAttribute to handle property assignments
    $mockProfile->shouldReceive('setAttribute')->withAnyArgs()->andReturnSelf();

    $mockPaginator = $this->mock(LengthAwarePaginator::class);
    $mockPaginator->shouldReceive('getCollection')
        ->andReturn(collect([$mockProfile]));

    $this->profileRepository->shouldReceive('search')
        ->once()
        ->with($username)
        ->andReturn($mockPaginator);

    // Mock request
    $request = $this->mock(Request::class);
    $request->shouldReceive('ajax')->andReturn(true);
    $request->shouldReceive('get')->with('search', '')->andReturn($username);
    $request->shouldReceive('user')->andReturn($this->user);

    // Ensure domain is available in the request
    $request->shouldReceive('attributes->get')->with('domain')->andReturn($this->domain);

    // Mock functions helper
    $this->app->bind('Functions::isOnline', function () {
        return false;
    });

    // Execute
    $response = $this->controller->search($request);

    // Assert
    $responseData = json_decode($response->getContent(), true);
    expect($responseData)->toBeArray();
    expect($responseData[0]['username'])->toBe($username);
    expect($responseData[0]['age'])->toBe(30);
});

it('redirects when not making an ajax request for searching a user', function () {
    // Mock request without ajax
    $request = $this->mock(Request::class);
    $request->shouldReceive('ajax')->andReturn(false);
    $request->shouldReceive('user')->andReturn($this->user);

    // Ensure domain is available in the request
    $request->shouldReceive('attributes->get')->with('domain')->andReturn($this->domain);

    // Mock Redirect facade
    Redirect::shouldReceive('route')
        ->once()
        ->with('home')
        ->andReturn('redirected');

    // Execute
    $response = $this->controller->search($request);

    // Assert
    expect($response)->toBe('redirected');
});

it('applies filters correctly', function ($filterKey) {
    $filter = $this->filters[$filterKey];
    if ($filterKey === 'region_id') {
        $filterValue = $this->regionId; // Use the valid region ID
        $requestParams = [
            $filter['filter_key'] => $filterValue,
            'age_min' => 18,
            'age_max' => 99,
            'gender' => 'female',
            'page' => 1,
            'order' => 'random',
        ];
    } else {
        $value = $filter['values'][0];
        $requestParams = [
            $filter['filter_key'] => $value['request_value'],
            'age_min' => 18,
            'age_max' => 99,
            'gender' => 'female',
            'page' => 1,
            'order' => 'random',
        ];
    }

    // Create a mock user for this test
    $mockUser = $this->mock(User::class);
    $mockUser->shouldReceive('getAttribute')->with('id')->andReturn($this->user->id);
    $mockUser->shouldReceive('getAttribute')->with('site_id')->andReturn($this->user->site_id);
    $mockUser->shouldReceive('getAttribute')->with('seek')->andReturn('female');

    $userInfo = (object) [
        'looking_for_age_min' => 18,
        'looking_for_age_max' => 99,
        'region' => (object) ['id' => $this->regionId],
    ];
    $mockUser->shouldReceive('getAttribute')->with('info')->andReturn($userInfo);

    // Add __get method for magic property access
    $mockUser->shouldReceive('__get')->with('id')->andReturn($this->user->id);
    $mockUser->shouldReceive('__get')->with('site_id')->andReturn($this->user->site_id);
    $mockUser->shouldReceive('__get')->with('seek')->andReturn('female');
    $mockUser->shouldReceive('__get')->with('info')->andReturn($userInfo);

    // Add offsetExists method for isset() checks
    $mockUser->shouldReceive('offsetExists')->andReturn(true);

    $mockUser->shouldReceive('favoriteProfileIds')->andReturn([]);
    $mockUser->shouldReceive('hasAffiliate')->andReturn(true);

    // Instead of mocking the Request, let's use a real Request object
    $request = Request::create('/members', 'GET', $requestParams);
    $request->setUserResolver(fn () => $mockUser);

    // Ensure domain is available in the request
    $request->attributes->set('domain', $this->domain);

    // Mock Auth facade
    Auth::shouldReceive('user')
        ->andReturn($mockUser);

    // Mock repositories
    $this->profileRepository->shouldReceive('getCachedOnlineProfileIds')
        ->once()
        ->with($mockUser->site_id)
        ->andReturn([]);

    $mockProfiles = new LengthAwarePaginator([], 0, 10);
    $this->profileRepository->shouldReceive('all')
        ->once()
        ->with(Mockery::type(Request::class), $mockUser)
        ->andReturn($mockProfiles);

    // Create a mock View instance instead of returning a string
    $mockView = Mockery::mock(\Illuminate\Contracts\View\View::class);

    // Mock View facade to return the mock View instance
    View::shouldReceive('make')
        ->once()
        ->with('theme.' . $this->domain->theme . '.user.index', Mockery::any())
        ->andReturn($mockView);

    // Execute
    $response = $this->controller->all($request);

    // Assert
    expect($response)->toBe($mockView);
})->with([
    'online',
    'hair_colors',
    'ethnicity',
    'region_id',
]);
