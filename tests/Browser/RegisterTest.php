<?php

use <PERSON><PERSON>\Dusk\Browser;

test('can sign up', function (string $theme) {
    $domain = $this->createDomain($theme);

    $this->artisan('optimize:clear');

    $this->browse(function (Browser $browser) use ($theme, $domain) {
        $browser->driver->manage()->deleteAllCookies();
        $browser->maximize()->visit('/');

        if ($theme === '001') {
            // Pause 1000ms  so the age verify age popup has been rendered
            $browser->pause(1000)->check('#checkVerifyAge')->click('@age-verify-agree-button');
        } elseif ($theme !== '003') {
            $browser->select('gender', 'male')->select('seek', 'female');
        }

        if ($theme === '004') {
            $browser->press('@open-registration-popup')
                ->pause(1000)
                ->select('birth_month', '01')
                ->select('birth_day', '01')
                ->select('birth_year', '1994');
        }

        $browser
            ->select('@region-select', $domain->country->regions->first()->id)
            ->type('@email', self::TEST_USER_EMAIL)
            ->type('username', 'duskmen')
            ->type('@pw', 'test123')
            ->check('terms')
            ->press($theme === '004' ? 'Sign Up Now!' : 'Sign up for free!')
            ->screenshot($theme . '_activation_wall')
            ->assertUrlIs(route('activation'));
    });
})->with('themes');
