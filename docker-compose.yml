version: '3.8'
services:
  client:
    container_name: l6-clients-nginx
    image: nginx:1.17
    ports:
      - "8081:80"
    volumes:
      - ./:/app:cached
      - ./docker/nginx/config/platform-api.nl.conf:/etc/nginx/conf.d/default.conf
      - ./docker/nginx/config/nginx.conf:/etc/nginx/nginx.conf
      - ../l6-assets/theme/:/app/public/theme
      - common-containers_uploads:/app/public/uploads
    links:
      - app
    environment:
      - "VIRTUAL_HOST=*.clients.test"
    networks:
      - common-containers_development

  app:
    build:
      context: 'docker/app'
      args:
        PHP_IMAGE_NAME: "${PHP_IMAGE_NAME}"
        NODE_VERSION: "${NODE_VERSION}"
        DOCKER_LOCAL_USER: "${DOCKER_LOCAL_USER}"
        DOCKER_LOCAL_WORKDIR: "${DOCKER_LOCAL_WORKDIR}"
        TIME_ZONE: "${APP_TIMEZONE}"
    container_name: l6-clients-app
    ports:
      - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
      - '${CHROME_PORT:-9515}:${CHROME_PORT:-9515}'
    volumes:
      - ./:/app:cached
      - ~/.ssh:/root/.ssh:ro
      - ~/.ssh:/home/<USER>/.ssh:ro
      - common-containers_uploads:/app/public/uploads
    working_dir: /app
    extra_hosts:
      - operators.test:host-gateway
    networks:
      - common-containers_development

networks:
  common-containers_development:
    external: true

volumes:
  common-containers_uploads:
    external: true
