<?php

use App\Http\Controllers\Api\AmpEmailController;
use App\Http\Controllers\Api\CampaignsOrdersController;
use App\Http\Controllers\Api\ChatToolController;
use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\ExternalDomainsController;
use App\Http\Controllers\Api\MicropaymentController;
use App\Http\Controllers\Api\Operators\ConversationController;
use App\Http\Controllers\Api\Operators\ConversationMessagesController;
use App\Http\Controllers\Api\Operators\IncomingReplyController;
use App\Http\Controllers\Api\Operators\NoteController;
use App\Http\Controllers\Api\Operators\ReportMemberController;
use App\Http\Controllers\Api\Operators\SearchNotesController;
use App\Http\Controllers\Api\Operators\UpgradeMemberController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\PaymentVerificationController;
use App\Http\Controllers\Api\ProfileController;
use App\Http\Controllers\Api\RegisterController;
use App\Http\Controllers\Api\ReviewController;
use App\Http\Controllers\Api\SettingController;
use App\Http\Controllers\Api\TranslationController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\CCBillController;
use App\Http\Controllers\CelerispayController;
use App\Http\Controllers\GetVolumeController;
use App\Http\Controllers\NetValveController;
use App\Http\Controllers\SegPayController;
use App\Http\Controllers\VerotelController;
use App\Http\Middleware\AuthenticateByToken;
use Illuminate\Support\Facades\Route;

Route::middleware(['api', 'signup.after'])->post('/signup', [RegisterController::class, 'register'])->name('apiSignup');
Route::post('chattool-incoming', [ChatToolController::class, 'addMessage'])->name('chattoolIncoming');

Route::post('celerispay/notification', [CelerispayController::class, 'notification'])->name('apiPayCelerispayNotification');
Route::post('netvalve/notification', [NetValveController::class, 'notification'])->name('apiNetvalveNotification');
Route::get('verotel/notification', [VerotelController::class, 'notification']);

Route::as('operators.')->prefix('operators')->middleware('api')->group(function () {
    Route::post('incoming/reply', IncomingReplyController::class)->name('incoming.reply');
    Route::match(['GET', 'POST'], 'conversations/{id}', [ConversationController::class, 'show'])->name('conversations.show');
    Route::match(['GET', 'POST'], 'conversations/messages/load-more', [ConversationMessagesController::class, 'loadMoreMessages'])->name('conversations.messages.more');
    Route::resource('notes', NoteController::class)->only(['store', 'update']);
    Route::post('notes/search', SearchNotesController::class)->name('notes.search');
    Route::post('report-member', ReportMemberController::class)->name('report-member');
    Route::post('upgrade-member', UpgradeMemberController::class)->name('upgrade-member');
});

Route::middleware(['api', 'auth.basic'])->prefix('/v1')->group(function () {
    Route::get('/', [DashboardController::class, 'dashboard']);
    Route::get('/orders', [OrderController::class, 'all']);
});

Route::post('credits/segpay/postback', [SegPayController::class, 'segPayPostback'])->name('segPayPostback')->middleware('segpay');
Route::post('credits/segpay/notification', [SegPayController::class, 'segPayNotification'])->name('segPayNotification')->middleware('segpay');
Route::match(['GET', 'POST'], 'credits/segpay/paypal', [SegPayController::class, 'segPayPaypalPostback'])->name('segPayPaypalPostback');
Route::get('credits/micropayment/return', [MicropaymentController::class, 'micropaymentNotification'])->name('micropaymentNotification');
Route::match(['GET', 'POST', 'PUT'], 'credits/getvolume/notification', [GetVolumeController::class, 'notification'])->name('getVolumeNotification');

Route::middleware(['auth.partner'])->group(function () {
    Route::post('/campaigns/orders', [CampaignsOrdersController::class, 'order'])->name('campaignsOrder');
});

Route::get('unsubscribe-emails/{email}/{user_id?}', [SettingController::class, 'unsubscribeInstant'])->name('unsubscribeEmails');

// Clear cache
Route::post('/clear-cache', [\App\Http\Controllers\SettingController::class, 'clearCache']);
Route::post('/clear-views', [\App\Http\Controllers\SettingController::class, 'clearViews']);

// Clear cache for the Topol Mail Templates
Route::post('/clear-topol-cache', [\App\Http\Controllers\SettingController::class, 'clearTopolCache']);

// Translations
Route::post('/translations', [TranslationController::class, 'getTranslations']);

// PaymentVerificationController
Route::post('/payment/verify', [PaymentVerificationController::class, 'verify']);

Route::match(['GET', 'POST'], 'ccbill/return', [CCBillController::class, 'postback'])->name('ccbillpostback');
Route::match(['GET', 'POST'], 'ccbill/denial', [CCBillController::class, 'denial'])->name('ccbillpostbackdenial');
Route::match(['GET', 'POST'], 'ccbill/webhook', [CCBillController::class, 'webhook'])->name('ccbillwebhook');

// External admin2 requests
Route::middleware('admin.auth')->group(function () {
    Route::apiResource('/users', UserController::class);
    Route::apiResource('/reviews', ReviewController::class);
    Route::apiResource('/users', UserController::class);
    Route::get('/users-count', [UserController::class, 'getUsersCount']);
    Route::get('external-domains/wonderpush', [ExternalDomainsController::class, 'listWonderpush']);
    Route::get('external-domains/companies', [ExternalDomainsController::class, 'listCompanies']);
    Route::get('external-domains/countries', [ExternalDomainsController::class, 'listCountries']);
    Route::get('external-domains/{url}', [ExternalDomainsController::class, 'getDomainByUrl']);
    Route::resource('external-domains', ExternalDomainsController::class);
    Route::apiResource('/profiles', ProfileController::class);
    Route::get('/profiles-count', [ProfileController::class, 'getProfilesCount']);
});

Route::prefix('amp')->group(function () {
    Route::get('/get-message/{message_id}', [AmpEmailController::class, 'getMessage'])->name('ampEmailGetMessage');
    Route::post('/add-message/{user_id}/{profile_id}', [AmpEmailController::class, 'addMessage'])->middleware(AuthenticateByToken::class)->name('ampEmailAddMessage');
});
