[{"aliases": ["mvsecure", "securelink", "securelinkuk", "securelinkau", "securelinkus"], "ips": ["*************", "************", "**************", "************", "*************", "************", "**************", "*************", "**************", "*************", "*************", "************", "**************", "*************", "************4", "************", "*************", "*************", "*************", "**************", "*************", "************", "*************", "*************", "*************", "************", "**************", "************", "**************", "************", "*************", "*************", "*************", "*************", "**************", "**************", "*************", "*************", "*************", "***********", "*************", "************", "*************", "*************", "*************", "**************", "*************", "*************", "************"]}, {"aliases": ["securereg", "secureregie", "securereguk", "secureregde", "secureregat", "securereg<PERSON>", "secureregca", "secureregau", "<PERSON><PERSON><PERSON>"], "ips": ["***********", "************", "*************", "*************", "*************"]}, {"aliases": ["twenty1"], "ips": ["*************", "*************", "**************"]}, {"aliases": ["admiit", "admiit2", "admiie", "admiie2", "admica", "admica2", "admiau", "admiau2", "admius", "admius2"], "ips": ["***********", "***********", "***********", "**********", "************", "***********", "***********", "***********"]}, {"aliases": ["wearemedia", "wamit", "wamat", "wamch", "wamde", "wamau"], "ips": ["**************", "************", "***********", "************", "************", "*************", "**************", "**************", "***********", "************", "************", "*************", "**************"]}, {"aliases": ["7clickit", "7<PERSON><PERSON><PERSON>", "7clickuk", "7clickde", "7clickca", "7<PERSON>lickaus", "7<PERSON><PERSON><PERSON>"], "ips": ["*************", "**************", "**************"]}, {"aliases": ["<PERSON><PERSON><PERSON>", "lospollosie", "lospollosat", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "ips": ["***********", "************", "**********", "***************", "*************"]}, {"aliases": ["c<PERSON><PERSON><PERSON><PERSON>", "crakus"], "ips": ["*************", "**************", "************", "*************", "*************", "************", "************"]}, {"aliases": ["trkadvus", "trkadvit", "trkadvie", "trkadvuk", "trkadvde", "trkadvca", "trkadvau", "trkadvus"], "ips": ["**************", "***********", "**********", "*************", "************"]}, {"aliases": ["<PERSON><PERSON><PERSON>", "ad<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "adredit"], "ips": ["*************", "**************", "104.21.75.70", "159.65.243.54", "***********", "************", "************", "*************", "**************"]}, {"aliases": ["<PERSON><PERSON><PERSON>", "medialirevext", "medialiprofit", "medialiprofitext", "mediali", "mediali2"], "ips": ["67.205.156.87", "178.137.34.230"]}, {"aliases": ["itrk", "<PERSON>rkie", "itrkde", "itrka<PERSON>"], "ips": ["13.36.149.190", "35.180.14.57"]}, {"aliases": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "ips": ["172.255.251.244", "172.255.97.172"]}, {"aliases": ["inspiravit", "inspiratrack", "inspirat<PERSON><PERSON>", "gigatrk"], "ips": ["45.146.57.239", "18.158.58.250", "3.64.98.191", "82.197.211.197", "158.58.250", "3.64.98.191"]}, {"aliases": ["m<PERSON><PERSON>"], "ips": ["162.214.173.27"]}, {"aliases": ["clickdealer", "cdr", "cdrev", "cdrev2", "cdat", "cdch", "cddoi", "cdsoi"], "ips": ["54.157.69.70", "34.232.158.78", "52.5.241.89", "52.3.48.42", "13.127.56.254", "35.154.201.151", "13.127.155.23", "13.127.97.64", "34.245.244.183", "52.18.240.19", "34.245.255.244", "46.51.198.159", "54.229.76.119", "52.20.127.90", "54.157.69.70", "34.232.158.78", "52.5.241.89", "52.3.48.42", "13.127.56.254", "35.154.201.151", "13.127.155.23", "13.127.97.64", "34.245.244.183", "52.18.240.19", "34.245.255.244", "46.51.198.159", "54.229.76.119", "52.20.127.90", "193.110.100.93"]}, {"aliases": ["wtrafficit", "wtrafficuk", "w<PERSON><PERSON><PERSON><PERSON>", "wtrafficat", "wtraff<PERSON>ch", "wtrafficca", "wtrafficau", "w<PERSON>ff<PERSON>"], "ips": ["68.169.80.163", "68.169.80.167", "68.169.80.166", "68.169.80.164"]}, {"aliases": ["rhit", "rhuk", "rhie", "rhde", "rhat", "rhch", "rhca", "rhau", "rhus", "rhcpl"], "ips": ["142.4.10.89", "44.239.31.180", "23.19.74.164", "11.0.2.0", "52.44.35.191", "199.250.204.200"]}, {"aliases": ["approachx"], "ips": ["2604:a880:400:d0::170c:7001", "167.172.137.172", "23.130.192.130"]}, {"aliases": ["trafficcompany"], "ips": ["92.68.14.88", "92.68.14.95", "176.34.129.175", "52.213.196.103", "34.241.16.129"]}, {"aliases": ["safetrkit"], "ips": ["87.166.219.130", "91.220.37.48", "217.62.50.198", "91.220.37.236", "91.220.37.237"]}, {"aliases": ["matemapuk", "matemapde", "matemapat", "matemapch", "matemapca", "matemapa<PERSON>", "mate<PERSON><PERSON>"], "ips": ["142.116.111.159", "81.169.181.179", "99.192.196.10"]}, {"aliases": ["mindscrapersit", "mindscrapersuk", "mindscrapersdach", "mindscrapersat", "mindscrapersch", "mindscrapersca", "mindscrapersaus", "mindscrapersus", "msde", "minds"], "ips": ["78.47.44.41", "138.201.186.67"]}, {"aliases": ["soyankit", "soyankuk", "soyankie", "soyank<PERSON>", "soyankat", "soyankch", "soyankca", "<PERSON><PERSON><PERSON><PERSON>", "soyan<PERSON>"], "ips": ["149.202.76.60"]}, {"aliases": ["geoeit", "geoeuk", "geoeie", "geoede", "geoeat", "geoech", "geoeca", "geoeau", "geoeus"], "ips": ["63.33.101.119"]}, {"aliases": ["affmy"], "ips": ["185.98.53.4"]}, {"aliases": ["blackorange", "blackorangeit", "black<PERSON><PERSON>e"], "ips": ["185.81.52.141", "91.199.255.0", "91.199.255.1", "91.199.255.2", "91.199.255.3", "91.199.255.4", "91.199.255.5", "91.199.255.6", "91.199.255.7", "91.199.255.8", "91.199.255.9", "91.199.255.10", "91.199.255.11", "91.199.255.12", "91.199.255.13", "91.199.255.14", "91.199.255.15", "91.199.255.16", "91.199.255.17", "91.199.255.18", "91.199.255.19", "91.199.255.20", "91.199.255.21", "91.199.255.22", "91.199.255.23", "91.199.255.24", "91.199.255.25", "91.199.255.26", "91.199.255.27", "91.199.255.28", "91.199.255.29", "91.199.255.30", "91.199.255.31", "91.199.255.32", "91.199.255.33", "91.199.255.34", "91.199.255.35", "91.199.255.36", "91.199.255.37", "91.199.255.38", "91.199.255.39", "91.199.255.40", "91.199.255.41", "91.199.255.42", "91.199.255.43", "91.199.255.44", "91.199.255.45", "91.199.255.46", "91.199.255.47", "91.199.255.48", "91.199.255.49", "91.199.255.50", "91.199.255.51", "91.199.255.52", "91.199.255.53", "91.199.255.54", "91.199.255.55", "91.199.255.56", "91.199.255.57", "91.199.255.58", "91.199.255.59", "91.199.255.60", "91.199.255.61", "91.199.255.62", "91.199.255.63", "91.199.255.64", "91.199.255.65", "91.199.255.66", "91.199.255.67", "91.199.255.68", "91.199.255.69", "91.199.255.70", "91.199.255.71", "91.199.255.72", "91.199.255.73", "91.199.255.74", "91.199.255.75", "91.199.255.76", "91.199.255.77", "91.199.255.78", "91.199.255.79", "91.199.255.80", "91.199.255.81", "91.199.255.82", "91.199.255.83", "91.199.255.84", "91.199.255.85", "91.199.255.86", "91.199.255.87", "91.199.255.88", "91.199.255.89", "91.199.255.90", "91.199.255.91", "91.199.255.92", "91.199.255.93", "91.199.255.94", "91.199.255.95", "91.199.255.96", "91.199.255.97", "91.199.255.98", "91.199.255.99", "91.199.255.100", "91.199.255.101", "91.199.255.102", "91.199.255.103", "91.199.255.104", "91.199.255.105", "91.199.255.106", "91.199.255.107", "91.199.255.108", "91.199.255.109", "91.199.255.110", "91.199.255.111", "91.199.255.112", "91.199.255.113", "91.199.255.114", "91.199.255.115", "91.199.255.116", "91.199.255.117", "91.199.255.118", "91.199.255.119", "91.199.255.120", "91.199.255.121", "91.199.255.122", "91.199.255.123", "91.199.255.124", "91.199.255.125", "91.199.255.126", "91.199.255.127", "91.199.255.128", "91.199.255.129", "91.199.255.130", "91.199.255.131", "91.199.255.132", "91.199.255.133", "91.199.255.134", "91.199.255.135", "91.199.255.136", "91.199.255.137", "91.199.255.138", "91.199.255.139", "91.199.255.140", "91.199.255.141", "91.199.255.142", "91.199.255.143", "91.199.255.144", "91.199.255.145", "91.199.255.146", "91.199.255.147", "91.199.255.148", "91.199.255.149", "91.199.255.150", "91.199.255.151", "91.199.255.152", "91.199.255.153", "91.199.255.154", "91.199.255.155", "91.199.255.156", "91.199.255.157", "91.199.255.158", "91.199.255.159", "91.199.255.160", "91.199.255.161", "91.199.255.162", "91.199.255.163", "91.199.255.164", "91.199.255.165", "91.199.255.166", "91.199.255.167", "91.199.255.168", "91.199.255.169", "91.199.255.170", "91.199.255.171", "91.199.255.172", "91.199.255.173", "91.199.255.174", "91.199.255.175", "91.199.255.176", "91.199.255.177", "91.199.255.178", "91.199.255.179", "91.199.255.180", "91.199.255.181", "91.199.255.182", "91.199.255.183", "91.199.255.184", "91.199.255.185", "91.199.255.186", "91.199.255.187", "91.199.255.188", "91.199.255.189", "91.199.255.190", "91.199.255.191", "91.199.255.192", "91.199.255.193", "91.199.255.194", "91.199.255.195", "91.199.255.196", "91.199.255.197", "91.199.255.198", "91.199.255.199", "91.199.255.200", "91.199.255.201", "91.199.255.202", "91.199.255.203", "91.199.255.204", "91.199.255.205", "91.199.255.206", "91.199.255.207", "91.199.255.208", "91.199.255.209", "91.199.255.210", "91.199.255.211", "91.199.255.212", "91.199.255.213", "91.199.255.214", "91.199.255.215", "91.199.255.216", "91.199.255.217", "91.199.255.218", "91.199.255.219", "91.199.255.220", "91.199.255.221", "91.199.255.222", "91.199.255.223", "91.199.255.224", "91.199.255.225", "91.199.255.226", "91.199.255.227", "91.199.255.228", "91.199.255.229", "91.199.255.230", "91.199.255.231", "91.199.255.232", "91.199.255.233", "91.199.255.234", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************"]}, {"aliases": ["virtux", "virtuxat", "virtuxch", "virtuxde", "virtuxrev", "virtuxrevat", "virtuxrevch", "virtuxrevde"], "ips": ["**************", "************", "*************"]}, {"aliases": ["capetrack", "cape<PERSON>ie"], "ips": ["*************", "*************", "************", "***********", "***********", "**************", "**************", "*************", "*************", "**************", "*************", "*************"]}]