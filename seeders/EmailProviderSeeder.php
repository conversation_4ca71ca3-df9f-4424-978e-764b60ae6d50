<?php

namespace Database\Seeders;

use App\Models\EmailProvider;
use Illuminate\Database\Seeder;

class EmailProviderSeeder extends Seeder
{
    public function run(): void
    {
        $path = database_path('seeders/data/email_providers.json');
        $email_providers = json_decode(file_get_contents($path), true);

        foreach ($email_providers as $email_provider) {
            EmailProvider::updateOrCreate(['provider' => $email_provider]);
        }
    }
}
