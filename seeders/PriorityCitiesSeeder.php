<?php

namespace Database\Seeders;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Seeder;
use Mirzacles\Models\City;
use Mirzacles\Models\Country;

class PriorityCitiesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $country = Country::where('name', 'United States')->first();

        $cities = [
            'Los Angeles' => 'California',
            'New York' => 'New York',
            'Washington' => 'D.C.',
            'Clarksville' => 'Tennessee',
            'Chicago' => 'Illinois',
            'Dallas' => 'Texas',
            'Nashville' => 'Tennessee',
            'Frisco' => 'Texas',
            'Philadelphia' => 'Pennsylvania',
            'Houston' => 'Texas',
            'Charlotte' => 'North Carolina',
            'Atlanta' => 'Georgia',
            'Orlando' => 'Florida',
            'Seattle' => 'Washington',
            'Denver' => 'Colorado',
            'Hagerstown' => 'Maryland',
            'Phoenix' => 'Arizona',
            'Austin' => 'Texas',
            'Sacramento' => 'California',
            'Las Vegas' => 'Nevada',
            'Detroit' => 'Michigan',
            'Minneapolis' => 'Minnesota',
            'Boston' => 'Massachusetts',
            'Baltimore' => 'Maryland',
            'Andover' => 'Minnesota',
            'Allentown' => 'Pennsylvania',
            'Indianapolis' => 'Indiana',
            'Ontario' => 'California',
            'Allen' => 'Texas',
            'San Antonio' => 'Texas',
            'Portland' => 'Oregon',
            'Madison' => 'Wisconsin',
            'Tampa' => 'Florida',
            'San Francisco' => 'California',
            'Miami' => 'Florida',
            'Coon Rapids' => 'Minnesota',
            'Memphis' => 'Tennessee',
            'Oklahoma City' => 'Oklahoma',
            'Cleveland' => 'Ohio',
            'Jacksonville' => 'Florida',
            'San Diego' => 'California',
            'Louisville' => 'Kentucky',
            'Salt Lake City' => 'Utah',
            'Coffeyville' => 'Kansas',
            'Raleigh' => 'North Carolina',
            'Springfield' => 'Missouri',
            'Pittsburgh' => 'Pennsylvania',
            'Milwaukee' => 'Wisconsin',
            'Birmingham' => 'Alabama',
            'St. Louis' => 'Missouri',
            'Greenville' => 'North Carolina',
            'Omaha' => 'Nebraska',
            'Albuquerque' => 'New Mexico',
            'Kansas City' => 'Kansas',
            'Tulsa' => 'Oklahoma',
            'Des Moines' => 'Iowa',
            'Little Rock' => 'Arkansas',
            'Sioux Falls' => 'South Dakota',
            'San Jose' => 'California',
            'New Orleans' => 'Louisiana',
            'Cheyenne' => 'Wyoming',
            'Fort Worth' => 'Texas',
            'Irving' => 'Texas',
            'Hialeah' => 'Florida',
            'Cincinnati' => 'Ohio',
            'Yonkers' => 'New York',
            'Chesapeake' => 'Virginia',
            'Sterling Heights' => 'Michigan',
            'Spokane' => 'Washington',
            'Otsego' => 'Minnesota',
            'Grand Rapids' => 'Michigan',
            'Richmond' => 'Virginia',
            'El Paso' => 'Texas',
            'Colorado Springs' => 'Colorado',
            'Cary' => 'North Carolina',
            'Macon' => 'Georgia',
            'Savannah' => 'Georgia',
            'Bowling Green' => 'Kentucky',
            'Quincy' => 'Washington',
            'Paterson' => 'New Jersey',
            'Tucson' => 'Arizona',
            'Jackson' => 'Mississippi',
            'Saint Cloud' => 'Minnesota',
            'Medford' => 'Oregon',
            'Virginia Beach' => 'Virginia',
            'Honolulu' => 'Hawaii',
            'Saint Paul' => 'Minnesota',
            'Greensburg' => 'Pennsylvania',
            'Knoxville' => 'Tennessee',
            'Greensboro' => 'North Carolina',
            'Mesa' => 'Arizona',
            'Norfolk' => 'Virginia',
            'Oxnard' => 'California',
            'Syracuse' => 'New York',
            'Thousand Oaks' => 'California',
            'Hampton' => 'Virginia',
            'Bloomfield' => 'Connecticut',
            'Anchorage' => 'Alaska',
            'Fresno' => 'California',
            'Altoona' => 'Pennsylvania',
            'Minot' => 'North Dakota',
            'Tuscaloosa' => 'Alabama',
            'Kenosha' => 'Wisconsin',
            'Independence' => 'Missouri',
            'Manchester' => 'New Hampshire',
            'Forest Park' => 'Georgia',
            'Mobile' => 'Alabama',
            'Parma' => 'Ohio',
            'Ann Arbor' => 'Michigan',
            'Charleston' => 'West Virginia',
            'Bakersfield' => 'California',
            'Wichita' => 'Kansas',
            'Lexington' => 'Kentucky',
            'Athens' => 'Georgia',
            'Wichita Falls' => 'Texas',
            'Lubbock' => 'Texas',
            'Cape Coral' => 'Florida',
            'Baton Rouge' => 'Louisiana',
            'Santa Barbara' => 'California',
            'Aurora' => 'Colorado',
            'Bloomington' => 'Indiana',
            'Montgomery' => 'Alabama',
            'Reno' => 'Nevada',
            'Huntsville' => 'Alabama',
            'Riverside' => 'California',
            'South Bend' => 'Indiana',
            'Roseville' => 'California',
            'Albany' => 'New York',
            'Lincoln' => 'Nebraska',
            'Boise' => 'Idaho',
            'Davenport' => 'Iowa',
            'Owensboro' => 'Kentucky',
            'Joliet' => 'Illinois',
            'Gilbert' => 'Arizona',
            'Columbus' => 'Indiana',
            'Four Corners' => 'Florida',
            'Naperville' => 'Illinois',
            'Roanoke' => 'Virginia',
            'Clinton Township' => 'Michigan',
            'Green Bay' => 'Wisconsin',
            'Murfreesboro' => 'Tennessee',
            'Cheektowaga' => 'New York',
            'Corpus Christi' => 'Texas',
            'Long Beach' => 'California',
            'Irvine' => 'California',
            'Vancouver' => 'Washington',
            'Rochester' => 'New York',
            'Stockton' => 'California',
            'Hazel Dell North' => 'Washington',
            'Kings Point' => 'Florida',
            'Clifton' => 'New Jersey',
            'Santa Clara' => 'California',
            'Fayetteville' => 'Arkansas',
            'Pigeon Forge' => 'Tennessee',
            'Saint Charles' => 'Missouri',
            'Augusta' => 'Georgia',
            'Toledo' => 'Ohio',
            'Metairie' => 'Louisiana',
            'Suffolk' => 'Virginia',
            'Tacoma' => 'Washington',
            'Akron' => 'Ohio',
            'Dalton' => 'Georgia',
            'Eugene' => 'Oregon',
            'Fort Wayne' => 'Indiana',
            'Oakland' => 'California',
            'Port Saint Lucie' => 'Florida',
            'Newark' => 'New Jersey',
            'Modesto' => 'California',
            'Chattanooga' => 'Tennessee',
            'Miami Beach' => 'Florida',
            'Spring Hill' => 'Florida',
            'Tallahassee' => 'Florida',
            'Buffalo' => 'New York',
            'Columbia' => 'Missouri',
            'Durham' => 'North Carolina',
            'The Villages' => 'Florida',
            'Grand Prairie' => 'Texas',
            'Evansville' => 'Indiana',
            'Lakeland' => 'Florida',
            'San Bernardino' => 'California',
            'St. Petersburg' => 'Florida',
            'Lancaster' => 'Pennsylvania',
            'Nampa' => 'Idaho',
            'Fargo' => 'North Dakota',
            'Anaheim' => 'California',
            'Arlington' => 'Virginia',
            'Fort Lauderdale' => 'Florida',
            'Garland' => 'Texas',
            'Pembroke Pines' => 'Florida',
            'Fort Collins' => 'Colorado',
            'Everett' => 'Washington',
            'Alexandria' => 'Virginia',
            'Chandler' => 'Arizona',
            'North Charleston' => 'South Carolina',
            'Kingsport' => 'Tennessee',
            'Scottsdale' => 'Arizona',
            'Amarillo' => 'Texas',
            'Canton' => 'Ohio',
            'Saint Michael' => 'Minnesota',
            'Chesterfield' => 'Missouri',
            'Plano' => 'Texas',
            'Salem' => 'Oregon',
            'Gulfport' => 'Mississippi',
            'Newport News' => 'Virginia',
            'Chula Vista' => 'California',
            'Winston-Salem' => 'North Carolina',
            'Concord' => 'North Carolina',
            'Costa Mesa' => 'California',
            'Rancho Cucamonga' => 'California',
            'Coral Springs' => 'Florida',
            'Cedar Rapids' => 'Iowa',
            'Jersey City' => 'New Jersey',
            'Hemet' => 'California',
            'Shreveport' => 'Louisiana',
            'Stockbridge' => 'Georgia',
            'Germantown' => 'Maryland',
            'Dayton' => 'Ohio',
            'Peoria' => 'Arizona',
            'Henderson' => 'Nevada',
            'Fort Smith' => 'Arkansas',
            'Huntington' => 'West Virginia',
        ];

        $found = 0;
        foreach ($cities as $name => $region) {
            $city = City::query()
                ->where('name', $name)
                ->whereHas('region', function (Builder $query) use ($country, $region) {
                    $query->where('country_id', $country->id);
                    $query->where('name', $region);
                })
                ->first();

            if ($city) {
                $city->update(['priority' => 1]);
                $found += 1;
            } else {
                echo 'Not found ' . $name . ' / ' . $region . "\r\n";
            }
        }

        echo 'Found ' . $found . ' of ' . count($cities);
    }
}
