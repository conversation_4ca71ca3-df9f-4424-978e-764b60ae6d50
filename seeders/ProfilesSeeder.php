<?php

namespace Database\Seeders;

use App\Models\Domain;
use App\Models\Profile;
use App\Models\ProfileInfo;
use Database\Support\SeedHelper;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;
use Mirzacles\Models\Upload;
use phpDocumentor\Reflection\DocBlock\Tags\See;
use Throwable;

class ProfilesSeeder extends Seeder
{
    public function run()
    {
        if (!in_array(config('app.env'), ['local'])) {
            return;
        }

        $domains = Domain::select('site_id')->groupBy('site_id')->pluck('site_id');
        $profiles = SeedHelper::getData('profiles.json');

        $limit = 100;

        $bar = $this->command->getOutput()->createProgressBar(count($domains) * ($limit ? $limit : count($profiles)));

        foreach ($domains as $site_id) {
            foreach($profiles as $index => $data) {
                if ($index >= $limit) {
                    break;
                }

                $data['profile']['site_id'] = $site_id;

                $profile = Profile::factory()->create($data['profile']);
                $profile_info = ProfileInfo::factory()->for($profile)->create($data['profile_info']);

                foreach ($data['images'] as $type => $upload) {

                    if ($type == 'message') {
                        $type = Upload::TYPE_PRIVATE;
                    }

                    Upload::create([
                        'member_id' => $profile->id,
                        'type' => $type,
                        'file' => $upload,
                        'site_id' => $profile->site_id,
                        'active' => true,
                        'is_approved' => true,
                    ]);

                    $this->downloadImage($upload);
                }
                $bar->advance();
            }
        }
        $bar->finish();
    }

    public function downloadImage($image)
    {
        $url = 'https://content.adultdating.date/uploads/';
        $storage = Storage::disk('public_uploads');
        $size = null;

        if ($storage->exists($image)) {
            return;
        }

        $headers = $this->parseHeaders(get_headers($url . $image));

        if (Str::contains($headers['Response'], '302') && Str::startsWith($image, 'crop/')) {
            $image = Str::after($image, 'crop/');
            $headers = $this->parseHeaders(get_headers($url . $image));
        }

        if (isset($headers['Content-Length'])) {
            $size = (int) $headers['Content-Length'];
        } else {
            return;
        }

        try {
            $imageContent = file_get_contents($url . $image);
            $storage->put($image, $imageContent);

            $existing_size = $storage->size($image);
            if ($size !== $existing_size) {
                $storage->delete($image);
            }
        } catch (\Throwable $th) {}

        if ($storage->exists($image)) {
            $this->generateThumbnails($image);
        }
    }

    public function parseHeaders($headers)
    {
        $parsed = [];
        foreach ($headers as $i => $h) {
            if ($i == 0) {
                $parsed['Response'] = $h;
            } else {
                $parts = explode(':', $h, 2);
                $parsed[$parts[0]] = isset($parts[1]) ? trim($parts[1]) : '';
            }
        }

        return $parsed;
    }

    private function generateThumbnails($filename): void
    {
        $uploadPath = public_path('uploads') . '/';
        $thumbSizes = [
            '150' => 150,
            'thumbs' => 400,
        ];
        $thumbnail_quality = 80;


        foreach ($thumbSizes as $folder => $size) {
            if (!file_exists($uploadPath . $folder)) {
                mkdir($uploadPath . $folder, 0777, true);
                mkdir($uploadPath . $folder . '/crop', 0777, true);
            }
        }

        if (empty($filename) || !file_exists($uploadPath . $filename)) {
            return;
        }

        try {
            $image = Image::make($uploadPath . $filename);
        } catch (Throwable $th) {
            return;
        }

        foreach ($thumbSizes as $folder => $size) {
            $thumb_path = $uploadPath . $folder . '/' . $filename;

            if (!file_exists($thumb_path)) {
                $image->resize($size, null, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                })
                    ->sharpen(5)
                    ->save($thumb_path, $thumbnail_quality);
            }
        }
    }

}
