<?php

namespace Database\Seeders;

use App\Models\Domain;
use App\Models\ProductSubscription;
use Illuminate\Database\Seeder;

class SubscriptionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $domains = Domain::all();

        $subscriptions = $this->getSubscriptions();

        foreach ($domains as $domain) {
            if (!$domain->use_subscriptions) {
                continue;
            }

            foreach ($subscriptions as $subscription) {
                ProductSubscription::firstOrCreate(
                    [
                        'domain_id' => $domain->id,
                        'initial_period' => $subscription['initial_period'],
                        'trial' => $subscription['trial'] ?? 0,
                    ],
                    $subscription
                );
            }
        }
    }

    public function getSubscriptions()
    {
        return [
            [
                'initial_period' => 30,
                'initial_price' => 0,
                'recurring_period' => 30,
                'recurring_price' => 1299,
                'rebills' => 99,
                'lifetime' => 1,
                'trial' => 1,
                'credits' => 10,
            ],
            [
                'initial_period' => 30,
                'initial_price' => 1299,
                'recurring_period' => 30,
                'recurring_price' => 1299,
                'rebills' => 99,
                'lifetime' => 1,
                'credits' => 10,
            ],
            [
                'initial_period' => 60,
                'initial_price' => 2198,
                'recurring_period' => 60,
                'recurring_price' => 2198,
                'rebills' => 99,
                'lifetime' => 1,
                'credits' => 10,
            ],
            [
                'initial_period' => 90,
                'initial_price' => 2697,
                'recurring_period' => 90,
                'recurring_price' => 2697,
                'rebills' => 99,
                'lifetime' => 1,
                'credits' => 10,
            ],
        ];
    }
}
