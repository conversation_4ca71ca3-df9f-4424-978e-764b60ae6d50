<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ThemeSeeder extends Seeder
{
    public function run()
    {
        DB::table('themes')->insert($this->getThemes());
    }

    /**
     * @return array[]
     */
    private function getThemes(): array
    {
        $date = Carbon::now();

        return [
            [
                'id' => Str::orderedUuid(),
                'name' => 'bumsplatz',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'casualmatchclub',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'chavsgowild',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'de3',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'dirtychavs',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'flirtyslapper',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'france',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'francegreen',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'francepink',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'italy',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'italygreen',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'italypink',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'kiwifling',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'milfcooper',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'myflirtymate',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'ozziebang',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'regionmatch',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'schlampeplatz',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'secondfling',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'sensualmatchclub',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'sextingbook',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'sextingplatz',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'sextingwelt',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'shaglicious',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'shagslags',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'slagnextdoor',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'slagplace',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'slagsandbeer',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'snapundfick',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'swipesecrets',
                'created_at' => $date,
                'updated_at' => $date,
            ], [
                'id' => Str::orderedUuid(),
                'name' => 'usa',
                'created_at' => $date,
                'updated_at' => $date,
            ],
        ];
    }
}
