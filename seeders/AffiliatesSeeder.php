<?php

namespace Database\Seeders;

use App\Models\Affiliate;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Mirzacles\Models\AffiliatePayout;
use Mirzacles\Models\AffiliateWhitelist;

class AffiliatesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (config('app.env') == 'production') {
            $path = sprintf('database/seeders/%s/%s/affiliates.json', config('app.env'), strtoupper(config('app.location')));
        } else {
            $path = sprintf('database/seeders/%s/affiliates.json', config('app.env'));
        }

        if (!file_exists($path)) {
            return;
        }

        $whitelist_path = 'database/seeders/global/affiliates_whitelist.json';
        $affiliates_whitelist = file_exists($whitelist_path) ? json_decode(file_get_contents($whitelist_path), true) : [];

        $affiliates = json_decode(file_get_contents($path), true);
        foreach ($affiliates as $affiliate) {
            $new_affiliate = Affiliate::firstOrCreate(
                [
                    'alias' => $affiliate['alias'],
                ],
                [
                    'email' => $affiliate['email'],
                    'password' => $affiliate['password'],
                    'role' => $affiliate['role'],
                    'payout_currency' => $affiliate['payout_currency'] ?? 'usd',
                    'affiliate_type' => $affiliate['affiliate_type'],
                    'affiliate_share' => $affiliate['affiliate_share'] ?? 0,
                    'postback' => $affiliate['postback'] ?? '',
                    'postback_orders' => $affiliate['postback_orders'] ?? '',
                    'postback_orders_recurring' => $affiliate['postback_orders_recurring'] ?? '',
                    'postback_orders_charge_backs' => $affiliate['postback_orders_charge_backs'] ?? '',
                    'labels' => $affiliate['labels'] ?? null,
                    'active' => $affiliate['active'] ?? 1,
                    'accountmanager_name' => $affiliate['accountmanager_name'] ?? '',
                    'accountmanager_email' => $affiliate['accountmanager_email'] ?? '',
                    'invoice_number' => $affiliate['invoice_number'] ?? '',
                    'invoice_currency' => $affiliate['invoice_currency'] ?? '',
                    'invoice_frequency' => $affiliate['invoice_frequency'] ?? '',
                    'invoice_vat' => $affiliate['invoice_vat'] ?? '',
                ]
            );

            if (isset($affiliate['payouts']) && is_array($affiliate['payouts'])) {
                foreach ($affiliate['payouts'] as $payout) {
                    AffiliatePayout::updateOrCreate(
                        [
                            'affiliate_id' => $new_affiliate->id,
                            'affiliate_code' => $payout['affiliate_code'] ?? '',
                            'device' => $payout['device'],
                            'site_id' => $payout['site_id'] ?? null,
                        ],
                        [
                            'payout' => $payout['payout'] ?? 0,
                            'payout_eur' => $payout['payout_eur'] ?? 0,
                            'created_at' => Carbon::now(),
                        ]
                    );
                }
            }

            foreach ($affiliates_whitelist as $whitelist) {
                if (in_array($affiliate['alias'], $whitelist['aliases'])) {
                    foreach ($whitelist['ips'] as $ip) {
                        AffiliateWhitelist::updateOrCreate(
                            [
                                'affiliate_id' => $new_affiliate->id,
                                'ip_address' => $ip,
                            ],
                            [
                                'blocked' => $whitelist['blocked'] ?? 0,
                            ]
                        );
                    }
                }
            }
        }
    }
}
