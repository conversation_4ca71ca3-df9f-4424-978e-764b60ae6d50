<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Mirzacles\Models\Region;
use Mirzacles\Models\Tax;

class TaxesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (config('app.env') == 'production') {
            $path = sprintf('database/seeders/%s/%s/taxes.json', config('app.env'), strtoupper(config('app.location')));
        } else {
            $path = sprintf('database/seeders/%s/taxes.json', config('app.env'));
        }

        if (!file_exists($path)) {
            return;
        }

        $taxes = json_decode(file_get_contents($path), true);

        foreach ($taxes as $tax) {
            $region = Region::where([
                'code' => $tax['code'],
                'name' => $tax['name'],
            ])
                ->first();

            if (!$region) {
                continue;
            }

            foreach ($tax['taxes'] as $idx => $t) {
                Tax::updateOrCreate(
                    [
                        'geo_id' => $region->id,
                        'name' => $t['name'],
                    ],
                    [
                        'geo_type' => $region->getMorphClass(),
                        'rate' => $t['rate'],
                        'order' => $idx + 1,
                    ]
                );
            }
        }
    }
}
