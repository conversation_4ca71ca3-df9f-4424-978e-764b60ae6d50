<?php

namespace Database\Seeders;

use App\Models\EmailType;
use Database\Support\SeedHelper;
use Illuminate\Database\Seeder;

class EmailTypeSeeder extends Seeder
{
    public function run(): void
    {
        $email_types = SeedHelper::getData('email_type.json');

        if (!empty($email_types)) {
            foreach ($email_types as $type) {
                EmailType::updateOrCreate(
                    ['desc' => $type],
                    []
                );
            }

            EmailType::whereNotIn('desc', $email_types)->delete();
        }
    }
}
