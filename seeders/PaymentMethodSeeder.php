<?php

namespace Database\Seeders;

use Database\Support\SeedHelper;
use Illuminate\Database\Seeder;
use Mirzacles\Models\PaymentProvider;
use Mirzacles\Models\PaymentType;

class PaymentMethodSeeder extends Seeder
{
    public function run(): void
    {
        $payment_types = SeedHelper::getData('payment_types.json');
        foreach ($payment_types as $paymentType) {
            PaymentType::query()->updateOrCreate(
                ['name' => $paymentType['name']],
                ['title' => $paymentType['title'], 'icon' => $paymentType['icon']]
            );
        }

        $payment_providers = SeedHelper::getData('payment_providers.json');
        foreach ($payment_providers as $paymentProvider) {
            PaymentProvider::query()->updateOrCreate(
                ['name' => $paymentProvider['name'], 'class' => $paymentProvider['class']]
            );
        }
    }
}
