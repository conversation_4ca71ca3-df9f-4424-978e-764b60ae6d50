<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class City extends Model
{
    use HasUuids;

    protected $table = 'geo_cities';
    protected $guarded = ['id'];

    public $timestamps = false;

    public function region(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(Region::class), 'region_id');
    }
}
