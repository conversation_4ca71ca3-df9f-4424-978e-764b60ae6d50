<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Sellable extends Model
{
    use HasUuids;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    public function centsToPay(): int
    {
        return $this->toPay() * 100;
    }

    public function orders(): MorphMany
    {
        return $this->morphMany(ModelFinder::getClass(Order::class), 'sellable');
    }

    public function getAmountAttribute(mixed $value): float
    {
        $value = $this instanceof ProductSubscription ? $this->initial_price : $value;
        return $value / 100;
    }

    public function getSpecialAmountAttribute(mixed $value): float
    {
        if (static::class === ProductPremium::class) {
            return 0;
        }

        return ($value / 100);
    }
}
