<?php

namespace Mirzacles\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Profile extends Model
{
    use HasFactory;
    use HasUuids;

    protected $table = 'profiles';

    protected $guarded = ['id'];

    public function info(): HasOne
    {
        return $this->hasOne(ModelFinder::getClass(ProfileInfo::class), 'profile_id');
    }

    public function uploads(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(Upload::class), 'member_id');
    }

    public function publicPhotos(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(Upload::class), 'member_id')->where('type', 'photo');
    }

    public function privatePhotos(): Has<PERSON>any
    {
        return $this->hasMany(ModelFinder::getClass(Upload::class), 'member_id')->where('type', 'private');
    }

    public function postIts(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(PostIt::class), 'user_id');
    }

    public function mainPhoto(): HasOne
    {
        return $this->hasOne(ModelFinder::getClass(Upload::class), 'member_id')->where('type', 'main');
    }

    public function allUploads()
    {
        return $this->uploads()->where('active', true)->orderByDesc('created_at');
    }

    public function isOnline(): bool
    {
        return $this->online || $this->updated_at->gt(Carbon::parse('-10 minutes'));
    }

    public function domain(): HasOne
    {
        return $this->hasOne(ModelFinder::getClass(Domain::class), 'site_id', 'site_id');
    }

    public function visitors(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(Visitor::class));
    }

    public function ranking(): HasOne
    {
        return $this->hasOne(ModelFinder::getClass(ProfileRanking::class), 'profile_id');
    }

    public function chatGroups()
    {
        $groupIds = ChatGroupMember::where('profile_id', $this->id)
            ->pluck('chat_group_id')
            ->toArray();

        return ChatGroup::whereIn('id', $groupIds)->get();
    }

    public function sharedInterests(User $user): array
    {
        if (!$user->info instanceof UserInfo || !$this->info instanceof ProfileInfo) {
            return [];
        }

        $profileActivePreferences = $this->info->activePreferences();
        $activeUserPreferences = $user->info->activePreferences();

        $interests = array_intersect_key($activeUserPreferences, $profileActivePreferences);

        $sharedInterests = [];

        foreach (array_keys($interests) as $interest) {
            if ($profileActivePreferences[$interest] && $activeUserPreferences[$interest]) {
                $sharedInterests[$interest] = true;
            }
        }

        return $sharedInterests;
    }
}
