<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AutoReplyQueue extends Model
{
    use HasUuids;

    protected $guarded = ['id'];
    protected $table = 'auto_reply_queue';

    protected function casts(): array
    {
        return [
            'send_at' => 'datetime',
        ];
    }

    public function autoReply(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(AutoReply::class));
    }

    public function profile(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(Profile::class), 'profile_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(User::class), 'user_id');
    }

    public function child(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(AutoReplyQueue::class), 'child_id');
    }
}
