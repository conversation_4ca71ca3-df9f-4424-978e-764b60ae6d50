<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProfileTheme extends Model
{
    use HasUuids;

    protected $table = 'profiles_themes';
    protected $guarded = ['id'];

    public function profile(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(Profile::class), 'profiles_id');
    }
}
