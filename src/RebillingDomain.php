<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RebillingDomain extends Model
{
    use HasUuids;

    public $timestamps = false;

    public function subscriptions(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(UserSubscription::class));
    }
}
