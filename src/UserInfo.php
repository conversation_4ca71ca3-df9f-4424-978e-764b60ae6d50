<?php

namespace Mirzacles\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserInfo extends Model
{
    use HasUuids;

    protected $table = 'user_info';
    protected $guarded = [];
    protected $appends = ['age'];


    public function region(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(Region::class));
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(City::class));
    }

    public function getAgeAttribute(): int
    {
        return Carbon::parse($this->birthdate)->age;
    }

    public function prefers(): array
    {
        return array_filter(
            $this->getAttributes(),
            fn ($key) => preg_match('/^(pref)(.*)/', $key) > 0,
            ARRAY_FILTER_USE_KEY
        );
    }

    public function activePreferences(): array
    {
        return array_filter($this->prefers(), fn ($v) => $v > 0);
    }
}
