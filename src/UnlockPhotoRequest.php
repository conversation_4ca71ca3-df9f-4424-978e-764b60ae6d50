<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UnlockPhotoRequest extends Model
{
    use HasUuids;

    protected $table = 'unlock_photos_requests';
    protected $guarded = [];

    public function user(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(User::class));
    }

    public function profile(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(Profile::class));
    }
}
