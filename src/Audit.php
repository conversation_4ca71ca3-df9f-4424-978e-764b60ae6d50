<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Model;

class Audit extends Model
{
    public const EVENTS = ['created', 'updated', 'deleted', 'restoring', 'restored', 'retrieved'];

    protected $table = 'audits';
    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'old_values' => 'json',
            'new_values' => 'json',
        ];
    }

    public function getSerializedDate($date): string
    {
        return $this->serializeDate($date);
    }
}
