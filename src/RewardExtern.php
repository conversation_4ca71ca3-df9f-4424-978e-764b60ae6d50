<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class RewardExtern extends Model
{
    use HasFactory;
    use HasUuids;

    protected $table = 'rewards_extern';
    protected $guarded = ['id'];

    public function visibilities(): BelongsToMany
    {
        return $this->belongsToMany(ModelFinder::getClass(User::class), 'rewards_extern_visibility')
            ->withPivot(['uuid']);
    }

    public function rewardedUsers(): BelongsToMany
    {
        return $this->belongsToMany(ModelFinder::getClass(User::class), 'user_extern_reward')
            ->withPivot(['collected']);
    }
}
