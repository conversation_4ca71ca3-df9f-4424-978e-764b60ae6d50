<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class PostIt extends Model
{
    use HasUuids;

	protected $table = 'post_its';
	protected $guarded = ['id'];
	protected $with = ['likes'];
	protected $appends = ['poster'];

	public function user(): BelongsTo
	{
		return $this->belongsTo(ModelFinder::getClass(User::class));
	}

	public function profile(): BelongsTo
	{
		return $this->belongsTo(ModelFinder::getClass(Profile::class));
	}

	public function likes(): BelongsToMany
	{
		return $this->belongsToMany(
            ModelFinder::getClass(User::class),
            'post_it_likes',
            'post_it_id',
            'user_id'
        );
	}

	public function getPosterAttribute()
	{
		return $this->user ?? $this->profile;
	}

    public function getPostAttribute($value)
    {
        $pattern = "/[^@\s]*@[^@\s]*\.[^@\s]*/";
        $replacement = '*****';
        $value = preg_replace($pattern, $replacement, $value);
        $value = preg_replace('/\+?[0-9][0-9()\-\s+]{4,20}[0-9]/', '*****', $value);

        return $value;
    }
}
