<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class AutoReply extends Model
{
    use HasFactory;
    use HasUuids;

    public const TYPE_UNLOCK_PHOTO_IN_CONVERSATION = 'photo-in-convo';
    public const TYPE_UNLOCK_PHOTO_NO_ATTACHMENT = 'photo-no-attachment';
    public const TYPE_UNLOCK_PHOTO_ATTACHMENT = 'photo-attachment';
    public const TYPE_UNLOCK_PHOTO_DENY = 'photo-deny';
    public const TYPE_PROFILE_PHOTO = 'photo-profile';
    public const TYPE_FAVORITE = 'favorite';
    public const TYPE_STOPPED_CONVERSATIONS = 'stopped-conversations';
    public const TYPE_STOPPED_SHORT = 'stopped-short';
    public const TYPE_STOPPED_5M = 'stopped-5m';
    public const TYPE_STOPPED_10M = 'stopped-10m';
    public const TYPE_STOPPED_MEDIUM = 'stopped-medium';
    public const TYPE_STOPPED_15M = 'stopped-15m';
    public const TYPE_STOPPED_LONG = 'stopped-long';
    public const TYPE_STOPPED_XL = 'stopped-xl';

    protected $table = 'auto_replies';
    protected $guarded = [];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(
            ModelFinder::getClass(User::class),
            'auto_replies_users_pivot',
            'auto_reply_id',
            'user_id'
        );
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(AutoReply::class), 'parent_id');
    }

    public function child(): HasOne
    {
        return $this->hasOne(ModelFinder::getClass(AutoReply::class), 'parent_id');
    }
}
