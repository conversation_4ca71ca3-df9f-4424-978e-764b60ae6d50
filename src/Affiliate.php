<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

class Affiliate extends Model
{
    use HasFactory;
    use HasUuids;

    protected $table = 'affiliates';
    protected $guarded = ['id'];
    protected $hidden = ['password'];

    public function payouts(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(AffiliatePayout::class), 'affiliate_id');
    }

    public function scrubs(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(AffiliatesScrub::class), 'affiliate_id');
    }

    public function getLabelsAttribute(?string $label): Collection
    {
        return collect(explode(',', $label))->filter();
    }

    public function getAffiliateShare(): float
    {
        return $this->affiliate_share / 100;
    }

    public function isType(string $type): bool
    {
        return strtolower($this->affiliate_type) === strtolower($type);
    }

    public function isAnyType($types): bool
    {
        $types = is_array($types) ? $types : func_get_args();

        foreach ($types as $type) {
            if ($this->isType($type)) {
                return true;
            }
        }

        return false;
    }
}
