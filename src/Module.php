<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Module extends Model
{
    use HasUuids;

    public const MODULE_REWARDS = 'rewards';
    public const MODULE_MEDIA_REQUESTS = 'media_requests';
    public const MODULE_FRIEND_REQUEST = 'friend_request';
    public const MODULE_GROUPS = 'chat_groups';

    protected $table = 'modules';
    protected $guarded = ['id'];

    public function domains(): BelongsToMany
    {
        return $this->belongsToMany(ModelFinder::getClass(Domain::class), 'domain_module');
    }
}
