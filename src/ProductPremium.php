<?php

namespace Mirzacles\Models;

class ProductPremium extends Sellable
{
    protected $table = 'products_premium';

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'price_per_month',
        'special_amount',
        'name'
    ];

    public function getPricePerMonthAttribute(): float
    {
        return $this->toPay() / $this->months ;
    }

    public function getNameAttribute(): string
    {
        return 'VIP - '.$this->months.' month(s)';
    }
}
