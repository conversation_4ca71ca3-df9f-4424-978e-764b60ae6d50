<?php

namespace Mirzacles\Models;

use Carbon\Carbon;
use DateTime;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Mirzacles\Helpers\Services\CacheKeyService;
use Illuminate\Support\Str;
use Plank\Metable\Metable;

class User extends Model
{
    use CanResetPassword;
    use HasFactory;
    use HasUuids;
    use Metable;

    public const GENDER_FEMALE = 'female';

    public const GENDER_MALE = 'male';

    public const PREFERENCES_ATTRIBUTES = [
        'pref_cam',
        'pref_kissing',
        'pref_lingerie',
        'pref_pictures',
        'pref_massage',
        'pref_bondage',
        'pref_sadomasochism',
        'pref_threesome',
        'pref_group',
        'pref_safe',
        'pref_oral',
        'pref_anal',
        'pref_public',
    ];

    public const PREFERENCES_NAMES = [
        'pref_cam' => 'Cam sex',
        'pref_kissing' => 'Kissing',
        'pref_lingerie' => 'Lingerie',
        'pref_pictures' => 'Exchanging pictures',
        'pref_massage' => 'Erotic massage',
        'pref_bondage' => 'Bondage',
        'pref_sadomasochism' => 'Sadomasochism',
        'pref_threesome' => 'Threesome',
        'pref_group' => 'Group sex',
        'pref_safe' => 'Safe sex',
        'pref_oral' => 'Oral sex',
        'pref_anal' => 'Anal sex',
        'pref_public' => 'Public sex',
    ];

    public const APPEARANCE_ATTRIBUTES = [
        'length',
        'body_type',
        'eye_color',
        'hair_color',
        'piercing',
        'tattoo',
        'shaved',
        'weight',
    ];

    protected $table = 'users';

    protected $guarded = ['id'];

    protected $hidden = ['password', 'remember_token'];

    protected function casts(): array
    {
        return [
            'password_remind_sent' => 'datetime',
            'premium' => 'datetime',
            'pushcrew_lastsend_at' => 'datetime',
        ];
    }

    public function delete() {
        $this->update([
            'username' => $this->username . '_rm' . now()->getTimestamp(),
            'status' => false,
            'unsubscribed_at' => now(),
            'deleted_at' => now()->getTimestamp(),
        ]);
    }

    public function mainPhoto(): HasOne
    {
        return $this->hasOne(ModelFinder::getClass(Upload::class), 'member_id')->where('type', 'main');
    }

    public function unlockPhotoRequests(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(UnlockPhotoRequest::class));
    }

    public function hasUnlockPhotosRequest(Profile $profile): bool
    {
        return $this->unlockPhotoRequests()->where('profile_id', $profile->id)->exists();
    }

    public static function friendsCount(string $id): int
    {
        return PhotosUnlocked::select(['user_id'])
            ->where('user_id', $id)
            ->distinct()
            ->count('user_id');
    }

    public function subscription(): HasOne
    {
        return $this->hasOne(ModelFinder::getClass(UserSubscription::class))
            ->where('ends_at', '>', now());
    }

    public function subscriptions(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(UserSubscription::class));
    }

    public function hasSubscription(): bool
    {
        return $this->subscriptions()->where('ends_at', '>', now())->exists();
    }

    public function eligibleForTrialSubscription(): bool
    {
        return $this->subscriptions()->count() === 0;
    }

    public function info(): HasOne
    {
        return $this->hasOne(ModelFinder::getClass(UserInfo::class), 'user_id');
    }

    public function uploads(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(Upload::class), 'member_id');
    }

    public function privatePhotos(): HasMany
    {
        return $this->uploads()->where('type', 'upload');
    }

    public function allUploads(int $limit = 2): HasMany
    {
        return $this->uploads()->take($limit);
    }

    public function favoriteProfiles(): HasManyThrough
    {
        return $this->hasManyThrough(
            ModelFinder::getClass(Profile::class),
            Favorite::class,
            'user_id',
            'id',
            'id',
            'profile_id',
        );
    }

    public function domain(): HasOne
    {
        return $this->hasOne(ModelFinder::getClass(Domain::class), 'label_id', 'label_id');
    }

    public function settings(): HasOne
    {
        return $this->hasOne(ModelFinder::getClass(Setting::class));
    }

    public function posts(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(ChatGroupPost::class), 'user_id');
    }

    public function likedChatGroupPosts(): BelongsToMany
    {
        return $this->belongsToMany(
            ModelFinder::getClass(ChatGroupPost::class),
            'chat_group_post_likes',
            'user_id',
            'chat_group_post_id'
        );
    }

    public function postIts(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(PostIt::class), 'user_id');
    }

    public function likedPostIts(): BelongsToMany
    {
        return $this->belongsToMany(
            ModelFinder::getClass(PostIt::class),
            'post_it_likes',
            'user_id',
            'post_it_id'
        );
    }

    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(
            ModelFinder::getClass(ChatGroup::class),
            'chat_group_members',
            'user_id',
            'chat_group_id'
        );
    }

    public function chatGroupPostLikes(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(ChatGroupPostLike::class));
    }

    public function shadowCredits(): HasOne
    {
        return $this->hasOne(ModelFinder::getClass(UserShadowCredits::class));
    }

    public function favorites(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(Favorite::class), 'user_id');
    }

    public function trackRecord(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(TrackRecord::class), 'user_id');
    }

    public function countUnlockedPhotoRequests(?Carbon $date = null): int
    {
        $requests = DB::table('unlock_photos_requests')->where('user_id', $this->id);

        if ($date !== null) {
            $requests->where('created_at', '>=', $date);
        }

        return $requests->count();
    }

    public function countSentMessages(Carbon $date): int
    {
        return DB::table('messages_' . $this->site_id)
            ->where('sent_by_user', true)
            ->where('user_id', $this->id)
            ->where('created_at', '>=', $date)
            ->count();
    }

    public function hasPaid()
    {
        $cacheKey = CacheKeyService::userHasPaid($this->id);
        $cacheTTL = CacheKeyService::ttl('user_has_paid');

        return Cache::remember($cacheKey, $cacheTTL, function () {
            return $this->orders()->where('status', 'Paid')->exists();
        });
    }

    public function hasAllEmailsEnabled(): bool
    {
        $emailSettings = $this->settings->email;

        // If user has frequency NOT set to 'all', don't return anything here. That mailflow is in LimitedTimeFrameRule
        if (isset($emailSettings['frequency']) && $emailSettings['frequency'] !== 'all') {
            return false;
        }

        return true;
    }

    public function getEmailFrequency(): string
    {
        $emailSettings = $this->settings->email;

        if (!isset($emailSettings['frequency'])) {
            return 'all';
        }

        return $emailSettings['frequency'];
    }

    public function getEmailTimeframe(): ?string
    {
        $emailSettings = $this->settings->email;

        if (!isset($emailSettings['timeframe'])) {
            return null;
        }

        return $emailSettings['timeframe'];
    }

    public function orders(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(Order::class));
    }

    public function vipExpires(): int
    {
        $now = Carbon::now();

        if ($this->isPremium()) {
            $expires = Carbon::parse($this->premium)->addDays();
            $diffHours = $expires->diffInHours($now) + 1;
            $diffDays = $expires->diffInDays($now) + 1;

            return $diffHours < 25 ? $diffHours : $diffDays;
        }

        return 0;
    }

    /**
     * Check if the user's premium subscription is active at the moment.
     */
    public function isPremium(): bool
    {
        return (bool) $this->premiumUntil()?->isFuture();
    }

    /**
     * Get the date of the end of the user's premium subscription.
     */
    public function premiumUntil(): ?Carbon
    {
        return $this->premium?->endOfDay();
    }

    /**
     * Whether the user should be offered loyalty discounts.
     */
    public function isEligibleForLoyaltyDiscount(): bool
    {
        if (!$this->isPremium()) {
            return false;
        }

        $diffInHours = $this->premiumUntil()->diffInHours(now());
        $threshold = (24 * 3);

        return $diffInHours <= $threshold;
    }

    public function getNextPackage(): Product
    {
        $latest_order = $this->orders()->where('status', 'Paid')->orderBy('created_at', 'desc')->first();
        /** @var Product $first_product */
        $first_product = Product::query()->where('site_id', $this->site_id)->where('location', 'main')->orderBy(
            'credits',
            'asc'
        )->first();
        if (!$latest_order) {
            return $first_product;
        }
        $current_product = Product::query()->find($latest_order->product_id);
        if ($current_product->location == 'promo') {
            return $first_product;
        }
        $next_product = Product::where('site_id', $this->site_id)->where('location', 'main')->where(
            'credits',
            '>',
            $current_product->credits
        )->orderBy('credits', 'asc')->first();

        return $next_product ?? $current_product;
    }

    public function hasFreeCredit()
    {
        return !$this->hasPaid() && !$this->isPremium() && $this->credits == 1;
    }

    public function emailActivated(): bool
    {
        return $this->active;
    }

    /**
     * Whether this user's account is active (Not deleted or block or disabled etc...).
     */
    public function isActive(): bool
    {
        return $this->status;
    }

    public function hasEnoughCredits(int $price): bool
    {
        return $this->credits >= $price;
    }

    public function autologin(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(Autologin::class));
    }

    /**
     * Returns the location name in the legacy platform format (country-region)
     */
    public function getFullLocationAttribute(): string
    {
        $region = $this->info->city->region;

        return implode('-', [
            $region->country->name,
            $region->name,
        ]);
    }

    public function orderHistory(): HasOne
    {
        return $this->hasOne(ModelFinder::getClass(UserOrderHistory::class));
    }


    public function getSubscriptionProduct(): ?Subscription
    {
        return Subscription::where([
            'currency' => strtolower($this->domain->currency),
            'days' => $this->subscription ? 30 : 3,
        ])->first();
    }

    public function regionName(): string
    {
        if ($this->info->region) {
            return $this->info->region->name;
        }

        return '';
    }

    public function hasAffiliate(): bool
    {
        return $this->click_request_id !== null &&
            !in_array($this->click_request_id, ['fabtest']) &&
            !Str::endsWith($this->email, 'google.com');
    }

    public function emailsTracking(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(EmailsTracking::class));
    }

    public function hasTrackRecord($trackRecord): bool
    {
        return $this->trackRecord()->where('event', $trackRecord)->exists();
    }

    public function autoReplies(): BelongsToMany
    {
        return $this->belongsToMany(
            ModelFinder::getClass(AutoReply::class),
            'auto_replies_users_pivot',
            'user_id',
            'auto_reply_id'
        );
    }

    public function matches(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(Matches::class));
    }

    public function preventPostback(): bool
    {
        return Str::contains($this->click_affiliate_id, ['fabtrk', 'fabapi', 'fabrest']) && Str::contains($this->sub4, 'google');
    }

    public function ageGroup(): string
    {
        $dob = new DateTime($this->info->birthdate);
        $now = new DateTime();
        $age = $now->diff($dob)->y;

        if ($age <= 24) {
            return '18_24';
        } elseif ($age >= 25 && $age <= 34) {
            return '25_34';
        } elseif ($age >= 35 && $age <= 44) {
            return '35_44';
        } elseif ($age >= 45 && $age <= 54) {
            return '45_54';
        } elseif ($age >= 55 && $age <= 64) {
            return '55_64';
        }

        return '65_99';
    }
}
