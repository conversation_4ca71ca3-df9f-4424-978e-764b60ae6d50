<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserRequest extends Model
{
    use HasUuids;

    protected $table = 'user_requests';
    protected $guarded = ['id'];
    protected $appends = ['sender', 'receiver'];

    public const PRICE = 3;

    public function user(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(User::class));
    }

    public function profile(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(Profile::class));
    }

    public function getSenderAttribute(): User|Profile
    {
        return $this->sent_by_user ? $this->user : $this->profile;
    }

    public function getReceiverAttribute(): User|Profile
    {
        return $this->sent_by_user ? $this->profile : $this->user;
    }

}
