<?php
namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Model;
use Mirzacles\Models\Profile;

class ProfileRanking extends Model
{
    // Specify the table associated with the model
    protected $table = 'profile_rankings';

    // Specify the attributes that are mass assignable
    protected $fillable = [
        'profile_id',
        'ranking',
    ];

    public $timestamps = false;

    // Define relationships if needed
    public function profile()
    {
        return $this->belongsTo(Profile::class);
    }
}