<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Visitor extends Model
{
    use HasFactory;
    use HasUuids;

    protected $table = 'visitors';
    protected $guarded = ['id'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(User::class));
    }

    public function profile(): BelongsTo
    {
        return $this->belongsTo(ModelFinder::getClass(Profile::class));
    }
}
