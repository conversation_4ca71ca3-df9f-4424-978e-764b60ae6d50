<?php

namespace Mirzacles\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ScheduledTaskStats extends Model
{
    use HasUuids;

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'history' => 'array',
            'last_run_at' => 'datetime',
        ];
    }

    public function history(): HasMany
    {
        return $this->hasMany(ModelFinder::getClass(ScheduledTaskStatHistory::class));
    }
}
