<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conversations_4', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->unsignedTinyInteger('last_sent_by_user');
            $table->char('user_id', 36);
            $table->char('profile_id', 36)->index();
            $table->char('last_message_id', 36);
            $table->string('last_message_type', 16)->index();
            $table->unsignedTinyInteger('visible')->default(1)->index();
            $table->unsignedTinyInteger('read')->default(0)->index();
            $table->unsignedTinyInteger('from_user')->default(0)->index();
            $table->unsignedTinyInteger('from_profile')->default(0)->index();
            $table->unsignedTinyInteger('is_archived')->default(0);
            $table->timestamp('last_sent_at')->nullable()->index();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();

            $table->index(['user_id', 'profile_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conversations_4');
    }
};
