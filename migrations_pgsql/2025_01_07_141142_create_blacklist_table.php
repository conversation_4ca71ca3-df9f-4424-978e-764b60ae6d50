<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blacklist', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->unsignedSmallInteger('site_id');
            $table->string('type', 20)->nullable();
            $table->char('element_id', 36);
            $table->text('string');
            $table->smallInteger('status')->nullable();
            $table->char('user_id', 36);
            $table->char('checked_by', 36);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blacklist');
    }
};
