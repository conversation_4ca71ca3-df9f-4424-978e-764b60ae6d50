<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('messages_queue_live', function (Blueprint $table) {
            $table->foreign(['profile_id'])->references(['id'])->on('profiles')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign(['upload_id'])->references(['id'])->on('uploads')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('cascade')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('messages_queue_live', function (Blueprint $table) {
            $table->dropForeign('messages_queue_live_profile_id_foreign');
            $table->dropForeign('messages_queue_live_upload_id_foreign');
            $table->dropForeign('messages_queue_live_user_id_foreign');
        });
    }
};
