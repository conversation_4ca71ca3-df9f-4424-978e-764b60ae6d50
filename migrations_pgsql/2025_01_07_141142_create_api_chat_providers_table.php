<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_chat_providers', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->char('domain_id', 36);
            $table->string('name');
            $table->unsignedTinyInteger('order')->default(0);
            $table->unsignedTinyInteger('percentage')->default(0);
            $table->unsignedTinyInteger('enabled')->default(1);
            $table->json('config')->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_chat_providers');
    }
};
