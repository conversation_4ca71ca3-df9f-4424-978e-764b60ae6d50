<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages_queue', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->tinyInteger('messages_table');
            $table->char('message_id', 36)->index();
            $table->char('user_id', 36)->index();
            $table->char('profile_id', 36)->index();
            $table->unsignedTinyInteger('read')->default(0)->index();
            $table->unsignedTinyInteger('is_post_it')->default(0);
            $table->unsignedTinyInteger('is_group_post')->default(0);
            $table->char('request_id', 36)->nullable();
            $table->char('chatter_id', 36)->nullable();
            $table->string('transaction_guid', 64)->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages_queue');
    }
};
