<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('geo_taxes', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->string('geo_type');
            $table->char('geo_id', 36);
            $table->string('name')->nullable();
            $table->decimal('rate', 5, 3);
            $table->unsignedTinyInteger('order')->default(0);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('geo_taxes');
    }
};
