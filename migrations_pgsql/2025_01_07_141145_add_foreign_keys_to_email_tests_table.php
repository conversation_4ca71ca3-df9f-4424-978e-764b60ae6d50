<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('email_tests', function (Blueprint $table) {
            $table->foreign(['acting_user_id'])->references(['id'])->on('users')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign(['domain_id'])->references(['id'])->on('domains')->onUpdate('cascade')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('email_tests', function (Blueprint $table) {
            $table->dropForeign('email_tests_acting_user_id_foreign');
            $table->dropForeign('email_tests_domain_id_foreign');
        });
    }
};
