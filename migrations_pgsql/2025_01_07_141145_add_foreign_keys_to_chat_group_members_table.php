<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chat_group_members', function (Blueprint $table) {
            $table->foreign(['profile_id'])->references(['id'])->on('profiles')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('cascade');
            $table->foreign(['chat_group_id'])->references(['id'])->on('chat_groups')->onUpdate('cascade')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chat_group_members', function (Blueprint $table) {
            $table->dropForeign('chat_group_members_profile_id_foreign');
            $table->dropForeign('chat_group_members_user_id_foreign');
            $table->dropForeign('chat_group_members_chat_group_id_foreign');
        });
    }
};
