<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('planning_warm_up_rules', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->char('domain_id', 36)->index();
            $table->date('start_date');
            $table->date('end_date');
            $table->time('allowed_start_time');
            $table->time('allowed_end_time');
            $table->smallInteger('limit');
            $table->unsignedTinyInteger('active')->default(1);
            $table->unsignedTinyInteger('random_rate')->default(0);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('planning_warm_up_rules');
    }
};
