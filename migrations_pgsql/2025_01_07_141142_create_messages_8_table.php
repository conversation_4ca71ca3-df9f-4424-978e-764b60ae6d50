<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages_8', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->unsignedTinyInteger('member')->default(1);
            $table->unsignedTinyInteger('read')->default(0);
            $table->char('user_id', 36)->index();
            $table->char('profile_id', 36)->index();
            $table->unsignedTinyInteger('sent_by_user')->default(1)->index();
            $table->string('subject')->nullable();
            $table->text('message')->nullable();
            $table->char('upload_id', 36)->nullable()->index();
            $table->char('chatter_id', 36)->nullable()->index();
            $table->char('template_id', 36)->nullable();
            $table->char('planning_id', 36)->nullable();
            $table->string('type', 20)->index();
            $table->timestamp('future')->nullable();
            $table->unsignedTinyInteger('status')->default(1);
            $table->unsignedTinyInteger('visible')->default(1)->index();
            $table->string('chat_uuid', 36)->nullable();
            $table->string('uuid', 36)->nullable();
            $table->unsignedTinyInteger('is_last')->nullable()->index();
            $table->timestamp('created_at')->nullable()->index();
            $table->timestamp('updated_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages_8');
    }
};
