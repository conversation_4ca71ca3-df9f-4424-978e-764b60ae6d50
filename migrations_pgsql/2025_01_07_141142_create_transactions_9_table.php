<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions_9', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->char('user_id', 36)->index();
            $table->char('elite_id', 36)->nullable();
            $table->integer('credits')->default(0);
            $table->date('premium')->nullable();
            $table->char('product_id', 36)->nullable();
            $table->char('product_type', 36)->nullable();
            $table->char('order_id', 36)->nullable();
            $table->text('source')->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions_9');
    }
};
