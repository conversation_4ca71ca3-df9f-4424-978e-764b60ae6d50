<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('planning', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->unsignedInteger('content_id')->nullable();
            $table->char('schedule_id', 36)->nullable()->index();
            $table->unsignedSmallInteger('site_id');
            $table->char('profile_id', 36)->index();
            $table->char('user_id', 36)->index();
            $table->text('target');
            $table->mediumInteger('target_size');
            $table->mediumInteger('target_sent');
            $table->string('type', 30);
            $table->string('subject')->nullable();
            $table->text('message')->nullable();
            $table->char('upload_id', 36)->nullable();
            $table->unsignedTinyInteger('concept')->default(0);
            $table->unsignedTinyInteger('sending')->default(0);
            $table->unsignedTinyInteger('sent')->default(0);
            $table->unsignedTinyInteger('renewable')->default(0);
            $table->dateTime('send_at')->nullable()->index();
            $table->text('geos')->nullable();
            $table->unsignedTinyInteger('approved')->nullable();
            $table->char('approved_by', 36)->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();

            $table->unique(['content_id', 'site_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('planning');
    }
};
