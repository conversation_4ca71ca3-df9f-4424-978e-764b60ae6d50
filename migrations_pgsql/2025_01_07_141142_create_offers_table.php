<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offers', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->char('product_id', 36);
            $table->string('name', 50)->nullable();
            $table->unsignedMediumInteger('target_size')->default(0);
            $table->string('alias', 25);
            $table->dateTime('valid_until');
            $table->smallInteger('limit')->nullable();
            $table->unsignedTinyInteger('active');
            $table->unsignedTinyInteger('email');
            $table->unsignedTinyInteger('sent');
            $table->smallInteger('repeat_days')->default(0);
            $table->string('subject', 100)->nullable();
            $table->string('landing_template')->nullable();
            $table->string('email_template')->nullable();
            $table->text('target')->nullable();
            $table->string('test_group')->nullable();
            $table->text('stats');
            $table->string('labels')->nullable();
            $table->text('batch_map');
            $table->text('push_notification');
            $table->text('email_plain_text');
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offers');
    }
};
