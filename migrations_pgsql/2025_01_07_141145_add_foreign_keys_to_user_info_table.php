<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_info', function (Blueprint $table) {
            $table->foreign(['city_id'])->references(['id'])->on('geo_cities')->onUpdate('cascade')->onDelete('no action');
            $table->foreign(['region_id'])->references(['id'])->on('geo_regions')->onUpdate('cascade')->onDelete('no action');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('cascade')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_info', function (Blueprint $table) {
            $table->dropForeign('user_info_city_id_foreign');
            $table->dropForeign('user_info_region_id_foreign');
            $table->dropForeign('user_info_user_id_foreign');
        });
    }
};
