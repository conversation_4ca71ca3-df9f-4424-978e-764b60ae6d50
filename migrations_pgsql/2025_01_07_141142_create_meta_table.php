<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meta', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->string('metable_type');
            $table->char('metable_id', 36);
            $table->string('type')->default('null');
            $table->string('key');
            $table->longText('value');
            $table->decimal('numeric_value', 36, 16)->nullable();
            $table->string('hmac', 64)->nullable();

            $table->index(['key', 'metable_type', 'numeric_value']);
            $table->unique(['metable_type', 'metable_id', 'key']);
            $table->index(['metable_type', 'key', 'value']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meta');
    }
};
