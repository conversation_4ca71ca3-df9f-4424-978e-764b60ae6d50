<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages_reported', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->string('message');
            $table->char('message_id', 36);
            $table->char('chatter_id', 36)->index();
            $table->char('report_message_id', 36)->index();
            $table->unsignedTinyInteger('approved')->default(0);
            $table->char('reporter_id', 36)->nullable();
            $table->char('operator_id', 36)->nullable();
            $table->unsignedSmallInteger('site_id');
            $table->char('profile_id', 36)->index();
            $table->char('user_id', 36)->index();
            $table->string('notes')->nullable();
            $table->unsignedTinyInteger('processed')->default(0);
            $table->char('processed_by', 36)->nullable();
            $table->timestamp('processed_date')->nullable();
            $table->smallInteger('rank')->default(0);
            $table->unsignedTinyInteger('supervisor_archived')->default(0);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages_reported');
    }
};
