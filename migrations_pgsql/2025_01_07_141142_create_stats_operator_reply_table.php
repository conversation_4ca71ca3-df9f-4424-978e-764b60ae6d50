<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stats_operator_reply', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->char('operator_id', 36);
            $table->date('generated_date');
            $table->unsignedInteger('send_messages');
            $table->unsignedInteger('replies');
            $table->double('avg');
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();

            $table->index(['operator_id', 'generated_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stats_operator_reply');
    }
};
