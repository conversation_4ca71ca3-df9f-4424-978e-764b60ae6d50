<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('operators', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->string('company');
            $table->string('api_provider')->nullable();
            $table->string('email', 100)->unique();
            $table->string('password');
            $table->string('pass', 200);
            $table->integer('cpm');
            $table->unsignedTinyInteger('status')->default(1);
            $table->unsignedTinyInteger('api')->default(0);
            $table->text('api_site_ids');
            $table->smallInteger('api_percentage')->default(0);
            $table->mediumInteger('api_day_limit')->default(0);
            $table->unsignedTinyInteger('internal')->default(0);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('operators');
    }
};
