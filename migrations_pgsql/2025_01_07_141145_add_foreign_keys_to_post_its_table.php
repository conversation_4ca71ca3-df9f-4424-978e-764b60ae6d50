<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('post_its', function (Blueprint $table) {
            $table->foreign(['profile_id'])->references(['id'])->on('profiles')->onUpdate('cascade')->onDelete('no action');
            $table->foreign(['template_id'])->references(['id'])->on('post_it_templates')->onUpdate('cascade')->onDelete('no action');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('cascade')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('post_its', function (Blueprint $table) {
            $table->dropForeign('post_its_profile_id_foreign');
            $table->dropForeign('post_its_template_id_foreign');
            $table->dropForeign('post_its_user_id_foreign');
        });
    }
};
