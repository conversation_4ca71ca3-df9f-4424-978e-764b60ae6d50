<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('scheduled_task_stat_history', function (Blueprint $table) {
            $table->foreign(['scheduled_task_stats_id'])->references(['id'])->on('scheduled_task_stats')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('scheduled_task_stat_history', function (Blueprint $table) {
            $table->dropForeign('scheduled_task_stat_history_scheduled_task_stats_id_foreign');
        });
    }
};
