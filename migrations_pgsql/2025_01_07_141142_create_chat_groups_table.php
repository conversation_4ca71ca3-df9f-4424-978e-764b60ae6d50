<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_groups', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->string('title');
            $table->string('slug')->nullable();
            $table->text('description');
            $table->string('image', 100);
            $table->unsignedSmallInteger('site_id')->index();
            $table->string('requirements')->nullable();
            $table->unsignedTinyInteger('required')->default(0);
            $table->unsignedInteger('male_members')->default(0);
            $table->unsignedInteger('female_members')->default(0);
            $table->unsignedInteger('total_members')->default(0);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_groups');
    }
};
