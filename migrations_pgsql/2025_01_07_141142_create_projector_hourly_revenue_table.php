<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projector_hourly_revenue', function (Blueprint $table) {
            $table->increments('id');
            $table->date('order_date');
            $table->unsignedSmallInteger('hour');
            $table->char('domain_id', 36);
            $table->char('payment_method_id', 36)->nullable();
            $table->integer('amount');
            $table->integer('amount_refunded')->default(0);
            $table->unsignedInteger('paid_orders')->default(0);
            $table->unsignedInteger('refunded_orders')->default(0);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();

            $table->unique(['domain_id', 'payment_method_id', 'order_date', 'hour']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projector_hourly_revenue');
    }
};
