<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('domain_subscription', function (Blueprint $table) {
            $table->foreign(['domain_id'])->references(['id'])->on('domains')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['subscription_id'])->references(['id'])->on('subscriptions')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('domain_subscription', function (Blueprint $table) {
            $table->dropForeign('domain_subscription_domain_id_foreign');
            $table->dropForeign('domain_subscription_subscription_id_foreign');
        });
    }
};
