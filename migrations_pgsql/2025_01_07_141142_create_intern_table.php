<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('intern', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->string('email')->unique();
            $table->string('password');
            $table->string('remember_token')->nullable();
            $table->string('alias', 50);
            $table->string('role', 25);
            $table->text('site_ids');
            $table->unsignedSmallInteger('company_id');
            $table->unsignedTinyInteger('status')->default(1);
            $table->unsignedTinyInteger('timelock_active')->default(0);
            $table->unsignedTinyInteger('timelocked')->default(0);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('intern');
    }
};
