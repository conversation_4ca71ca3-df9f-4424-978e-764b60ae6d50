<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('domain_wonderpush', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->string('title', 100);
            $table->string('message');
            $table->string('positive_button', 20)->default('Subscribe');
            $table->string('negative_button', 10)->default('Cancel');
            $table->string('icon')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('domain_wonderpush');
    }
};
