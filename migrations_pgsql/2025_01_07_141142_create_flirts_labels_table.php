<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flirts_labels', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->unsignedSmallInteger('site_id');
            $table->unsignedSmallInteger('label_id');
            $table->unsignedTinyInteger('default')->default(0);
            $table->string('name', 50);
            $table->string('description', 150);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();

            $table->index(['name', 'description']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flirts_labels');
    }
};
