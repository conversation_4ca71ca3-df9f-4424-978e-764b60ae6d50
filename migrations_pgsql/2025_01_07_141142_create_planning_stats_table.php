<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('planning_stats', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->char('planning_id', 36)->index();
            $table->mediumInteger('target_size');
            $table->mediumInteger('responses');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('planning_stats');
    }
};
