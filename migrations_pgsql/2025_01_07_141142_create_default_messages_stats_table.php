<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('default_messages_stats', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->char('message_id', 36)->index();
            $table->date('date')->nullable();
            $table->unsignedMediumInteger('target_size');
            $table->unsignedMediumInteger('responses');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('default_messages_stats');
    }
};
