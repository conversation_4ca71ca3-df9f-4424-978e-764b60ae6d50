<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('planning_top_pokes', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->char('planning_id', 36)->index();
            $table->string('type', 20);
            $table->text('message');
            $table->string('target_photo', 4);
            $table->smallInteger('profile_age')->index();
            $table->string('profile_hair_color', 30);
            $table->string('profile_marital_status', 30);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
            $table->unsignedSmallInteger('batch_id')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('planning_top_pokes');
    }
};
