<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mailerq_warmup_ips', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->string('ip', 45)->unique();
            $table->unsignedTinyInteger('trusted')->default(0);
            $table->unsignedTinyInteger('hotmail_blacklist')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mailerq_warmup_ips');
    }
};
