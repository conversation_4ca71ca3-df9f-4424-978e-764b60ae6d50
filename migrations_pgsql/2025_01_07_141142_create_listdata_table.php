<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('listdata', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->string('text', 50)->nullable();
            $table->enum('type', ['chat_blacklist', 'email_blacklist', 'email_autocomplete'])->nullable();
            $table->unsignedTinyInteger('active');
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('listdata');
    }
};
