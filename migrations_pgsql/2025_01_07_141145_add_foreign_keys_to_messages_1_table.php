<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('messages_1', function (Blueprint $table) {
            $table->foreign(['profile_id'])->references(['id'])->on('profiles')->onUpdate('cascade')->onDelete('no action');
            $table->foreign(['upload_id'])->references(['id'])->on('uploads')->onUpdate('cascade')->onDelete('set null');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('cascade')->onDelete('no action');
            $table->foreign(['chatter_id'])->references(['id'])->on('chatters')->onUpdate('no action')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('messages_1', function (Blueprint $table) {
            $table->dropForeign('messages_1_profile_id_foreign');
            $table->dropForeign('messages_1_upload_id_foreign');
            $table->dropForeign('messages_1_user_id_foreign');
            $table->dropForeign('messages_1_chatter_id_foreign');
        });
    }
};
