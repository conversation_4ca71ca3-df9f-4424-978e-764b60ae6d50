<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_tests', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->char('domain_id', 36)->unique();
            $table->unsignedTinyInteger('enabled')->default(1);
            $table->json('templates');
            $table->json('mail_list');
            $table->char('acting_user_id', 36)->index();
            $table->unsignedSmallInteger('force_mailprovider')->nullable();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_tests');
    }
};
