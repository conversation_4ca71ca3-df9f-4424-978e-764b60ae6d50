<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('planning_schedule_used', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->unsignedSmallInteger('site_id');
            $table->char('planning_id', 36)->comment('ID of planning_top_pokes');
            $table->timestamp('created_at')->nullable()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('planning_schedule_used');
    }
};
