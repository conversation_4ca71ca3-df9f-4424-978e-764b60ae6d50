<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('profile_info', function (Blueprint $table) {
            $table->foreign(['city_id'])->references(['id'])->on('geo_cities')->onUpdate('cascade')->onDelete('no action');
            $table->foreign(['profile_id'])->references(['id'])->on('profiles')->onUpdate('cascade')->onDelete('cascade');
            $table->foreign(['region_id'])->references(['id'])->on('geo_regions')->onUpdate('cascade')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('profile_info', function (Blueprint $table) {
            $table->dropForeign('profile_info_city_id_foreign');
            $table->dropForeign('profile_info_profile_id_foreign');
            $table->dropForeign('profile_info_region_id_foreign');
        });
    }
};
