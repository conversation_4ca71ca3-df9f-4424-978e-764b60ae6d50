<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('chat_group_message_templates', function (Blueprint $table) {
            $table->foreign(['chat_group_id'])->references(['id'])->on('chat_groups')->onUpdate('cascade')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('chat_group_message_templates', function (Blueprint $table) {
            $table->dropForeign('chat_group_message_templates_chat_group_id_foreign');
        });
    }
};
