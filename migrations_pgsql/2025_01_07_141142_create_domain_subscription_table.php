<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('domain_subscription', function (Blueprint $table) {
            $table->char('id', 36)->primary();
            $table->char('domain_id', 36);
            $table->char('subscription_id', 36)->index();

            $table->unique(['domain_id', 'subscription_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('domain_subscription');
    }
};
