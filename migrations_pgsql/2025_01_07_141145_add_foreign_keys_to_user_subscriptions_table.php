<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_subscriptions', function (Blueprint $table) {
            $table->foreign(['rebilling_domain_id'])->references(['id'])->on('rebilling_domains')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['subscription_id'])->references(['id'])->on('subscriptions')->onUpdate('no action')->onDelete('no action');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('no action')->onDelete('no action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_subscriptions', function (Blueprint $table) {
            $table->dropForeign('user_subscriptions_rebilling_domain_id_foreign');
            $table->dropForeign('user_subscriptions_subscription_id_foreign');
            $table->dropForeign('user_subscriptions_user_id_foreign');
        });
    }
};
