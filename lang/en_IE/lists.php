<?php

return [
    'preferences' => [
        'pref_cam' => 'Cam',
        'pref_kissing' => 'Kissing',
        'pref_lingerie' => 'Lingerie',
        'pref_pictures' => 'Photo exchange',
        'pref_massage' => 'Erotic massage',
        'pref_bondage' => 'Bondage',
        'pref_sadomasochism' => 'Sadomasochism',
        'pref_threesome' => 'Threesome',
        'pref_group' => 'Group sex',
        'pref_safe' => 'Safe sex',
        'pref_oral' => 'Oral sex',
        'pref_anal' => 'Anal sex',
        'pref_public' => 'Public sex',
    ],

    'preferences_new' => [
        'pref_anal' => 'Anal Sex',
        'pref_oral' => 'Oral Sex',
        'pref_safe' => 'Safe Sex',
        'pref_bare' => 'Bare Sex',
        'pref_threesome' => 'Threesome',
        'pref_group' => 'Group Sex',
        'pref_bukkake' => 'Bukkake',
        'pref_bondage' => 'Bondage',
        'pref_sadomasochism' => 'S&M Soft',
        'pref_smhard' => 'S&M Hard',
        'pref_massage' => 'Erotic massage',
        'pref_pictures' => 'Exchanging Pictures',
        'pref_outfits' => 'Sexy Outfits',
        'pref_dick' => 'Dick Rating',
    ],

    'preferences_clean' => [
        'pref_cam' => 'Videochat',
        'pref_kissing' => 'Kissing',
        'pref_pictures' => 'Photo exchange',
        'pref_massage' => 'Massage',
        'pref_group' => 'Parties',
        'pref_oral' => 'Drinks',
        'pref_public' => 'Sightseeing',
    ],

    'body_types' => [
        'athletic' => 'Athletic / toned',
        'average' => 'Average',
        'bbwbhm' => 'BBW/BHM',
        'curvy' => 'Curvy',
        'slender' => 'Slender',
        'voluptuous' => 'Voluptuous',
        'other' => 'Other',
    ],

    'body_types2' => [
        'athletic' => 'Athletic',
        'average' => 'Average',
        'bbwbhm' => 'A few extra pounds',
        'curvy' => 'Curvy',
        'petite' => 'Petite',
        'voluptuous' => 'Voluptuous',
        'other' => 'Other',
    ],

    'eye_colors' => [
        'black' => 'Black',
        'brown' => 'Brown',
        'blue' => 'Blue',
        'green' => 'Green',
        'gray' => 'Gray',
        'hazel' => 'Hazel',
        'other' => 'Other',
    ],

    'hair_colors' => [
        'black' => 'Black',
        'blonde' => 'Blonde',
        'brown' => 'Brown',
        'red' => 'Red',
        'gray' => 'Gray',
        'white' => 'White',
        'other' => 'Other',
    ],

    'lengths' => [
        'small' => 'Small',
        'normal' => 'Normal',
        'large' => 'Large',
    ],

    'marital_status' => [
        'single' => 'Single',
        'relationship' => 'In Relationship',
        'married' => 'Married',
        'divorced' => 'Divorced',
        'widowed' => 'Widowed',
    ],

    'ethnicity' => [
        'white' => 'White',
        'black' => 'Black',
        'latino' => 'Latino',
        'asian' => 'Asian',
        'arabic' => 'Arabic',
    ],

    'heights' => [
        '149' => 'less than 150 cm',
        '150' => '150 cm',
        '151' => '151 cm',
        '152' => '152 cm',
        '153' => '153 cm',
        '154' => '154 cm',
        '155' => '155 cm',
        '156' => '156 cm',
        '157' => '157 cm',
        '158' => '158 cm',
        '159' => '159 cm',
        '160' => '160 cm',
        '161' => '161 cm',
        '162' => '162 cm',
        '163' => '163 cm',
        '164' => '164 cm',
        '165' => '165 cm',
        '166' => '166 cm',
        '167' => '167 cm',
        '168' => '168 cm',
        '169' => '169 cm',
        '170' => '170 cm',
        '171' => '171 cm',
        '172' => '172 cm',
        '173' => '173 cm',
        '174' => '174 cm',
        '175' => '175 cm',
        '176' => '176 cm',
        '177' => '177 cm',
        '178' => '178 cm',
        '179' => '179 cm',
        '180' => '180 cm',
        '181' => '181 cm',
        '182' => '182 cm',
        '183' => '183 cm',
        '184' => '184 cm',
        '185' => '185 cm',
        '186' => '186 cm',
        '187' => '187 cm',
        '188' => '188 cm',
        '189' => '189 cm',
        '190' => '190 cm',
        '191' => '191 cm',
        '192' => '192 cm',
        '193' => '193 cm',
        '194' => '194 cm',
        '195' => '195 cm',
        '196' => '196 cm',
        '197' => '197 cm',
        '198' => '198 cm',
        '199' => '199 cm',
        '200' => '200 cm or more',
    ],

    'heights_grouped' => [
        '149' => 'less than 150 cm',
        '150' => '150 cm - 154 cm',
        '151' => '150 cm - 154 cm',
        '152' => '150 cm - 154 cm',
        '153' => '150 cm - 154 cm',
        '154' => '150 cm - 154 cm',
        '155' => '155 cm - 159 cm',
        '156' => '155 cm - 159 cm',
        '157' => '155 cm - 159 cm',
        '158' => '155 cm - 159 cm',
        '159' => '155 cm - 159 cm',
        '160' => '160 cm - 164 cm',
        '161' => '160 cm - 164 cm',
        '162' => '160 cm - 164 cm',
        '163' => '160 cm - 164 cm',
        '164' => '160 cm - 164 cm',
        '165' => '165 cm - 169 cm',
        '166' => '165 cm - 169 cm',
        '167' => '165 cm - 169 cm',
        '168' => '165 cm - 169 cm',
        '169' => '165 cm - 169 cm',
        '170' => '170 cm - 174 cm',
        '171' => '170 cm - 174 cm',
        '172' => '170 cm - 174 cm',
        '173' => '170 cm - 174 cm',
        '174' => '170 cm - 174 cm',
        '175' => '175 cm - 179 cm',
        '176' => '175 cm - 179 cm',
        '177' => '175 cm - 179 cm',
        '178' => '175 cm - 179 cm',
        '179' => '175 cm - 179 cm',
        '180' => '180 cm - 184 cm',
        '181' => '180 cm - 184 cm',
        '182' => '180 cm - 184 cm',
        '183' => '180 cm - 184 cm',
        '184' => '180 cm - 184 cm',
        '185' => '185 cm - 189 cm',
        '186' => '185 cm - 189 cm',
        '187' => '185 cm - 189 cm',
        '188' => '185 cm - 189 cm',
        '189' => '185 cm - 189 cm',
        '190' => '190 cm - 194 cm',
        '191' => '190 cm - 194 cm',
        '192' => '190 cm - 194 cm',
        '193' => '190 cm - 194 cm',
        '194' => '190 cm - 194 cm',
        '195' => '195 cm - 199 cm',
        '196' => '195 cm - 199 cm',
        '197' => '195 cm - 199 cm',
        '198' => '195 cm - 199 cm',
        '199' => '195 cm - 199 cm',
        '200' => '200 cm - 204 cm',
        '201' => '200 cm - 204 cm',
        '202' => '200 cm - 204 cm',
        '203' => '200 cm - 204 cm',
        '204' => '200 cm - 204 cm',
        '205' => '205 cm - 209 cm',
        '206' => '205 cm - 209 cm',
        '207' => '205 cm - 209 cm',
        '208' => '205 cm - 209 cm',
        '209' => '205 cm - 209 cm',
        '210' => '210 cm - 214 cm',
        '211' => '210 cm - 214 cm',
        '212' => '210 cm - 214 cm',
        '213' => '210 cm - 214 cm',
        '214' => '210 cm - 214 cm',
        '215' => '215 cm or more',
    ],

    'heights_grouped_short' => [
        '140' => 'less than 150 cm',
        '150' => '150 cm - 159 cm',
        '160' => '160 cm - 169 cm',
        '170' => '170 cm - 179 cm',
        '180' => '180 cm - 189 cm',
        '190' => '190 cm - 199 cm',
        '200' => '200 cm or more',
    ],

    'weights' => [
        '45' => '45 kg or less',
        '50' => '50 kg',
        '55' => '55 kg',
        '60' => '60 kg',
        '65' => '65 kg',
        '70' => '70 kg',
        '75' => '75 kg',
        '80' => '80 kg',
        '85' => '85 kg',
        '90' => '90 kg',
        '95' => '95 kg',
        '100' => '100 kg or more',
    ],

    'email_frequency' => [
        '-1' => 'Realtime',
        '1' => 'Daily',
        '3' => 'Every three days',
        '7' => 'Weekly',
        '15' => 'Every two weeks',
        '30' => 'Every month',
        '0' => 'Never',
    ],

    'distance' => [
        '50',
        '100',
        '150',
        '200',
        '250',
        '300',
    ],
];
