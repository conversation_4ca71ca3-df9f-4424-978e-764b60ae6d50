<?php

return [
    'body_types' => [
        'athletic' => 'Sportos / tónusos',
        'average' => 'Átlagos',
        'bbwbhm' => 'BBW/BHM',
        'curvy' => 'Görbült',
        'slender' => 'Karcsú',
        'voluptuous' => 'Vagány',
        'other' => 'Egyéb',
    ],

    'body_types_men' => [
        'athletic' => 'Sportos / tónusos',
        'muscular' => 'Izmos',
        'slender' => 'Karcsú',
        'average' => 'Átlagos',
        'overweight' => 'Túlsúly',
        'other' => 'Egyéb',
    ],

    'body_types2' => [
        'athletic' => 'Atlétikai',
        'average' => 'Átlagos',
        'bbwbhm' => 'Néhány plusz kiló',
        'curvy' => 'Görbült',
        'petite' => 'Petite',
        'voluptuous' => 'Vagány',
        'other' => 'Egyéb',
    ],

    'eye_colors' => [
        'black' => 'Fekete',
        'brown' => 'Barna',
        'blue' => 'Kék',
        'green' => 'Zöld',
        'gray' => 'Szürke',
        'hazel' => 'Mogyoróbarna',
        'other' => 'Egyéb',
    ],

    'hair_colors' => [
        'black' => 'Fekete',
        'blonde' => 'Szőke',
        'brown' => 'Barna',
        'red' => 'Vörös',
        'gray' => 'Szürke',
        'white' => 'Fehér',
        'other' => 'Egyéb',
    ],

    'lengths' => [
        'small' => 'Kis',
        'normal' => 'Normál',
        'large' => 'Nagy',
    ],

    'marital_status' => [
        'single' => 'Egyedülálló',
        'relationship' => 'Kapcsolatban',
        'married' => 'Házas',
        'divorced' => 'Elvált',
        'widowed' => 'Megözvegyült',
    ],

    'ethnicity' => [
        'white' => 'Fehér',
        'black' => 'Fekete',
        'latino' => 'Latino',
        'asian' => 'Ázsiai',
        'arabic' => 'Arab',
    ],

    'pets' => [
        'none' => 'Nincs háziállat',
        'cats' => 'Macska',
        'dogs' => 'Kutya',
        'birds' => 'Madár',
        'fish' => 'Hal',
        'reptiles' => 'Hüllő',
        'various' => 'Különféle',
        'other' => 'Egyéb',
    ],

    'diet' => [
        'none' => 'Nincs speciális diéta',
        'vegetarian' => 'Vegetáriánus',
        'vegan' => 'Vegán',
        'gluten' => 'Gluténmentes',
        'lactose' => 'Laktózmentes',
        'keto' => 'Keto',
    ],

    'heights' => [
        '149' => '150 cm-nél kisebb',
        '150' => '150 cm',
        '151' => '151 cm',
        '152' => '152 cm',
        '153' => '153 cm',
        '154' => '154 cm',
        '155' => '155 cm',
        '156' => '156 cm',
        '157' => '157 cm',
        '158' => '158 cm',
        '159' => '159 cm',
        '160' => '160 cm',
        '161' => '161 cm',
        '162' => '162 cm',
        '163' => '163 cm',
        '164' => '164 cm',
        '165' => '165 cm',
        '166' => '166 cm',
        '167' => '167 cm',
        '168' => '168 cm',
        '169' => '169 cm',
        '170' => '170 cm',
        '171' => '171 cm',
        '172' => '172 cm',
        '173' => '173 cm',
        '174' => '174 cm',
        '175' => '175 cm',
        '176' => '176 cm',
        '177' => '177 cm',
        '178' => '178 cm',
        '179' => '179 cm',
        '180' => '180 cm',
        '181' => '181 cm',
        '182' => '182 cm',
        '183' => '183 cm',
        '184' => '184 cm',
        '185' => '185 cm',
        '186' => '186 cm',
        '187' => '187 cm',
        '188' => '188 cm',
        '189' => '189 cm',
        '190' => '190 cm',
        '191' => '191 cm',
        '192' => '192 cm',
        '193' => '193 cm',
        '194' => '194 cm',
        '195' => '195 cm',
        '196' => '196 cm',
        '197' => '197 cm',
        '198' => '198 cm',
        '199' => '199 cm',
        '200' => '200 cm vagy több',
    ],

    'heights_grouped' => [
        '149' => 'less than 150 cm',
        '150' => '150 cm - 154 cm',
        '151' => '150 cm - 154 cm',
        '152' => '150 cm - 154 cm',
        '153' => '150 cm - 154 cm',
        '154' => '150 cm - 154 cm',
        '155' => '155 cm - 159 cm',
        '156' => '155 cm - 159 cm',
        '157' => '155 cm - 159 cm',
        '158' => '155 cm - 159 cm',
        '159' => '155 cm - 159 cm',
        '160' => '160 cm - 164 cm',
        '161' => '160 cm - 164 cm',
        '162' => '160 cm - 164 cm',
        '163' => '160 cm - 164 cm',
        '164' => '160 cm - 164 cm',
        '165' => '165 cm - 169 cm',
        '166' => '165 cm - 169 cm',
        '167' => '165 cm - 169 cm',
        '168' => '165 cm - 169 cm',
        '169' => '165 cm - 169 cm',
        '170' => '170 cm - 174 cm',
        '171' => '170 cm - 174 cm',
        '172' => '170 cm - 174 cm',
        '173' => '170 cm - 174 cm',
        '174' => '170 cm - 174 cm',
        '175' => '175 cm - 179 cm',
        '176' => '175 cm - 179 cm',
        '177' => '175 cm - 179 cm',
        '178' => '175 cm - 179 cm',
        '179' => '175 cm - 179 cm',
        '180' => '180 cm - 184 cm',
        '181' => '180 cm - 184 cm',
        '182' => '180 cm - 184 cm',
        '183' => '180 cm - 184 cm',
        '184' => '180 cm - 184 cm',
        '185' => '185 cm - 189 cm',
        '186' => '185 cm - 189 cm',
        '187' => '185 cm - 189 cm',
        '188' => '185 cm - 189 cm',
        '189' => '185 cm - 189 cm',
        '190' => '190 cm - 194 cm',
        '191' => '190 cm - 194 cm',
        '192' => '190 cm - 194 cm',
        '193' => '190 cm - 194 cm',
        '194' => '190 cm - 194 cm',
        '195' => '195 cm - 199 cm',
        '196' => '195 cm - 199 cm',
        '197' => '195 cm - 199 cm',
        '198' => '195 cm - 199 cm',
        '199' => '195 cm - 199 cm',
        '200' => '200 cm - 204 cm',
        '201' => '200 cm - 204 cm',
        '202' => '200 cm - 204 cm',
        '203' => '200 cm - 204 cm',
        '204' => '200 cm - 204 cm',
        '205' => '205 cm - 209 cm',
        '206' => '205 cm - 209 cm',
        '207' => '205 cm - 209 cm',
        '208' => '205 cm - 209 cm',
        '209' => '205 cm - 209 cm',
        '210' => '210 cm - 214 cm',
        '211' => '210 cm - 214 cm',
        '212' => '210 cm - 214 cm',
        '213' => '210 cm - 214 cm',
        '214' => '210 cm - 214 cm',
        '215' => '215 cm or more',
    ],

    'heights_grouped_short' => [
        '140' => '150 cm-nél kisebb',
        '150' => '150 cm - 159 cm',
        '160' => '160 cm - 169 cm',
        '170' => '170 cm - 179 cm',
        '180' => '180 cm - 189 cm',
        '190' => '190 cm - 199 cm',
        '200' => '200 cm vagy több',
    ],

    'weights' => [
        '45' => 'legfeljebb 45 kg',
        '50' => '50 kg',
        '55' => '55 kg',
        '60' => '60 kg',
        '65' => '65 kg',
        '70' => '70 kg',
        '75' => '75 kg',
        '80' => '80 kg',
        '85' => '85 kg',
        '90' => '90 kg',
        '95' => '95 kg',
        '100' => 'legalább 100 kg',
    ],

    'email_frequency' => [
        '-1' => 'Valós idejű',
        '1' => 'Never',
        '3' => 'Háromnaponta',
        '7' => 'Heti',
        '15' => 'Kéthetente',
        '30' => 'Minden hónapban',
    ],

    'distance' => [
        '50',
        '100',
        '150',
        '200',
        '250',
        '300',
    ],

    'reasons_delete' => [
        'break' => 'Szünetet akarok tartani',
        'partner' => 'Találtam egy partnert',
        'adult' => 'Nem tudtam, hogy ez egy felnőtt szolgáltatás',
        'other' => 'Más:',
    ],

    'reasons_pause' => [
        'focus' => 'A magánéletemre szeretnék koncentrálni',
        'credit' => 'Hitelkártyás problémáim vannak',
        'dislike' => 'Nem szeretem a beszélgetéseket',
        'others' => 'Egyéb:',
    ],

    'archive_folders' => [
        '1' => 'Fontos',
        '2' => 'Utolérje később',
    ],

    'issue_types' => [
        'Fizetés',
        'Kreditek/Tokenek',
        'Visszatérítés',
        'Leiratkozás',
        'Profilbeállítások',
        'Technikai problémák',
        'Egyéb',
    ],

    'flirts_default' => '[
    {
        "name":"starter_clean_1",
        "description":"A profilod nagyon felkeltette a figyelmemet, szeretnél néhány mókás emléket összehozni együtt?"
    },
    {
        "name":"starter_clean_2",
        "description":"Csak én vagyok így, vagy minden világosabb lett, amikor megláttam a fotódat?"
    },
    {
        "name":"starter_clean_3",
        "description":"Az az érzésem, hogy Ön lehet az oka annak, hogy végül töröltem a fiókomat."
    },
    {
        "name":"starter_clean_4",
        "description":"Olyan valakinek tűnik, akit szívesen megismernék, szeretne beszélgetni?"
    },
    {
        "name":"starter_clean_5",
        "description":"Nem tudtam megállni, hogy szia, a hangulatod annyira hívogató."
    },
    {
        "name":"starter_clean_6",
        "description":"Mi az, amiért rajongsz? Szívesen hallok róla."
    }]',
];
