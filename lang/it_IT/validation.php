<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'day' => 'Il giorno non è valido.',
    'month' => 'Il mese non è valido.',
    'year' => 'Anno non valido.',
    'age' => 'Età non valida. Devi avere più di 18',
    'accepted' => 'L\'attributo :attribute deve essere accettato.',
    'active_url' => 'L\'attributo :attribute non è un URL valido.',
    'after' => 'L\'attributo :attribute deve essere una data successiva a :date.',
    'after_or_equal' => 'L\'attributo :attribute deve essere una data successiva o uguale a :date.',
    'alpha' => 'L\'attributo :attribute può contenere solo lettere.',
    'alpha_dash' => 'L\'attributo :attribute può contenere solo lettere, numeri, trattini e trattini bassi.',
    'alpha_num' => 'L\'attributo :attribute può contenere solo lettere e numeri.',
    'array' => 'L\'attributo :attribute deve essere un array.',
    'before' => 'L\'attributo :attribute deve essere una data prima di :date.',
    'before_or_equal' => 'L\'attributo :attribute deve essere una data precedente o uguale a :date.',
    'between' => [
        'numeric' => 'L\'attributo :attribute deve essere compreso tra :min e :max.',
        'file' => 'L\'attributo :attribute deve essere compreso tra :min e :max kilobyte.',
        'string' => 'L\'attributo :attribute deve essere compreso tra :min e :max caratteri.',
        'array' => 'L\'attributo :attribute avere elementi compresi tra :min e :max.',
    ],
    'boolean' => 'Il campo :attribute deve deve essere vero o falso.',
    'confirmed' => 'La conferma :attribute non corrisponde.',
    'date' => 'L\'attributo :non è una data valida.',
    'date_equals' => 'L\'attributo :attribute deve essere una data uguale a :date.',
    'date_format' => 'L\'attributo :attribute non corrisponde al formato :format.',
    'different' => 'L\'attributo :attribute e :altro devono deve essere diversi.',
    'digits' => 'L\'attributo :attribute deve essere :cifre cifre.',
    'digits_between' => 'L\'attributo :attribute essere compreso tra :min e :max cifre.',
    'dimensions' => 'L\'attributo :attribute ha dimensioni dell\'immagine non valide.',
    'distinct' => 'Il campo :attribute ha un valore duplicato.',
    'email' => 'L\'attributo :attribute non essere un indirizzo email valido.',
    'ends_with' => 'L\'attributo :attribute deve terminare con uno dei seguenti: :values',
    'exists' => 'L\'attributo selezionato :attribute non è valido.',
    'file' => 'L\'attributo :attribute non essere un file.',
    'filled' => 'Il campo :attribute deve avere un valore.',
    'gt' => [
        'numeric' => 'L\'attributo :attribute deve essere maggiore di :value.',
        'file' => 'L\'attributo :attribute deve essere maggiore di :valore kilobyte.',
        'string' => 'L\'attributo :attribute deve essere maggiore di :caratteri valore.',
        'array' => 'L\'attributo :attribute deve avere più di :elementi di valore.',
    ],
    'gte' => [
        'numeric' => 'L\'attributo :attribute deve essere maggiore o uguale a :value.',
        'file' => 'L\'attributo :attribute deve essere maggiore o uguale a :valore kilobyte.',
        'string' => 'L\'attributo :attribute deve essere maggiore o uguale a :caratteri di valore.',
        'array' => 'L\'attributo :attribute deve avere :elementi di valore o più.',
    ],
    'image' => 'L\'attributo :attribute deve essere un\'immagine.',
    'in' => 'L\'attributo :selezionato non è valido.',
    'in_array' => 'Il campo :attribute non esiste in :altro.',
    'integer' => 'L\'attributo :attribute deve essere un numero intero.',
    'ip' => 'L\'attributo :attribute deve essere un indirizzo IP valido.',
    'ipv4' => 'L\'attributo :attribute deve essere un indirizzo IPv4 valido.',
    'ipv6' => 'L\'attributo :attribute deve essere un indirizzo IPv6 valido.',
    'json' => 'L\'attributo :attribute deve essere una stringa JSON valida.',
    'lt' => [
        'numeric' => 'L\'attributo :attribute deve essere minore di :value.',
        'file' => 'L\'attributo :attribute deve essere inferiore a :valore kilobyte.',
        'string' => 'L\'attributo :attribute deve essere inferiore a :caratteri valore.',
        'array' => 'L\'attributo :attribute deve avere meno di :elementi di valore.',
    ],
    'lte' => [
        'numeric' => 'L\'attributo :attribute deve essere minore o uguale a :value.',
        'file' => 'L\'attributo :attribute deve essere minore o uguale a :valore kilobyte.',
        'string' => 'L\'attributo :attribute deve essere minore o uguale a :caratteri di valore.',
        'array' => 'L\'attributo :attribute non deve avere più di :elementi di valore.',
    ],
    'max' => [
        'numeric' => 'L\'attributo :attribute non può essere maggiore di :max.',
        'file' => 'L\'attributo :attribute non può essere maggiore di :max kilobyte.',
        'string' => 'L\'attributo :attribute non può essere maggiore di :max caratteri.',
        'array' => 'L\'attributo :attribute non può contenere più di :max elementi.',
    ],
    'mimes' => 'L\'attributo :attribute deve essere un file di tipo: :valori.',
    'mimetypes' => 'L\'attributo :attribute deve essere un file di tipo: :valori.',
    'min' => [
        'numeric' => 'L\'attributo :attribute deve essere almeno :min.',
        'file' => 'L\'attributo :attribute deve essere almeno :min kilobyte.',
        'string' => 'L\'attributo :attribute contenere almeno :min caratteri.',
        'array' => 'L\'attributo :attribute avere almeno :min elementi.',
    ],
    'not_in' => 'L\'attributo selezionato :attribute non è valido.',
    'not_regex' => 'Il formato :attribute non è valido.',
    'numeric' => 'L\'attributo :attribute deve essere un numero.',
    'password' => 'La password non è corretta.',
    'present' => 'Il campo :attribute deve essere presente.',
    'regex' => 'Il formato :attribute non è valido.',
    'required' => 'Il campo :attribute è obbligatorio',
    'required_if' => 'Il campo :attribute è obbligatorio quando :other è :value.',
    'required_unless' => 'Il campo :attribute è obbligatorio a meno che :other non sia in :values.',
    'required_with' => 'Il campo :attribute è obbligatorio quando :values ​​è presente.',
    'required_with_all' => 'Il campo :attribute è obbligatorio quando sono presenti :valori.',
    'required_without' => 'Il campo :attribute è obbligatorio quando :values ​​non è presente.',
    'required_without_all' => 'Il campo :attribute è obbligatorio quando nessuno dei :valori è presente.',
    'same' => 'L\'attributo :attribute e :other devono corrispondere.',
    'size' => [
        'numeric' => 'L\'attributo :attribute deve essere :size.',
        'file' => 'L\'attributo :attribute deve essere :size kilobyte.',
        'string' => 'L\'attributo :attribute deve essere :size caratteri.',
        'array' => 'L\'attributo :attribute contenere :size elementi.',
    ],
    'starts_with' => 'L\'attributo :attribute deve iniziare con uno dei seguenti: :values',
    'string' => 'L\'attributo :attribute deve essere una stringa.',
    'timezone' => 'L\'attributo :attribute deve essere una zona valida.',
    'unique' => 'L\'attributo :attribute è già stato preso.',
    'uploaded' => 'Impossibile caricare :attribute.',
    'url' => 'Il formato :attribute non è valido.',
    'uuid' => 'L\'attributo :attribute deve essere un UUID valido.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */
    'attributi' => [
        'username' => 'Nome utente',
        'e-mail' => 'e-mail',
        'password' => 'Password',
        'gender' => 'Sesso',
        'birthdate_day' => 'Giorno',
        'birthdate_month' => 'Mese',
        'birthdate_year' => 'Anno',
        'message' => 'Messaggio',
        'profile_image' => 'Foto del profilo',
        'comment' => 'Commento',
        'image' => 'Foto',
        'name' => 'Nome',
        'recaptcha_response_field' => 'reCAPTCHA',
        'product_id' => 'Crediti',
        'payment_profile' => 'Metodo di pagamento',
    ],
];
