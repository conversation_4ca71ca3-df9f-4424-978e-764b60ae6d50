<?php

use Illuminate\Support\Facades\Facade;

return [

    'env_tag' => env('APP_ENV_TAG', ''),

    'gcp_project_name' => env('GCP_PROJECT_NAME', ''),

    'mail_key' => env('APP_MAIL_KEY'),

    'thumb_folders' => ['thumbs', '300', '150'],

    'admin_api_key' => env('ADMIN_API_KEY'),

    'admin_api_url' => env('ADMIN_API_URL', 'myadmin.com'),

    'operators_bearer_token' => env('OPERATORS_BEARER_TOKEN'),

    'operators_api_url' => env('OPERATORS_API_URL', 'myoperatorservice.com'),

    'aliases' => Facade::defaultAliases()->merge([
        'Functions' => \App\Facade\Functions::class,
        'Image' => Intervention\Image\Facades\Image::class,
        'Redis' => Illuminate\Support\Facades\Redis::class,
        'Sentry' => Sentry\Laravel\Facade::class,
        'Html' => Spatie\Html\Html::class,
    ])->toArray(),

    'location' => env('APP_LOCATION'),

    'timezone' => env('APP_TIMEZONE', 'UTC'),

    'spam_report' => [
        'auth_token' => env('SPAM_REPORT_AUTH_TOKEN'),
        'unsubscribe_url' => 'https://spamreport.direct/api/unsubscribe',
    ],

    'lead_filter' => [
        'predict_url' => 'https://leadfilter.info/predict',
    ],

];
