<?php

return [

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'client_key' => env('GOOGLE_CLIENT_KEY'),
        'redirect' => '', // This is set in AppServiceProvider@setGoogleCallbackUrl
    ],

    'usebouncer' => [
        'url' => 'https://api.usebouncer.com/v1/',
        'key' => env('USEBOUNCER_KEY'),
    ],

    'pubsub' => [
        'user_filter' => [
            'project_id' => env('APP_INGEST_PROJECT_ID'),
            'credentials' => env('PUBSUB_KEY_FILE_PATH'),
        ],

        'logging' => [
            'project_id' => env('APP_PUBSUB_LOGGING_PROJECT_ID'),
            'credentials' => env('PUBSUB_KEY_FILE_PATH'),
        ],
    ],

    'user_filter' => [
        'query_url' => env('USER_FILTER_QUERY_URL'),
    ],

    'pastebin' => [
        'api_dev_key' => env('PASTEBIN_API_DEV_KEY'),
        'api_user_key' => env('PASTEBIN_API_USER_KEY'),
    ],

];
