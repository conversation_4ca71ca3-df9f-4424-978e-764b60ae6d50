<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class ProductPremiumFactory extends Factory
{
    public function definition(): array
    {
        return [
            'years' => $this->faker->numberBetween(1, 10),
            'months' => $this->faker->numberBetween(1, 12),
            'days' => $this->faker->numberBetween(1, 30),
            'credits' => $this->faker->numberBetween(200, 5000),
            'amount' => $this->faker->numberBetween(200, 5000) * 150,
        ];
    }
}
