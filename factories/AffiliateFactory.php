<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class AffiliateFactory extends Factory
{
    public function definition(): array
    {
        return [
            'email' => $this->faker->companyEmail,
            'password' => $this->faker->password,
            'alias' => $this->faker->name,
            'role' => 'stats_affiliate',
            'payout_currency' => 'usd',
            'affiliate_type' => 'ppl',
            'affiliate_share' => 0.00,
            'active' => true,
        ];
    }
}
