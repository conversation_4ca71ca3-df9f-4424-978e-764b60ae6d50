<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Mirzacles\Models\City;
use Mirzacles\Models\Country;
use Mirzacles\Models\Region;

class ProfileInfoFactory extends Factory
{
    public function definition(): array
    {
        $country = Country::query()->where('code', 'US')->first();
        $region = $this->faker->randomElement([Region::query()->where('country_id', $country->id)->inRandomOrder()->first(), null]);
        if ($region) {
            $city = City::query()->where('region_id', $region->id)->inRandomOrder()->first();
        } else {
            $city = null;
        }

        return [
            'age' => $this->faker->numberBetween(18, 99),
            'region_id' => $city ? $city->region_id : null,
            'city_id' => $city ? $city->id : null,
            'length' => $this->faker->randomElement([150, 152, 155, 157, 160, 163, 165, 168, 170, 173, 175, 178, 180, 183, 185, 188, 191]),
            'body_type' => $this->faker->randomElement(['athletic', 'average', 'bbwbhm', 'curvy', 'slender', 'voluptuous', 'other']),
            'eye_color' => $this->faker->randomElement(['black', 'brown', 'blue', 'green', 'gray', 'hazel', 'other']),
            'hair_color' => $this->faker->randomElement(['black', 'blonde', 'brown', 'red', 'gray', 'white', 'other']),
            'ethnicity' => $this->faker->randomElement(['white', 'black', 'latino', 'asian', 'arabic', null]),
            'smoking' => $this->faker->boolean,
            'piercing' => $this->faker->boolean,
            'tattoo' => $this->faker->boolean,
            'about' => $this->faker->text,
            'hobby' => '',
            'education' => '',
            'seeking' => '',
            'nature' => '',
            'shaved' => true,
            'weight' => '',
            'marital_status' => $this->faker->randomElement(['single', 'relationship', 'married', 'divorced', 'widowed']),
            'penis_length' => '',
            'drink' => $this->faker->boolean,
            'pref_cam' => $this->faker->boolean,
            'pref_kissing' => $this->faker->boolean,
            'pref_lingerie' => $this->faker->boolean,
            'pref_pictures' => $this->faker->boolean,
            'pref_massage' => $this->faker->boolean,
            'pref_bondage' => $this->faker->boolean,
            'pref_sadomasochism' => $this->faker->boolean,
            'pref_threesome' => $this->faker->boolean,
            'pref_group' => $this->faker->boolean,
            'pref_safe' => $this->faker->boolean(75),
            'pref_oral' => $this->faker->boolean,
            'pref_anal' => $this->faker->boolean(25),
            'pref_public' => $this->faker->boolean,
        ];
    }
}
