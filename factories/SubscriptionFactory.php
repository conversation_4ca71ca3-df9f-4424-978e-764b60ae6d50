<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class SubscriptionFactory extends Factory
{
    public function definition(): array
    {
        return [
            'is_active' => $this->faker->boolean,
            'currency' => $this->faker->randomElement([
                'gbp',
                'eur',
                'usd',
                'aud',
            ]),
            'price' => random_int(50, 50000),
            'days' => random_int(3, 60),
            'name' => $this->faker->randomElement([
                '3 Day Trial',
                '1 Month All Access',
            ]),
        ];
    }
}
