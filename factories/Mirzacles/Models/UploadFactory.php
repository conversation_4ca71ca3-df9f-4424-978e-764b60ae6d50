<?php

namespace Database\Factories\Mirzacles\Models;

use Illuminate\Database\Eloquent\Factories\Factory;
use Mirzacles\Models\Upload;

class UploadFactory extends Factory
{
    protected $model = Upload::class;

    public function definition(): array
    {
        return [
            'file' => $this->faker->sha256 . '.' . $this->faker->fileExtension(),
            'type' => 'upload',
            'active' => true,
            'site_id' => 1,
        ];
    }
}
