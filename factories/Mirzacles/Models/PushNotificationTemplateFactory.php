<?php

namespace Database\Factories\Mirzacles\Models;

use Illuminate\Database\Eloquent\Factories\Factory;
use Mirzacles\Models\PushNotificationTemplate;

class PushNotificationTemplateFactory extends Factory
{
    protected $model = PushNotificationTemplate::class;

    public function definition(): array
    {
        $title = $this->faker->text(50) . ' '
            . $this->faker->randomElement(['%city%', '%user_city%', '%age%', '%username%', '%profilename%']);

        $message = $this->faker->text(100) . ' '
            . $this->faker->randomElement(['%city%', '%user_city%', '%age%', '%username%', '%profilename%', '%message%']);

        return [
            'title' => $title,
            'message' => $message,
            'attachment' => false,
            'creator' => 'phpunit',
        ];
    }
}
