<?php

namespace Database\Factories\Mirzacles\Models;

use Illuminate\Database\Eloquent\Factories\Factory;
use Mirzacles\Models\MessageApiIncoming;

class MessageApiIncomingFactory extends Factory
{
    protected $model = MessageApiIncoming::class;

    public function definition(): array
    {
        return [
            'transaction_guid' => $this->faker->uuid,
            'message_guid' => $this->faker->uuid,
            'type' => MessageApiIncoming::TYPE_MESSAGE,
        ];
    }
}
