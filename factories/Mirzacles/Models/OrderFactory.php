<?php

namespace Database\Factories\Mirzacles\Models;

use App\Models\Domain;
use App\Models\ProductPremium;
use Illuminate\Database\Eloquent\Factories\Factory;
use Mirzacles\Models\Order;

class OrderFactory extends Factory
{
    protected $model = Order::class;

    public function definition(): array
    {
        return [
            'domain_id' => Domain::factory()->create()->id,
            'order_id' => $this->faker->numberBetween(1000, 100000),
            'product_id' => 0,
            'product_type' => ProductPremium::class,
            'amount_revshare' => 0,
            'payment_profile' => 3,
            'status' => 'paid',
            'ip' => '127.0.0.1',
            'site_id' => 1,
            'device' => 'pc',
        ];
    }
}
