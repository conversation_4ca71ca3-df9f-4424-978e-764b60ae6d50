<?php

namespace Database\Factories\Mirzacles\Models;

use Illuminate\Database\Eloquent\Factories\Factory;
use Mirzacles\Models\Visit;

class VisitFactory extends Factory
{
    protected $model = Visit::class;

    public function definition(): array
    {
        return [
            'site_id' => 1,
            'label_id' => 1,
            'ip' => '127.0.0.1',
            'device' => 'desktop',
            'landing' => 1,
            'click_affiliate_id' => 'phpunit_aff',
        ];
    }
}
