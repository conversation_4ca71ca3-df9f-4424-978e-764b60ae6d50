<?php

namespace Database\Factories;

use App\Models\Domain;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\DB;

class DomainFactory extends Factory
{
    public function configure()
    {
        return $this->afterMaking(function (Domain $domain) {
            //
        })->afterCreating(function (Domain $domain) {
            $domain->setMeta('show_certified_profiles', true);
        });
    }

    public function definition(): array
    {
        $country = DB::table('geo_countries')->where('code', 'US')->first(['id', 'code']);

        return [
            'type' => 'whitelabel',
            'whitelabel' => 'whitelabel.wildflings.com',
            'theme' => '003',
            'stylesheet' => '003',
            'url' => 'us3.clients.test',
            'name' => 'WildFlings',
            'name_url' => 'WildFlings.com',
            'mail_url' => 'mail.wildflings.com',
            'mail_url2' => 'mail.wildflings.com',
            'doi_mail' => '<EMAIL>',
            'https' => false,
            'alias' => 'US3',
            'site_id' => 1,
            'label_id' => 1,
            'items_per_page' => 24,
            'country_id' => $country->id,
            'country_code' => $country->code,
            'cdn_url' => 'us3.clients.test',
            'cdn_url_landing' => 'us3.clients.test',
            'activation_wall' => true,
            'invitation_alias' => 'sex proposal',
            'messages_order' => 'desc',
            'messages_pagination' => true,
            'pokes_status' => 'live',
            'uploads_edit_limit' => 4,
            'uploads_edit_types' => 'upload',
            'with_about_me' => true,
            'profile_with_messages' => false,
            'use_dynamic_regions' => true,
            'currency' => 'USD',
            'lang' => 'en',
            'locale' => 'en_US',
            'recaptcha_site_key' => '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI',
            'use_google_login' => true,
            'conversation_with_inbox' => true,
            'max_search_distance' => 30,
            'welcome_credits' => 1,
        ];
    }
}
