<?php

namespace App\Livewire;

use App\Models\Matches;
use App\Repository\MatchesRepository;
use App\Repository\UploadRepository;
use Illuminate\Http\Request;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use Livewire\Component;

class MatchGame extends Component
{
    private MatchesRepository $matchesRepository;

    private UploadRepository $uploadRepository;

    public $matchId;

    public $profile;

    public $candidate;

    public $noMoreCandidate = '';

    public $uploads;

    public $noLikes = false;

    #[Url]
    public $age_min = '';

    #[Url]
    public $age_max = '';

    #[Url]
    public $distance = '';

    #[Url]
    public $region_id = '';

    public function boot(MatchesRepository $matchesRepository, UploadRepository $uploadRepository)
    {
        $this->matchesRepository = $matchesRepository;
        $this->uploadRepository = $uploadRepository;
    }

    public function mount($matchId = null, $candidate = null)
    {
        $user = auth()->user();

        if ($user->isPremium()) {
            $count = $this->matchesRepository->getTodayLikes($user);

            if ($count >= 40) {
                $this->noMoreCandidate = 'premium';

                return;
            }
        } else {
            $count = $this->matchesRepository->getAllLikes($user);

            if ($count >= 10) {
                $this->noLikes = true;
            }
        }

        $this->candidate = $candidate;
        $this->matchId = $matchId;

        $this->uploads = $this->uploadRepository->getUploadsOfUser($this->candidate['candidate'], ['upload'], 6, true);
    }

    public function nextCandidate($type)
    {
        $user = auth()->user();

        $request = new Request;

        $request->merge([
            'age_min' => $this->age_min,
            'age_max' => $this->age_max,
        ]);

        if ($this->distance) {
            $request->merge([
                'distance' => $this->distance,
            ]);
        }

        if ($this->region_id) {
            $request->merge([
                'region_id' => $this->region_id,
            ]);
        }

        if ($user->isPremium()) {
            $count = $this->matchesRepository->getTodayLikes($user);

            if ($count >= 40) {
                $this->noMoreCandidate = 'premium';

                return;
            }
        } else {
            $count = $this->matchesRepository->getAllLikes($user);

            if ($count >= 10) {
                $this->noLikes = true;
            }
        }

        $candidate = $this->matchesRepository->getCandidateOrCreate($request, $user);

        if ($candidate !== null) {
            $this->candidate = $candidate;
            $this->uploads = $this->uploadRepository->getUploadsOfUser($this->candidate['candidate'], ['upload'], 6, true);
        } else {
            $this->noMoreCandidate = 'no-candidate';

            return;
        }

        $this->dispatch('candidate-updated', $type);
    }

    public function undo(Matches $id)
    {
        $request = new Request;

        $request->merge([
            'age_min' => $this->age_min,
            'age_max' => $this->age_max,
        ]);

        if ($this->distance) {
            $request->merge([
                'distance' => $this->distance,
            ]);
        }

        if ($this->region_id) {
            $request->merge([
                'region_id' => $this->region_id,
            ]);
        }
        $this->matchesRepository->undo($request, $id);
        $this->nextCandidate('undo');
    }

    public function accept(Matches $id)
    {
        $domain = domain();
        $this->matchesRepository->accept($id);
        $randomNumber = rand(1, 100);
        if ($randomNumber <= 5 && auth()->user()->isPremium()) {
            $this->matchesRepository->accept($id, 1);
        }

        if ($this->matchesRepository->checkIfMatch($id)) {
            $this->dispatch('show-match-popup', ['candidate' => $this->candidate['candidate']->username, 'url' => route('conversation', ['type' => 'all', 'username' => $this->candidate['candidate']->username]), 'thumb' => thumb($this->candidate['candidate']->profile_image, $this->candidate['candidate']->gender)]);
        }

        $this->nextCandidate('accept');
    }

    public function decline(Matches $id)
    {
        $this->matchesRepository->decline($id);
        $this->nextCandidate('decline');
    }

    #[On('previousCandidateCheck')]
    public function previousCandidateCheck()
    {
        $request = new Request;

        $request->merge([
            'age_min' => $this->age_min,
            'age_max' => $this->age_max,
        ]);

        if ($this->distance) {
            $request->merge([
                'distance' => $this->distance,
            ]);
        }

        if ($this->region_id) {
            $request->merge([
                'region_id' => $this->region_id,
            ]);
        }
        $prevCandidateAvailable = $this->matchesRepository->previousCandidateCheck($request);
        $this->dispatch('previous-candidate', $prevCandidateAvailable);
    }

    #[On('accept')]
    public function updateAccept()
    {
        $match = $this->matchesRepository->get($this->candidate['matchId']);
        $this->accept($match);
    }

    #[On('decline')]
    public function updateDecline()
    {
        $match = $this->matchesRepository->get($this->candidate['matchId']);
        $this->decline($match);
    }

    #[On('undo')]
    public function updateUndo()
    {
        $match = $this->matchesRepository->get($this->candidate['matchId']);
        $this->undo($match);
    }

    public function render()
    {
        return view('livewire.match-game');
    }
}
