<?php

namespace App\Livewire;

use App\Repository\UserRepository;
use Illuminate\Support\Facades\Redirect;
use Livewire\Component;
use Mirzacles\Models\UserDelete;

class DeactivateAccount extends Component
{
    private UserRepository $userRepository;

    public $reason = '';

    public $otherText = '';

    public function boot(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function deactivate()
    {
        $user = \Auth::user();

        if ($this->reason) {
            UserDelete::create([
                'user_id' => $user->id,
                'reason' => $this->reason,
                'other' => $this->otherText,
            ]);
        }

        $this->userRepository->deactivateUser($user);

        return Redirect::route('guestHome')->with(
            'success',
            __('You have successfully deleted your account. We are sorry to see you go...')
        );
    }

    public function render()
    {
        return view('livewire.deactivate-account');
    }
}
