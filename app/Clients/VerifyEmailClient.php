<?php

namespace App\Clients;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class VerifyEmailClient
{
    public const STATUS_OK = 'OK';

    public const STATUS_BAD = 'Bad';

    public const STATUS_BLACK = 'Black';

    private Client $client;

    private string $apiKey;

    public function __construct(Client $client, string $apiKey)
    {
        $this->client = $client;
        $this->apiKey = $apiKey;
    }

    /**
     * @return bool|object
     */
    public function verify(string $email)
    {
        try {
            $response = $this->client->get('/cgi-bin/emailchecker.cgi', [
                'query' => [
                    'key' => $this->apiKey,
                    'email' => $email,
                ],
                'headers' => [
                    'content-type' => 'application/json',
                ],
            ]);
        } catch (RequestException $exception) {
            Log::error('Failed to receive response', [
                'exception' => $exception->getMessage(),
                'email' => encrypt($email),
            ]);

            return false;
        }

        $result = json_decode($response->getBody());

        if ($result === null) {
            return false;
        }

        if (!(property_exists($result, 'status') && $result->status !== null)) {
            return true;
        }

        return $result;
    }
}
