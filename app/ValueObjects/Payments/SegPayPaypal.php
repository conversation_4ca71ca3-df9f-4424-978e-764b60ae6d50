<?php

namespace App\ValueObjects\Payments;

use Mirzacles\Models\DomainPaymentMethod;

class SegPayPaypal extends Payment
{
    private string $paymentLink;

    private string $packageId;

    private string $pricePointId;

    private ?string $userId;

    private ?string $userAccessKey;

    public function __construct(DomainPaymentMethod $paymentMethod)
    {
        parent::__construct($paymentMethod);
        $this->setPaymentLink($paymentMethod->providerConfig->config->payment_link);
        $this->setPackageId($paymentMethod->providerConfig->config->package_id);
        $this->setPricePointId($paymentMethod->providerConfig->config->price_point_id);
        $this->setUserId($paymentMethod->providerConfig->config->user_id ?? null);
        $this->setUserAccessKey($paymentMethod->providerConfig->config->user_access_key ?? null);
    }

    public function getPaymentLink(): string
    {
        return $this->paymentLink;
    }

    public function setPaymentLink(string $paymentLink): void
    {
        $this->paymentLink = $paymentLink;
    }

    public function getPackageId(): string
    {
        return $this->packageId;
    }

    public function setPackageId(string $packageId): void
    {
        $this->packageId = $packageId;
    }

    public function getPricePointId(): string
    {
        return $this->pricePointId;
    }

    public function setPricePointId(string $pricePointId): void
    {
        $this->pricePointId = $pricePointId;
    }

    public function getUserId(): ?string
    {
        return $this->userId;
    }

    public function setUserId(?string $userId): void
    {
        $this->userId = $userId;
    }

    public function getUserAccessKey(): ?string
    {
        return $this->userAccessKey;
    }

    public function setUserAccessKey(?string $userAccessKey): void
    {
        $this->userAccessKey = $userAccessKey;
    }
}
