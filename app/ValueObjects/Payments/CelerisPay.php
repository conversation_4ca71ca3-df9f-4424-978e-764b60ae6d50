<?php

namespace App\ValueObjects\Payments;

use Gateway\Common\GatewayConstants;
use Mirzacles\Models\DomainPaymentMethod;

class CelerisPay extends Payment
{
    private string $apiKey;

    private string $apiToken;

    private string $certPath;

    private string $privateKeyPath;

    private string $endpoint;

    private ?string $webhook;

    public function __construct(DomainPaymentMethod $paymentMethod)
    {
        parent::__construct($paymentMethod);
        $this->setApiKey($paymentMethod->providerConfig->config->api_key);
        $this->setApiToken($paymentMethod->providerConfig->config->api_token);
        $this->setCertPath($paymentMethod->providerConfig->config->cert_path);
        $this->setPrivateKeyPath($paymentMethod->providerConfig->config->private_key_path);
        $this->setEndpoint($paymentMethod->providerConfig->config->live);
        $this->setWebhook($paymentMethod->providerConfig->config->webhook ?? null);
    }

    public function getApiKey(): string
    {
        return $this->apiKey;
    }

    public function setApiKey(string $apiKey): void
    {
        $this->apiKey = $apiKey;
    }

    public function getApiToken(): string
    {
        return $this->apiToken;
    }

    public function setApiToken(string $apiToken): void
    {
        $this->apiToken = $apiToken;
    }

    public function getCertPath(): string
    {
        return $this->certPath;
    }

    public function setCertPath(string $certPath): void
    {
        $this->certPath = $certPath;
    }

    public function getPrivateKeyPath(): string
    {
        return $this->privateKeyPath;
    }

    public function setPrivateKeyPath(string $privateKeyPath): void
    {
        $this->privateKeyPath = $privateKeyPath;
    }

    public function getEndpoint(): string
    {
        return $this->endpoint;
    }

    public function setEndpoint(bool $live): void
    {
        $this->endpoint = $live ? GatewayConstants::API_ENDPOINT : GatewayConstants::TEST_API_ENDPOINT;
    }

    public function getWebhook(): ?string
    {
        return $this->webhook;
    }

    public function setWebhook(?string $webhook = null): void
    {
        $this->webhook = $webhook;
    }
}
