<?php

namespace App\ValueObjects\Payments;

use Mirzacles\Models\DomainPaymentMethod;

class Paynl extends Payment
{
    private string $code;

    private string $token;

    private string $serviceId;

    public function __construct(DomainPaymentMethod $paymentMethod)
    {
        parent::__construct($paymentMethod);
        $this->setCode($paymentMethod->providerConfig->config->code);
        $this->setToken($paymentMethod->providerConfig->config->token);
        $this->setServiceId($paymentMethod->providerConfig->config->service_id);
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): void
    {
        $this->code = $code;
    }

    public function getToken(): string
    {
        return $this->token;
    }

    public function setToken(string $token): void
    {
        $this->token = $token;
    }

    public function getServiceId(): string
    {
        return $this->serviceId;
    }

    public function setServiceId(string $serviceId): void
    {
        $this->serviceId = $serviceId;
    }
}
