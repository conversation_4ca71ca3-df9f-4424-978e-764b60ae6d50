<?php

namespace App\ValueObjects\Rewards;

use Illuminate\Database\Eloquent\Collection;

class RewardsTotalCreditsEarned
{
    private int $collected;

    private int $total;

    public function __construct(Collection $internRewards, Collection $externRewards)
    {
        $this->collected = $this->getCollectedCredits($internRewards) + $this->getCollectedCredits($externRewards);
        $this->total = $internRewards->sum('credits') + $externRewards->sum('credits');
    }

    public function getCollected(): int
    {
        return $this->collected;
    }

    public function getTotal(): int
    {
        return $this->total;
    }

    private function getCollectedCredits(Collection $rewards): int
    {
        return (int) $rewards->filter(function ($reward) {
            $rewardedUser = $reward->rewardedUsers->first();

            if ($rewardedUser === null) {
                return false;
            }

            return $rewardedUser->pivot->collected;
        })->sum('credits');
    }
}
