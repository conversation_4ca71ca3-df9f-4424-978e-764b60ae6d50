<?php

namespace App\ValueObjects;

use Mirzacles\Models\City;
use Mirzacles\Models\Country;
use Mirzacles\Models\Region;

class Location
{
    private $country;

    private $region;

    private $city;

    public function __construct(string $location)
    {
        if ($city = City::find($location)) {
            $this->city = $city;
            $this->region = $city->region;
            $this->country = $city->region->country;

            return;
        }
        if ($region = Region::find($location)) {
            $this->region = $region;
            $this->country = $region->country;
        }
    }

    public function getCountry(): ?Country
    {
        return $this->country;
    }

    public function getRegion(): ?Region
    {
        return $this->region;
    }

    public function getCity(): ?City
    {
        return $this->city ?? null;
    }
}
