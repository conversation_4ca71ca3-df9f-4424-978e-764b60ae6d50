<?php

namespace App\ValueObjects;

use App\Models\Domain;
use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;

class UserPaymentMethods
{
    private User $user;

    private Domain $domain;

    private Collection $paymentMethods;

    private string $preferredMethod = '';

    private Collection $switchableMethods;

    private bool $showHiddenProviders = false;

    private array $hiddenPaymentMethodIds = [];

    private array $hiddenProviders = [];

    public function get($user = null, $domain = null): Collection|\Illuminate\Support\Collection
    {
        $this->user = $user instanceof User ? $user : Auth::user();
        $this->domain = $domain instanceof Domain ? $domain : domain();
        $this->showHiddenProviders = $this->showHiddenProviders();
        $this->hiddenPaymentMethodIds = $this->getHiddenPaymentMethodIds();
        $this->paymentMethods = $this->domain->paymentMethods()->whereNotIn('id', $this->hiddenPaymentMethodIds)->get();

        if (!$this->paymentMethods->count() && !$this->domain->hasMeta('hidden_payment_providers')) {
            return collect();
        }

        // Get switchable payment methods
        $this->switchableMethods = $this->getSwitchablePaymentMethods();

        if ($this->switchableMethods->count() == 1) {
            $this->preferredMethod = $this->paymentMethods->first()->id;

            return $this->getPaymentMethods();
        }

        // Assign payment method according to percentage
        foreach ($this->switchableMethods as $switchableMethod) {
            // Force payment method if percentage is greater than 100
            if ($switchableMethod->percentage > 100) {
                $this->preferredMethod = $switchableMethod->id;

                return $this->getPaymentMethods();
            }
            if ($switchableMethod->percentage == 0 || $switchableMethod->percentage >= rand(1, 100)) {
                $this->preferredMethod = $switchableMethod->id;
                break;
            }
        }

        // Check to see which payment provider the user used last and set it as preferred
        if ($lastPaymentMethodId = $this->getLastPaymentMethodId()) {
            $this->preferredMethod = $lastPaymentMethodId;

            return $this->getPaymentMethods();
        }

        if (!$this->preferredMethod && $this->switchableMethods->count() > 0) {
            $this->preferredMethod = $this->switchableMethods->first()->id;
        }

        return $this->getPaymentMethods();
    }

    public function getPaymentMethods(): Collection
    {
        $hideMethods = $this->switchableMethods
            ->whereNotIn('id', $this->preferredMethod)
            ->pluck('id')
            ->toArray();

        $paymentMethods = $this->paymentMethods->whereNotIn('id', $hideMethods);

        if ($this->domain->hasMeta('hidden_payment_providers') && $this->showHiddenProviders) {
            $hiddenMethods = $this->domain->paymentMethods()->whereIn('id', $this->hiddenPaymentMethodIds)->get();
            $paymentMethods = $paymentMethods->merge($hiddenMethods);
        }

        return $paymentMethods->sortBy('order');
    }

    public function getLastPaymentMethodId(): ?string
    {
        return $this->user->orders()
            ->whereIn('payment_method_id', $this->switchableMethods->pluck('id'))
            ->when($this->user->hasPaid(), function ($query) {
                $query->where('status', Order::STATUS_PAID);
            })
            ->orderByDesc('created_at')
            ->first()?->payment_method_id;
    }

    public function getSwitchablePaymentMethods(): Collection
    {
        return $this->domain->paymentMethods()
            ->whereHas('type', function ($query) {
                $query->whereIn('name', ['credit_card', 'credit_card_hosted']);
            })
            ->whereNotIn('id', $this->getHiddenPaymentMethodIds())
            ->get();
    }

    public function getHiddenPaymentMethodIds(): array
    {
        if ($this->domain->hasMeta('hidden_payment_providers')) {
            $this->hiddenProviders = explode(',', $this->domain->getMeta('hidden_payment_providers'));

            return $this->domain->paymentMethods()->whereHas('provider', function ($query) {
                $query->whereIn('name', $this->hiddenProviders);
            })->pluck('id')->toArray();
        }

        return [];
    }

    public function showHiddenProviders(): bool
    {
        $meta = $this->domain->getMeta('show_hidden_providers_to');
        $usernames = $meta ? explode(',', $meta) : [];

        return in_array($this->user->username, $usernames);
    }
}
