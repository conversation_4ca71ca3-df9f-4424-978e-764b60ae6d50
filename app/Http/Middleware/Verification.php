<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class Verification
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, ?string $guard = null): Response
    {
        if (
            !$request->expectsJson() &&
            Auth::check() &&
            Auth::user()->hasMeta('verify_registration')
        ) {
            return redirect()->route('verification');
        }

        return $next($request);
    }
}
