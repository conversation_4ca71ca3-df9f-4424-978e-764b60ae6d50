<?php

namespace App\Http\Middleware;

use App\Helpers\Functions;
use App\Jobs\TouchUserJob;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Mirzacles\PubSub\UserFilterPublisher;
use Mirzacles\PubSub\UserFilterTopicEnum;
use Symfony\Component\HttpFoundation\Response;

class TouchUser
{
    public function handle(Request $request, Closure $next): Response
    {
        /** @var \App\Models\User $user */
        $user = $request->user();

        if ($user) {
            $previousUrl = $request->headers->get('referer');

            $domain = $user->domain;
            if (
                now()->subMinutes(10)->isAfter($user->updated_at) &&
                $previousUrl &&
                Str::contains($previousUrl, $domain->url)
            ) {
                if ($domain->hasModule('user_filter')) {
                    $userFilterPublisher = app(UserFilterPublisher::class);
                    $userFilterPublisher->publish(UserFilterTopicEnum::ACTIVITY, [
                        'source' => Functions::getGcpProjectName($domain->country_code) . '-s' . $user->site_id,
                        'email' => $user->email,
                    ]);
                }

                TouchUserJob::dispatchAfterResponse(Auth::user());
            }
        }

        return $next($request);
    }
}
