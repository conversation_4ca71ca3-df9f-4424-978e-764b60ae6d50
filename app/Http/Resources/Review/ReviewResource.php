<?php

namespace App\Http\Resources\Review;

use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * App\Http\Resources\Review\ReviewResource
 *
 * @property string $id
 * @property string $created_at
 * @property string $updated_at
 *
 * @method static Builder|ReviewResource newModelQuery()
 * @method static Builder|ReviewResource newQuery()
 * @method static Builder|ReviewResource query()
 *
 * @mixin Eloquent
 */
class ReviewResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,

            'created_at' => dateTimeFormat($this->created_at),
            'updated_at' => dateTimeFormat($this->updated_at),
        ];
    }
}
