<?php

namespace App\Http\Controllers;

use App\Repository\ConversationRepository;
use App\Repository\MessageRepository;
use App\Support\FlashMessage;
use Exception;
use Illuminate\Http\RedirectResponse;

class ConversationController extends Controller
{
    /**
     * @return void
     */
    public function __construct(
        private readonly ConversationRepository $conversationRepository,
        private readonly MessageRepository $messageRepository
    ) {
        $this->conversationRepository->setDomain(domain());
    }

    /**
     * @throws Exception
     */
    public function archiveConversation($id): RedirectResponse
    {
        $conversation = $this->conversationRepository->findOrFail($id);
        $this->conversationRepository->archiveConversation($conversation);

        $this->messageRepository->clearConversationsCache($conversation->user);

        return FlashMessage::success(__('The conversation has been archived'));
    }

    /**
     * @throws Exception
     */
    public function unArchiveConversation($id): RedirectResponse
    {
        $conversation = $this->conversationRepository->findOrFail($id);
        $this->conversationRepository->unArchiveConversation($conversation);

        $this->messageRepository->clearConversationsCache($conversation->user);

        return FlashMessage::success(__('The conversation has been moved to your inbox'));
    }
}
