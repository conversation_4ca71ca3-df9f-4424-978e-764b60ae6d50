<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Request;
use Intervention\Image\Laravel\Facades\Image;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class ImageController extends Controller
{
    public function appleIcon(Request $request): BinaryFileResponse
    {
        $domain = request()->attributes->get('domain');
        $image_path = public_path('theme/' . $domain->theme . '/images/favicon/apple-touch-icon.png');

        $image = Image::read($image_path)->toPng();

        return response()->file($image);
    }
}
