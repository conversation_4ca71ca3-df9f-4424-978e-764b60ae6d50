<?php

namespace App\Http\Controllers;

use App\Events\EmailVerifyRequested;
use App\Helpers\Functions;
use App\Models\User;
use App\Repository\EmailToxicityRepository;
use App\Repository\SettingsRepository;
use App\Repository\UserRepository;
use App\Rules\MatchOldPassword;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\View;
use Illuminate\Validation\Rule;
use Mirzacles\PubSub\UserFilterPublisher;
use Throwable;

class SettingController extends Controller
{
    const HEADER_TOKEN = 'a7eb07e5026ebbfd261a0d8e53fc97e8';

    /**
     * @return void
     */
    public function __construct(
        private readonly EmailToxicityRepository $emailToxicityRepository,
        private readonly SettingsRepository $settingsRepository,
        private readonly UserRepository $userRepository
    ) {}

    public function settings(Request $request): \Illuminate\Contracts\View\View
    {
        $domain = domain();

        /** @var User $user */
        $user = Auth::user();

        $allNotificationsDisabled = (
            !$user->settings->email_message &&
            !$user->settings->email_message_convo &&
            !$user->settings->email_flirt &&
            !$user->settings->email_favorite &&
            !$user->settings->email_visitor &&
            !$user->settings->email_new_members &&
            !$user->settings->email_promotional_offer &&
            !$user->settings->pushcrew_notes
        );

        $isUserToxic = $this->emailToxicityRepository->isToxic($user->email);
        $canEditNotifications = !$isUserToxic && !$user->hasMeta('needs_toxicity_scan') &&
            ($user->hasAffiliate() || $user->hasMeta('first_payment'));
        $emailSettings = $user->settings->email;

        return View::make('theme.' . $domain->theme . '.user.edit', [
            'page_title' => __('Account settings'),
            'user' => $request->user(),
            'menuSettings' => true,
            'page' => 'inbox',
            'domain' => $domain,
            'notifications_disabled' => $allNotificationsDisabled,
            'can_edit_notifications' => $canEditNotifications,
            'emailSettings' => $emailSettings,
        ]);
    }

    public function updateNotifications(Request $request): RedirectResponse
    {
        /** @var User $user */
        $user = $request->user();

        if ($request->has('pause-notifications')) {
            $this->settingsRepository->update($user, 0, 0, 0, 0, 0, 0, false, 0);

            $user->removeMeta('needs_toxicity_scan');

            return Redirect::back()->with('success', __('All notification emails have been paused'));
        }

        if ($request->has('resume-notifications')) {
            $this->settingsRepository->update($user, 1, 1, 1, 1, 1, 1, true, 1);

            return Redirect::back()->with('success', __('All notification emails have been resumed'));
        }

        if ($request->has('email_frequency')) {
            $emailSettings = [
                'frequency' => $request->get('email_frequency', 'all'),
                'timeframe' => $request->get('email_timeframe', ''),
            ];
            $this->settingsRepository->updateJson($user, $emailSettings);

            if ($request->get('email_frequency') == 'none') {
                $this->settingsRepository->update($user, 0, 0, 0, 0, 0, 0, false, 0);
            } else {
                $this->settingsRepository->update($user, 1, 1, 1, 1, 1, 1, true, 1);
            }
        } else {
            $this->settingsRepository->update(
                $user,
                $request->input('email_favorite', 0),
                $request->input('email_flirt', 0),
                $request->input('email_message', 0),
                $request->input('email_message_convo', 0),
                $request->input('email_visitor', 0),
                $request->input('email_new_members', 0),
                $request->input('email_promotional_offer', 0),
                $request->input('pushnotes', 0),
            );
        }

        return Redirect::back()->with('success', __('Your notification settings have been updated successfully'));
    }

    public function updatePassword(Request $request): RedirectResponse
    {
        /** @var User $user */
        $user = $request->user();

        $request->validate([
            'current_password' => ['required', new MatchOldPassword],
            'new_password' => ['required', 'min:6'],
            'confirm_new_password' => ['same:new_password'],
        ]);

        $this->userRepository->updatePassword($user, $request->input('new_password'));

        return Redirect::back()->with('success', __('Your password has been changed successfully'));
    }

    /**
     * @return bool|string
     *
     * @throws Throwable
     */
    public function updateEmail(Request $request)
    {
        $user = $request->user();

        $email = $request->input('email');
        $newEmail = $request->input('email_new');

        if ($email !== $user->email) {
            return back()->with('info', __('The current email is incorrect.'));
        }

        $repeatedEmail = $request->input('email_new2');

        if ($newEmail !== $repeatedEmail) {
            return back()->with('info', __('The given emails don\'t match. Please try again.'));
        }

        $validator = Validator::make($request->all(), [
            'email_new' => [
                'required',
                'email',
                'max:100',
                Rule::unique('users', 'email')->where(function (Builder $query) use ($user) {
                    $query->where('site_id', $user->site_id)
                        ->where('status', 1);
                }),
            ],
        ]);

        if ($validator->fails()) {
            return Redirect::back()->withErrors($validator)->withInput();
        }

        $this->userRepository->updateEmail($user, $newEmail);

        $user->update(['active' => false]);

        $request->session()->regenerate();

        event(new EmailVerifyRequested($user));

        return Redirect::back()->with('success', __('Your email has been changed successfully'));
    }

    public function unsubscribeSettings(): RedirectResponse
    {
        return redirect()->route('editSettings');
    }

    public function unsubscribe(Request $request): \Illuminate\Contracts\View\View
    {
        if (!Auth::check()) {
            return redirect()->back()->withErrors(['error' => __('This link has expired.')]);
        }

        $domain = domain();

        return View::make('theme.' . $domain->theme . '.unsubscribe');
    }

    public function unsubscribed(Request $request): \Illuminate\Contracts\View\View
    {
        $domain = domain();

        return View::make('theme.' . $domain->theme . '.unsubscribed');
    }

    public function unsubscribeConfirm(): RedirectResponse
    {
        $user = Auth::user();

        $this->settingsRepository->disableAllMail($user);
        $this->publishUnsubscribeAction($user);

        return redirect('unsubscribed');
    }

    public function unsubscribeInstant(Request $request): RedirectResponse
    {
        /** @var User $user */
        $user = $request->user();

        if (app()->environment('production')) {
            try {
                $headers = [
                    'Authorization' => config('app.spam_report.auth_token'),
                ];

                $response = Http::withHeaders($headers)->post(config('app.spam_report.unsubscribe_url'), [
                    'email' => $user->email,
                    'platform' => domain()->url,
                    'user_id' => $user->id,
                    'source' => 'list_unsub',
                ]);

                if ($response->failed()) {
                    Log::error('Spam report API not available or invalid request!', [
                        'response' => $response,
                    ]);
                }
            } catch (Throwable $throwable) {
                Log::error('Spam report API not available or not working!', [
                    'exception' => $throwable,
                ]);
            }
        }

        $this->settingsRepository->disableAllMail($user);
        $this->publishUnsubscribeAction($user);

        return redirect('unsubscribed');
    }

    private function publishUnsubscribeAction(User $user): void
    {
        if (!$user->domain->hasModule('user_filter')) {
            return;
        }

        app(UserFilterPublisher::class)->publishSimple('ecf__prod_chtp_wp_unsub_v1', [
            'source' => Functions::getGcpProjectName($user->domain->country_code) . '-s' . $user->site_id,
            'email' => $user->email,
        ]);
    }

    public function clearCache(Request $request): Response
    {
        $token = $request->header('ClearCacheToken');

        if ($token !== self::HEADER_TOKEN) {
            return response('Invalid token', 403)->header('Content-Type', 'text/plain');
        }

        Artisan::call('optimize:clear');

        return response('Optimize cleared ', 200)->header('Content-Type', 'text/plain');
    }

    public function clearViews(Request $request): Response
    {
        $token = $request->header('ClearCacheToken');

        if ($token !== self::HEADER_TOKEN) {
            return response('Invalid token', 403)->header('Content-Type', 'text/plain');
        }

        Artisan::call('view:clear');

        return response('Views cleared ', 200)->header('Content-Type', 'text/plain');
    }

    public function clearTopolCache(Request $request)
    {
        $token = $request->header('ClearTopolCacheToken');

        if ($token !== self::HEADER_TOKEN) {
            return response()->json(['success' => false, 'message' => 'Bad credentials']);
        }

        $label = strtolower($request->input('label'));
        $type = $request->input('type');

        if (!$label || !$type) {
            return response()->json(['success' => false, 'message' => 'Label or Email Type is Not Found']);
        }

        $tmtKey = \sprintf('tmt_%s_%s', $label, $type);

        if (Cache::has($tmtKey)) {
            Cache::forget($tmtKey);

            return response()->json([
                'success' => true,
                'message' => 'Cache Cleared',
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Cache not exists, nothing to do',
        ]);
    }
}
