<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\TransactionService;
use App\Support\FlashMessage;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illum<PERSON>\Routing\Controller as BaseController;
use Symfony\Component\HttpFoundation\Response;

class Controller extends BaseController
{
    use AuthorizesRequests;
    use ValidatesRequests;

    public function abortIfNotEnoughCredits(int $price, ?string $message = null, ?Response $response = null): void
    {
        $hasEnoughCredits = $this->hasEnoughCredits($price);

        if ($hasEnoughCredits) {
            return;
        }

        if (!$response) {
            $response = back();
        }

        if (!$message) {
            $message = __('Not enough credits');
        }

        FlashMessage::error($message);

        if (request()->ajax()) {
            abort(response()->json(['message' => $message, 'popup' => true], 403), $message);
        }

        abort($response, $message);
    }

    public function createTransaction(string $type, int $price): void
    {
        app()->make(TransactionService::class)->create(auth()->user(), $type, $price);
    }

    private function hasEnoughCredits(int $price): bool
    {
        /** @var User $user */
        $user = auth()->user();

        if (!$user->hasEnoughCredits($price)) {
            if ($user->credits === $price) {
                session()->put('out_of_credits', true);
            }

            return false;
        }

        return true;
    }
}
