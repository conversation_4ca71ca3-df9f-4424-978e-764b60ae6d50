<?php

namespace App\Http\Controllers;

use App\Repository\ListDataRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ListDataController extends Controller
{
    public function __construct(private readonly ListDataRepository $listDataRepository) {}

    public function getEmailAutoFills(Request $request): JsonResponse
    {
        return new JsonResponse($this->listDataRepository->getEmailAutoFills($request->input('domain')));
    }
}
