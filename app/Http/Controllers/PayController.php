<?php

namespace App\Http\Controllers;

use App\Aggregates\OrderAggregate;
use App\Exceptions\EventSourcing\OrderException;
use App\Helpers\Functions;
use App\Http\Controllers\Traits\HandlesPayments;
use App\Http\Requests\BuyProductRequest;
use App\Models\Affiliate;
use App\Models\Domain;
use App\Models\Order;
use App\Models\Product;
use App\Models\Profile;
use App\Models\User;
use App\Repository\AffiliateRepository;
use App\Repository\OrderRepository;
use App\Repository\PremiumRepository;
use App\Repository\ProductRepository;
use App\Repository\ProfileRepository;
use App\Repository\SubscriptionRepository;
use App\Repository\TrackRecordRepository;
use App\Repository\UserRepository;
use App\Services\ClientIp;
use App\Services\Payment\PaymentFactory;
use App\Support\CurrencyCode;
use App\ValueObjects\UserPaymentMethods;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Mirzacles\Models\Country;
use Mirzacles\Models\TrackRecord;
use Mirzacles\PubSub\UserFilterPublisher;
use Mirzacles\PubSub\UserFilterTopicEnum;
use Psr\Container\ContainerExceptionInterface;
use Throwable;

class PayController extends Controller
{
    use HandlesPayments;

    private ?Domain $domain;

    public function __construct(
        private readonly AffiliateRepository $affiliateRepository,
        private readonly OrderRepository $orderRepository,
        private readonly ProductRepository $productRepository,
        private readonly PremiumRepository $premiumRepository,
        private readonly SubscriptionRepository $subscriptionRepository,
        private readonly TrackRecordRepository $trackRecordRepository,
        private readonly UserRepository $userRepository,
        private readonly ProfileRepository $profileRepository
    ) {
        $this->domain = domain();
    }

    /**
     * @return Renderable|RedirectResponse
     *
     * @throws BindingResolutionException
     */
    public function products(Request $request)
    {
        /** @var User $user */
        $user = auth()->user();
        /** @var Domain $domain */
        $domain = domain();

        $this->trackRecordRepository->create($user, TrackRecord::VISIT_ORDER);

        $back = $this->getBackUrl();

        if ($domain->hasModule('store_popup')) {
            return redirect()->route('home')->with('show_store', true);
        }

        $bestSeller = $this->productRepository->getBestSeller();

        if (($profile_id = request()->query('profile'))) {
            $profile = Profile::find($profile_id);
            request()->session()->put('last_chat_profile', $profile);
        }

        $paymentMethods = app()->make(UserPaymentMethods::class)->get();

        $products = $domain->products()->orderBy('credits')->get();
        $trialSubscription = $domain->use_subscriptions ? $this->subscriptionRepository->getTrial() : null;

        $page_title = __('Credits');
        $themes = ['001', '005'];

        if (in_array($domain->theme, $themes)) {
            $page_title = __('Wallet');
        }

        if ($request->cookie('offer_id')) {
            $product = Product::where('id', $request->cookie('offer_id'))->get();
            $product_type = $product->first()->location;

            if ((Str::startsWith($product_type, 'promo') && !$user->hasPaid()) || $product_type == 'campaigns') {
                $page_title = __('Special offer');
                $offer = true;
                $products = $product;
            }
            if ($product_type == 'main') {
                $products = $product;
            }
            cookie()->expire('offer_id');
        }

        if ($request->cookie('order_id')) {
            $order_id = $request->cookie('order_id');
            cookie()->expire('order_id');
        }

        return View::make('theme.' . $this->domain->theme . '.payment.credits', [
            'page_title' => $page_title,
            'productType' => 'credits',
            'defaultSelected' => request()->input('bundle', $bestSeller?->id),
            'formRoute' => 'postTransaction',
            'profile' => $profile ?? null,
            'bestseller' => $bestSeller,
            'back' => $back,
            'paymentMethods' => $paymentMethods,
            'products' => $products,
            'is_offer' => $offer ?? false,
            'orderId' => $order_id ?? false,
            'ipAddress' => ClientIp::getIp($request),
            'countries' => Country::all(),
            'currencyCode' => CurrencyCode::getCurrencyCode($this->domain), // TODO: Move currency code to payment provider config
            'trialSubscription' => $trialSubscription,
        ]);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws Throwable
     * @throws OrderException
     */
    public function transaction(BuyProductRequest $request)
    {
        /** @var User $user */
        $user = auth()->user();
        if ($user->hasPaid() && $this->productIsOneTimeOffer($request->validated())) {
            return redirect()->back()->withErrors([__('This offer can be purchased only once')]);
        }

        $userIp = ClientIp::getIp($request);

        $order = $this->orderRepository->findOrCreate($request->validated(), domain(), $user, 'default', $userIp);
        Log::info('Transaction Order ID: ' . $order);

        if ($order->wasRecentlyCreated) {
            OrderAggregate::retrieve($order->id)->createOrder($order)->persist();
        }

        if ($request->has('payment_method_id') && $order->payment_method_id !== $request->input('payment_method_id')) {
            $order->setPaymentMethodId($request->input('payment_method_id'));
        }

        return PaymentFactory::create($order->payment_method_id, $request)->generate($order);
    }

    /**
     * Handle a user's return from a payment process.
     *
     * @throws Throwable
     */
    public function getReturn(Request $request): RedirectResponse
    {
        $order = $this->orderRepository->getOrFail(
            $request->input('order_number'),
            $request->input('user_id')
        );

        if ($request->input('saleID')) {
            $order->setExtId($request->input('saleID'));
        }

        if ($order->isPaid()) {
            return $this->returnSuccessResponse($order);
        }

        if ($order->isOfPaymentProfile(2, 3, 10) && !$order->isApproved()) {
            return $this->returnCancelResponse($order);
        }

        $this->handleSuccessfulPayment($order, __METHOD__);

        return $this->returnSuccessResponse($order);
    }

    public function failed(Request $request)
    {
        $order = $request->filled('orderNumber')
            ? Order::query()->where('order_id', $request->input('orderNumber'))->first()
            : null;

        if ($order === null || $order->isPaid()) {
            return redirect()->route('pay');
        }

        $order->update(['status' => Order::STATUS_FAILED]);
        $paymentMethod = $this->domain->paymentMethods->where('id', $order->payment_method_id)->first();
        $paymentMethods = app()->make(UserPaymentMethods::class)->get($order->user, $order->domain);
        $paymentMethods = $paymentMethods->whereNotIn('id', $order->payment_method_id)->sortBy('order');

        foreach ($paymentMethods as $pm) {
            if ($pm->type->name !== $pm::APPLE_PAY) {
                $paymentMethod = $pm;

                break;
            }
        }

        $user = $order->user;
        $domain = $user->domain;

        if ($domain->hasModule('user_filter')) {
            $userFilterPublisher = app(UserFilterPublisher::class);
            $source = Functions::getGcpProjectName($domain->country_code) . '-s' . $user->site_id;
            $amount = floatval($order->amount);

            $userFilterPublisher->publish(UserFilterTopicEnum::PAYMENT, [
                'source' => $source,
                'email' => $user->email,
                'amount' => $amount,
                'currency' => $domain->currency,
            ]);

            $affiliate = $user->aff_id ? Affiliate::where('alias', $user->aff_id)->first() : null;
            $paymentMethod = $order->paymentMethod;

            $userFilterPublisher->publishSimple('ecf__prod_chtp_wp_order_v1', [
                'source' => $source,
                'email' => $user->email,
                'order_id' => (string) $order->order_id,
                'amount' => $amount,
                'currency' => $domain->currency,
                'affiliate_share_percentage' => (int) $affiliate?->getAffiliateShare(),
                'amount_usd' => null,
                'payment_type' => $paymentMethod->type->name,
                'payment_provider' => $paymentMethod->provider->name,
                'status' => [
                    'string' => Order::STATUS_FAILED,
                ],
            ]);
        }

        return View::make('theme.' . $this->domain->theme . '.payment.failed', [
            'page_title' => __('Payment Denied'),
            'back' => route('pay'),
            'order' => $order,
            'paymentMethod' => $paymentMethod,
        ]);
    }

    /**
     * Handle a payment exchange request.
     *
     * @throws Throwable
     */
    public function exchange(): string
    {
        $event = (request()->input('event') === 'chargeback') ? 'chargeback' : 'payment';
        $orderNumber = request()->input('referenceID');
        $userId = request()->input('custom1');
        $order = $this->orderRepository->getOrFail($orderNumber, $userId);

        if ($event === 'payment' && !$order->isPaid()) {
            $this->handleSuccessfulPayment($order, __METHOD__);
        }

        if ($event === 'chargeback') {
            $this->handleChargeback($order);
        }

        return 'OK';
    }

    /**
     * @throws Throwable
     */
    public function exchangePost(): string
    {
        $event = (request()->input('transactionstatus') === 'ChargedBack' || request()->input('transactiontype') === 'Void' || request()->input('transactiontype') === 'Voided') ? 'chargeback' : 'payment';
        $order = $this->orderRepository->getOrFail(request()->input('referenceid'));

        if ($event === 'payment' && !$order->isPaid()) {
            $this->handleSuccessfulPayment($order, __METHOD__);
        }

        if ($event === 'chargeback') {
            $this->handleChargeback($order);
        }

        return 'OK';
    }

    public function offer(Request $request, $offer_id = null): RedirectResponse
    {
        /** @var Domain $domain */
        $domain = domain();

        if ($domain->hasModule('store_popup')) {
            return redirect($this->getBackUrl())
                ->with('show_store', true)
                ->with('offer_id', $offer_id)
                ->with('order_id', $request->input('order_id'));
        }

        return redirect()->route('pay')
            ->cookie('offer_id', $offer_id)
            ->cookie('order_id', $request->input('order_id'));
    }

    public function productIsOneTimeOffer($data): bool
    {
        if (array_key_exists('order_id', $data)) {
            $order = $this->orderRepository->getOrder($data['order_id']);
            if ($order !== null && Str::startsWith($order->product->location, 'promo')) {
                return true;
            }
        }
        if (array_key_exists('product_id', $data)) {
            $product = $this->productRepository->find($data['product_id']);
            if ($product !== null && Str::startsWith($product->location, 'promo')) {
                return true;
            }
        }

        return false;
    }

    public function getBackUrl(): string
    {
        return Str::contains(URL::previous(), $this->domain->url) &&
            URL::previous() !== URL::current() &&
            !Str::contains(URL::previous(), sprintf('offers.%s', $this->domain->url))
            ? URL::previous() : route('home');
    }
}
