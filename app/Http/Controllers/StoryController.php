<?php

namespace App\Http\Controllers;

use App\Exceptions\CreditException;
use App\Helpers\Resizer;
use App\Models\Upload;
use App\Models\User;
use App\Repository\StoryRepository;
use App\Services\UserCreditService;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Mirzacles\Models\Feed;
use Mirzacles\Models\Story;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class StoryController extends Controller
{
    /**
     * @return void
     */
    public function __construct(private readonly StoryRepository $storyRepository) {}

    public function index(Request $request): \Illuminate\Contracts\View\View
    {
        $domain = domain();
        $user = Auth::user();

        $unlockStory = DB::table('story_unlocked')
            ->select('story_id')
            ->where('user_id', $user->id)
            ->pluck('story_id')
            ->toArray();

        return View::make('theme.' . $domain->theme . '.stories.index', [
            'stories' => $this->storyRepository->allFromSite($user, 20),
            'menuStories' => true,
            'story_id' => $unlockStory,
        ]);
    }

    public function show(Request $request, string $story): \Illuminate\Contracts\View\View
    {
        $domain = domain();

        return View::make('theme.' . $domain->theme . '.stories.show', [
            'story' => $this->storyRepository->find($story, Auth::user()),
        ]);
    }

    /**
     * @throws CreditException
     */
    public function unlock(string $id): RedirectResponse
    {
        /** @var User $user */
        $user = Auth::user();

        if (!UserCreditService::hasEnoughCredits($user, Story::PRICE)) {
            return Redirect::back()
                ->with('error', 'Unfortunately you don\'t have enough D-Currency. Please <a href="/credits">click here to purchase D-Currency</a> to unlock this story.');
        }

        UserCreditService::pay($user, Story::PRICE);
        $this->storyRepository->unlock($id, $user);

        return Redirect::back()->with('success', 'You\'ve successfully unlocked this story!');
    }

    /**
     * @throws CreditException
     */
    public function create(Request $request): RedirectResponse
    {
        /** @var User $user */
        $user = Auth::user();

        if (!UserCreditService::hasEnoughCredits($user, 3)) {
            return Redirect::back()->with('error', 'Unfortunately you don\'t have enough credits. Please <a href="/credits">click here to purchase credits</a> to unlock this story.');
        }

        $validator = Validator::make($request->input(), [
            'title' => 'required|string',
            'story' => 'required|string',
            'type' => 'required',
        ]);

        $upload_id = ($request->hasFile('attachment')) ? $this->attachment($request->file('attachment')) : 0;

        if ($validator->fails()) {
            return Redirect::back()->withErrors($validator)->withInput();
        }

        UserCreditService::pay($user, 3);

        Feed::create([
            'user_id' => $user->id,
            'title' => $request->input('title'),
            'feed' => $request->input('story'),
            'type' => $request->input('type'),
            'upload_id' => $upload_id,
        ]);

        return Redirect::route('myFeedPosts')->with('success', 'Feed has been created');
    }

    public function myFeedPosts(Request $request): \Illuminate\Contracts\View\View
    {
        $domain = domain();

        return View::make('theme.' . $domain->theme . '.feed.my-feed-posts', [
            'stories' => $this->storyRepository->myFeedPosts(Auth::user(), 20),
            // 'nearby_profiles' => $this->profileRepository->nearbyProfiles()
        ]);
    }

    public function feedPosts(Request $request): \Illuminate\View\View
    {
        $domain = domain();
        $user = Auth::user();
        $stories = Story::query()
            ->where('profile_id', '!=', $user->id)
            ->whereHas('profile', function ($query) use ($user) {
                $query->where('site_id', $user->site_id);
            })
            ->where('concept', false)
            ->orderBy('updated_at', 'desc')
            ->paginate(5);

        return View::make('theme.' . $domain->theme . '.feed.index', [
            'stories' => $stories,
            // 'nearby_profiles' => $this->profileRepository->nearbyProfiles(),
            'menuFeedPosts' => true,
        ]);
    }

    private function attachment(UploadedFile $img): int|string
    {
        $imgName = $img->getClientOriginalName();
        $imgName = explode('.', $imgName);
        $imgName = array_pop($imgName);
        $dataName = date('i') . '' . Auth::user()->id . '' . date('s') . '' . Str::random(7);
        $imgName = $dataName . '.' . $imgName;
        $imgTMPName = $dataName . '-tmp.' . $imgName;
        $file = $img->move(public_path() . '/uploads/', $imgTMPName);

        try {
            Resizer::open($file)->resize(130, 130, 'crop')
                ->save(public_path() . '/uploads/150/' . $imgName, 95);
            Resizer::open($file)->resize(450, 450, 'crop')
                ->save(public_path() . '/uploads/' . $imgName, 95);

            if (file_exists(public_path() . '/uploads/' . $imgTMPName)) {
                unlink(public_path() . '/uploads/' . $imgTMPName);
            }

            $upload = new Upload;
            $upload->user_id = Auth::user()->id;
            $upload->type = 'message';
            $upload->file = $imgName;
            $upload->save();

            return $upload->id;
        } catch (Exception $e) {
            return 0;
        }
    }
}
