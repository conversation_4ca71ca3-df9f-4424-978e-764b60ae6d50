<?php

namespace App\Http\Controllers;

use App\Exceptions\ModelException;
use App\Helpers\Functions;
use App\Jobs\ProfileViewJob;
use App\Mappers\RewardInternMapper;
use App\Mappers\RewardsStatsMapper;
use App\Models\ChatGroupMember;
use App\Models\FavoritePhoto;
use App\Models\Profile;
use App\Models\Upload;
use App\Models\User;
use App\Repository\ApprovalRepository;
use App\Repository\ChatGroupRepository;
use App\Repository\FlirtRepository;
use App\Repository\MessageRepository;
use App\Repository\ProfileRepository;
use App\Repository\RewardInternRepository;
use App\Repository\SettingsRepository;
use App\Repository\TrackRecordRepository;
use App\Repository\UploadRepository;
use App\Repository\UserRepository;
use App\Rules\ValidBirthdate;
use App\Services\UserCreditService;
use App\Support\FlashMessage;
use App\ValueObjects\Location;
use App\ValueObjects\UserRequest;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Mirzacles\Helpers\Services\CacheKeyService;
use Mirzacles\Models\City;
use Mirzacles\Models\Favorite;
use Mirzacles\Models\PhotosUnlocked;
use Mirzacles\Models\Region;
use Mirzacles\Models\TrackRecord;
use Mirzacles\Models\UserDelete;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Throwable;

class UserController extends Controller
{
    /**
     * @return void
     */
    public function __construct(
        private readonly ApprovalRepository $approvalRepository,
        private readonly MessageRepository $messageRepository,
        private readonly ProfileRepository $profileRepository,
        private readonly RewardInternRepository $rewardInternRepository,
        private readonly UserRepository $userRepository,
        private readonly UploadRepository $uploadRepository,
        private readonly SettingsRepository $settingsRepository,
        private readonly FlirtRepository $flirtRepository,
        private readonly TrackRecordRepository $trackRecordRepository,
        private readonly ChatGroupRepository $chatGroupRepository
    ) {}

    /**
     * @throws Exception
     */
    public function all(Request $request): \Illuminate\Contracts\View\View|JsonResponse
    {
        $domain = domain();

        /** @var User $user */
        $user = Auth::user();

        if (!$request->query->get('page')) {
            $request->query->set('page', 1);
        }
        if (!$request->query->get('order')) {
            $request->query->set('order', 'random');
        }

        $default_age_min = $user->info->looking_for_age_min ?? 18;
        $default_age_max = $user->info->looking_for_age_max ?? 99;

        if ($domain->hasMeta('show_certified_profiles') && !$user->hasAffiliate()) {
            $default_age_min = 18;
            $default_age_max = 99;
        }

        $age_min = $request->query('age_min', $default_age_min);
        $age_max = $request->query('age_max', $default_age_max);

        $request->merge([
            'age_min' => $age_min,
            'age_max' => $age_max,
            'gender' => $user->seek,
        ]);

        $region_id_default = !$domain->use_distance && empty($request->input('region_id')) ? $user->info->region?->id : null;

        if ($domain->hasMeta('show_certified_profiles') && !$user->hasAffiliate() && !$request->filled('region_id')) {
            $region_id_default = null;
        }

        $onlineProfileIds = $this->profileRepository->getCachedOnlineProfileIds($user->site_id);
        $favoriteProfileIds = $user->favoriteProfileIds();

        if ($request->ajax() && $domain->theme == '004') {
            $profiles = $this->profileRepository->all($request, $user);
            $view = View::make('theme.' . $domain->theme . '.user.includes.profile', compact('profiles'))->render();

            return response()->json([
                'view' => $view,
            ]);
        }
        $profiles = $this->profileRepository->all($request, $user);

        $defaultFilters = [
            'gender' => $user->seek,
            'age_min' => $age_min,
            'age_max' => $age_max,
            'distance' => 300,
            'region_id' => $region_id_default,
            'ethnicity' => null,
            'hair_colors' => null,
            'favorite' => 0,
            'search' => '',
        ];

        $filter = [
            'gender' => $request->input('gender', $user->seek),
            'age_min' => $request->input('age_min', $age_min),
            'age_max' => $request->input('age_max', $age_max),
            'distance' => $request->input('distance', 300),
            'region_id' => !empty($request->input('region_id')) ? $request->input('region_id') : $region_id_default,
            'ethnicity' => $request->input('ethnicity'),
            'hair_colors' => $request->input('hair_colors'),
            'favorite' => $request->input('favorite', 0),
            'search' => $request->input('search', ''),
        ];

        $filtersChanged = collect($defaultFilters)->diffAssoc(collect($filter))->count() > 0;

        $regions = $domain->use_distance ? [] : Functions::regions();

        $regionNames = [];
        foreach ($regions as $region) {
            $regionNames[] = $region['name'];
        }

        $data = array_filter($request->all());

        return View::make('theme.' . $domain->theme . '.user.index', [
            'page_title' => __('Members'),
            'profiles' => $profiles,
            'online' => $onlineProfileIds,
            'menuHome' => true,
            'showFilter' => true,
            'options' => $user->info,
            'user' => $user,
            'favorites' => $favoriteProfileIds,
            'filter' => $filter,
            'filtersChanged' => $filtersChanged,
            'data' => $data,
            'has_token' => $request->has('token'),
            'has_filters' => $request->has('has_filters'),
            'regions' => $regions,
            'regionNames' => $regionNames,
        ]);
    }

    public function home(Request $request): \Illuminate\Contracts\View\View
    {
        $domain = domain();

        /** @var User $user */
        $user = Auth::user();
        $internRewards = $this->rewardInternRepository->getVisibleRewards($user->id);

        return View::make('theme.' . $domain->theme . '.user.home')->with([
            'intern_rewards_stats' => RewardsStatsMapper::map($internRewards),
            'intern_rewards' => RewardInternMapper::map($internRewards, $user),
            'menuDashboard' => true,
            'friendRequests' => $this->userRepository->getFriends($user, 0),
            'users' => $this->userRepository->getNewNearbyMembers($user),
        ]);
    }

    public function edit(Request $request): \Illuminate\Contracts\View\View
    {
        $domain = domain();
        $this->userRepository->setDomain($domain);

        /** @var User $user */
        $user = $request->user();
        $uploads = $this->uploadRepository->getUploadsOfUser($user, ['upload'], 6, true);
        $info = $this->userRepository->getUserInfo($user);
        $changedBirthdate = $user->hasTrackRecord('changed_birthdate');

        $view = 'theme.' . $domain->theme . '.user.settings';

        return View::make($view, [
            'page_title' => __('Profile settings'),
            'uploads' => $uploads,
            'info' => $info,
            'changedBirthdate' => $changedBirthdate,
        ]);
    }

    public function get(Request $request): \Illuminate\Contracts\View\View
    {
        $domain = domain();
        $this->userRepository->setDomain($domain);

        /** @var User $user */
        $user = Auth::user();
        $uploads = $this->uploadRepository->getUploadsOfUser($user, ['upload'], 6, true);
        $info = array_merge($this->userRepository->getUserInfo($user), $request->old());

        $nearbyProfiles = $this->profileRepository->nearbyProfiles();

        $view = 'theme.' . $domain->theme . '.user.profile';
        $regionName = '';

        if ($city = City::find($user->info->looking_for_region)) {
            $regionName = $city->region->name;
        }
        if ($region = Region::find($user->info->looking_for_region)) {
            $regionName = $region;
        }

        return View::make($view, [
            'page_title' => __('My Profile'),
            'nearbyProfiles' => $nearbyProfiles,
            'uploads' => $uploads,
            'info' => $info,
            'regionName' => $regionName,
        ]);
    }

    /**
     * @throws ModelException|Exception
     */
    public function member(string $username, Request $request)
    {
        $domain = domain();
        $user = $request->user();
        $messages = [];

        $this->profileRepository->setDomain($domain);
        $this->flirtRepository->setDomain($domain);

        try {
            $profile = $this->profileRepository->find($username);
        } catch (ModelNotFoundException) {
            return response()->view('errors.member-not-found', [], 404);
        }

        if ($user->seek !== $profile->gender) {
            return FlashMessage::error(__('You can only view members that match your preferences'));
        }

        $nearbyProfiles = new Collection;
        $similarProfiles = new Collection;

        if ($domain->hasModule('nearby_profiles')) {
            $nearbyProfiles = $this->profileRepository->nearbyProfiles($profile);
        }
        if ($domain->hasModule('similar_profiles')) {
            $similarProfiles = $this->profileRepository->similarProfiles($profile);
        }

        $uploads = $this->uploadRepository->getUploadsOfUser($profile, ['upload'], 2, true);
        $uploads = !$user->hasAffiliate() ? collect([]) : $uploads;

        $online = in_array($profile->id, $this->profileRepository->getCachedOnlineProfileIds($user->site_id), true);

        if (Auth::check() && $domain->hasModule('friends')) {
            $user->countUnlockedPhotoRequests(Carbon::now()->startOfDay());

            DB::table('unlock_photos_requests')
                ->where('user_id', $user->id)
                ->where('profile_id', $profile->id)->exists();
        }

        $favorite = Favorite::where('user_id', $user->id)
            ->where('profile_id', $profile->id)
            ->first();

        $isMatch = $favorite && $favorite->user_action && $favorite->profile_action;
        $profileFavoriteUser = $favorite && $favorite->profile_action && !$isMatch;

        $back = Str::contains(url()->previous(), $domain->url) ? url()->previous() : route('home');

        ProfileViewJob::dispatchAfterResponse($user, $profile);

        return View::make('theme.' . $domain->theme . '.user.profile-member', [
            'page_title' => ucfirst($profile->username),
            'profile' => $profile,
            'online' => $online,
            'menuProfile' => true,
            'messages' => $messages,
            'nearbyProfiles' => $nearbyProfiles,
            'similarProfiles' => $similarProfiles,
            'uploads' => $uploads,
            'back' => $back,
            'flirts' => $this->flirtRepository->all(),
            'isMatch' => $isMatch,
            'profileFavoriteUser' => $profileFavoriteUser,
        ]);
    }

    public function preferences(Request $request)
    {
        /** @var User $user */
        $user = $request->user();
        $domain = domain();

        if (!$domain->hasModule('preferences') || $user->initial_setup) {
            return redirect()->route('home');
        }

        $chat_groups = $this->chatGroupRepository->all();

        return View::make('theme.' . $domain->theme . '.user.preferences', [
            'chat_groups' => $chat_groups,
        ]);
    }

    public function savePreferences(Request $request): RedirectResponse
    {
        /** @var User $user */
        $user = $request->user();
        $domain = domain();

        if (!$domain->hasModule('preferences') || $user->initial_setup) {
            return redirect()->route('home');
        }

        if ($request->has('chat_groups')) {
            foreach ($request->input('chat_groups') as $groupId) {
                ChatGroupMember::updateOrCreate([
                    'chat_group_id' => $groupId,
                    'user_id' => $user->id,
                ], [
                    'chat_group_id' => $groupId,
                    'user_id' => $user->id,
                    'removable' => 0,
                ]);
            }
        }

        if ($request->has('interests')) {
            $preferencesArray = $request->input('interests');
            foreach ($preferencesArray as $key => $value) {
                if ($value === 'on' || $value == 1) {
                    $preferencesArray[$key] = true;
                }
            }

            $user->info->update($preferencesArray);
        }

        if ($request->has('about')) {
            $user->info->update(['about' => $request->input('about')]);
        }

        $user->update(['initial_setup' => true]);

        $route = View::exists('theme.' . $domain->theme . '.user.settings') ? 'editProfile' : 'userProfile';

        return redirect()->route($route);
    }

    public function postFilter(Request $request): RedirectResponse
    {
        return Redirect::route('filter', [
            $request->input('gender'),
            $request->input('age_min'),
            $request->input('age_max'),
            $request->input('place'),
            'status' => $request->input('status', 0),
            'photo' => $request->input('photo', 0),
        ])->withInput();
    }

    public function update(Request $request): RedirectResponse
    {
        /** @var User $user */
        $user = $request->user();
        $domain = domain();

        if (!$request->has('birthdate')) {
            $request->merge(['birthdate' => $user->info->birthdate]);
        }

        $rules = [
            'gender' => 'in:male,female',
            'about' => 'max:500',
            'birthdate' => [new ValidBirthdate],
        ];

        $data = $request->all();
        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            return Redirect::back()->withErrors($validator);
        }

        $about = Functions::removeEmoji(trim($request->get('about')));
        $about = strip_tags($about);
        $about = stripslashes($about);
        $about = Str::replace(['<', '>'], '', $about);

        // Check if there's no e-mail address in about me
        if (preg_match('/\b[A-Z0-9._%+\- ]+@[A-Z0-9.\- ]+\.[A-Z ]{2,4}\b/si', $about)) {
            return Redirect::back()->withErrors(
                [__('It is not allowed to share your e-mail address on your profile page.')]
            )->withInput();
        }

        if ($user->info->about != $about) {
            $user->modified_at = date('Y-m-d H:i:s');

            $this->approvalRepository->create($user);
            $this->trackRecordRepository->create($user, TrackRecord::CHANGED_BIO);
        }

        $infoArray = $request->input('interests');
        $userInfoArray = $user->info->toArray();
        $infoPreference = array_filter($userInfoArray, fn ($key) => substr($key, 0, 5) == 'pref_' && $userInfoArray[$key] == 1, ARRAY_FILTER_USE_KEY);

        if ($infoPreference !== $infoArray) {
            $this->trackRecordRepository->create($user, TrackRecord::CHANGED_PREFERENCES);
        }

        $request->merge(['about' => $about]);

        $birthdate = $request->input('birthdate');

        if (!empty($birthdate)) {
            $birthDateObj = Carbon::createFromFormat('Y-m-d', $birthdate);

            if ($birthdate !== $user->info->birthdate) {
                $this->trackRecordRepository->create($user, TrackRecord::CHANGED_BIRTHDATE);

                if ($domain->hasMeta('auto_age_range')) {
                    $user_age = $birthDateObj->age;
                    $age_min = $user_age - 15;
                    $age_max = $age_min + 20;

                    if ($age_min < 18) {
                        $age_min = 18;
                        $age_max = $age_min + 20;
                    }
                    if ($age_max > 99) {
                        $age_max = 99;
                        $age_min = $age_max - 20;
                    }

                    if ($age_min > 55) {
                        $age_min = 55;
                    }

                    $request->merge([
                        'looking_for_age_min' => $age_min,
                        'looking_for_age_max' => $age_max,
                    ]);
                }
            }

            $request->merge([
                'birth_year' => $birthDateObj->year,
                'birth_month' => $birthDateObj->month,
                'birth_day' => $birthDateObj->day,
            ]);
        } else {
            $birthdate = Carbon::parse($user->info->birthdate);
            $request->merge([
                'birth_year' => $birthdate->year,
                'birth_month' => $birthdate->month,
                'birth_day' => $birthdate->day,
            ]);
        }

        $this->userRepository->update($user, new UserRequest($request));

        if ($domain->theme === '004') {
            $cacheKey = CacheKeyService::nearbyProfiles($user);
            Cache::forget($cacheKey);
        }

        return Redirect::back()->with('success', __('Your changes have been saved successfully'));
    }

    public function deactivate(Request $request): RedirectResponse
    {
        $user = $request->user();

        if ($request->has('reason')) {
            UserDelete::create([
                'user_id' => $user->id,
                'reason' => $request->input('reason'),
                'other' => $request->input('other-text'),
            ]);
        }

        $this->userRepository->deactivateUser($user);

        return Redirect::route('guestHome')->with(
            'success',
            __('You have successfully deleted your account. We are sorry to see you go...')
        );
    }

    public function cancelSubscription(Request $request): RedirectResponse
    {
        $user = $request->user();
        $user->update(['premium' => null]);

        return Redirect::back()->with(
            'success',
            __('You have successfully cancelled your premium subscription.')
        );
    }

    public function deleted(Request $request): \Illuminate\Contracts\View\View
    {
        $domain = domain();

        return View::make('theme.' . $domain->theme . '.deleted');
    }

    public function pauseAccount(Request $request): RedirectResponse
    {
        $user = $request->user();

        if ($request->has('reason')) {

            UserDelete::create([
                'user_id' => $user->id,
                'reason' => 'pause-' . $request->input('reason'),
                'other' => $request->input('other-text'),
            ]);
        }
        $this->userRepository->pauseUser($user);

        return Redirect::route('guestHome')->with(
            'success',
            __('See you soon!')
        );
    }

    public function logout(Request $request): RedirectResponse
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::route('home');
    }

    public function addCity(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), ['cityId' => 'required']);

        if ($validator->fails()) {
            return Redirect::route('home')
                ->with('locationError', __('Please fill in your city'))
                ->withInput();
        }

        $location = new Location($request->get('cityId'));

        $this->userRepository->updateLocation(
            $request->user(),
            $location->getRegion(),
            $location->getCity()
        );

        return Redirect::route('home')
            ->with('registered', true)
            ->with('success', __('Your city of residence has been set!'));
    }

    public function verifyPrivacy(Request $request): RedirectResponse
    {
        $this->userRepository->confirmPrivacy($request->user());

        return Redirect::back();
    }

    public function verifyPrivacyJsonResponse(Request $request): JsonResponse
    {
        $this->userRepository->confirmPrivacy($request->user());

        return new JsonResponse(null, 200);
    }

    public function verifyPrivacyReject(Request $request): RedirectResponse
    {
        $this->userRepository->deactivateUser($request->user());

        return Redirect::route('home');
    }

    public function welcomeCredit(Request $request): RedirectResponse
    {
        $this->userRepository->markWelcomeCreditPopupAsRead($request->user());

        return Redirect::back();
    }

    /**
     * @throws Throwable
     *
     * @deprecated
     */
    public function report(Request $request, string $id): RedirectResponse
    {
        $request->validate([
            'reason' => 'required|array|filled',
        ]);

        $domain = domain();

        $this->profileRepository->setDomain($domain);

        $authenticatedUser = $request->user();
        $profile = $this->profileRepository->find($id);

        $reasons = $request->get('reason');
        $reasons[] = $request->get('other_reason');

        // TODO: Report user email
        // Mail::send(
        //     new MemberReportMail(
        //         ModelToMailObjectMapper::map($domain),
        //         ModelToMailObjectMapper::map($authenticatedUser),
        //         $reasons,
        //         ModelToMailObjectMapper::map($profile)
        //     )
        // );

        return Redirect::back()->with('success', __('This member has been reported. Thank you!'));
    }

    public function photos(Request $request): \Illuminate\Contracts\View\View
    {
        $domain = domain();

        /** @var User $user */
        $user = $request->user();

        return View::make('theme.' . $domain->theme . '.user.photos', [
            'user' => $user,
            'menuProfile' => Functions::owner($user->id),
        ]);
    }

    public function search(Request $request)
    {
        if (!$request->ajax()) {
            return redirect()->route('home');
        }

        $domain = domain();
        $this->profileRepository->setDomain($domain);

        $search = trim($request->get('search', ''));
        $paginator = $this->profileRepository->search($search);

        $items = $paginator
            ->getCollection()
            ->map(fn (Profile $profile) => [
                'profile_id' => $profile->id,
                'username' => $profile->username,
                'name' => $profile->name,
                'age' => $profile->info->age,
                'image' => thumb($profile->profile_image),
                'url' => route('profile', ['username' => $profile->username]),
                'is_online' => Functions::isOnline($profile->id),
            ])
            ->values();

        return response()->json($items);
    }

    public function myPosts(Request $request): \Illuminate\Contracts\View\View
    {
        /** @var User $user */
        $user = $request->user();

        $domain = domain();

        if ($request->input('type') === 'group') {
            $posts = $user->posts()->orderBy('created_at', 'desc')->paginate(24);

            return View::make('theme.' . $domain->theme . '.user.chat_group_posts', [
                'posts' => $posts,
                'user' => $user,
                'menuPosts' => true,
            ]);
        }

        $postIts = $user->postIts()
            ->with(['user'])
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        return View::make('theme.' . $domain->theme . '.user.post_its', [
            'post_its' => $postIts,
            'menuPosts' => true,
        ]);
    }

    public function myFavorites(Request $request): \Illuminate\Contracts\View\View
    {
        /** @var User $user */
        $user = $request->user();

        $domain = domain();

        if ($request->input('type') === 'group_post') {
            $posts = $user->likedChatGroupPosts()->orderBy('created_at', 'desc')->paginate(24);

            return View::make('theme.' . $domain->theme . '.user.favorite_chat_group_posts', [
                'posts' => $posts,
                'user' => $user,
                'menuFavorites' => true,
                'enough_credits' => UserCreditService::hasEnoughCredits($user),
            ]);
        }

        $postIts = $user->likedPostIts()
            ->with(['user'])
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        return View::make('theme.' . $domain->theme . '.user.favorite_post_its', [
            'post_its' => $postIts,
            'user' => $user,
            'menuFavorites' => true,
            'enough_credits' => UserCreditService::hasEnoughCredits($user),
        ]);
    }

    /**
     * When user clicks away the rewards popup
     */
    public function rewardsPopup(): RedirectResponse
    {
        DB::table('rewards_popup_accepted')->insertOrIgnore(['user_id' => Auth::user()->id]);

        return Redirect::route('rewards');
    }

    /**
     * @throws ModelException
     */
    public function mediaRequest(Request $request): RedirectResponse
    {
        $user = Auth::user();
        $profileId = $request->input('profile_id');

        $hasPhotoRequest = DB::table('unlock_photos_requests')
            ->where('user_id', $user->id)
            ->where('profile_id', $profileId)
            ->exists();

        if ($hasPhotoRequest) {
            return Redirect::back()->with('error', 'There\'s already a pending request from you.');
        }

        $profile = User::query()->select(['id', 'online'])->where('id', $profileId)->first();
        $this->userRepository->photoRequests($user, $profile);

        // Add message
        $this->messageRepository->addMessage(
            $user,
            $profile,
            $user->username . ' has requested you to see your photos!',
            null,
            'message',
            true,
            false
        );

        /*
        AcceptMediaRequestJob::dispatch($user, $profile)
            ->delay(Carbon::now()->addMinutes(($profile->online) ? 1 : rand(3, 15)))
            ->onQueue('l6_clients_mail_queue');
        */

        return Redirect::back()
            ->with('success', 'The request has been sent! You\'ll receive an e-mail once it\'s been accepted.');
    }

    public function myFriends(Request $request): \Illuminate\Contracts\View\View
    {
        $domain = domain();
        $user = Auth::user();
        $paginate = 12;
        $page = $request->get('page', 1);

        $getSentFriends = PhotosUnlocked::query()
            ->where('user_id', $user->id)
            ->get()
            ->map(fn (PhotosUnlocked $photosUnlocked) => $photosUnlocked->profile)
            ->filter();

        $getReceivedFriends = PhotosUnlocked::query()
            ->where('profile_id', $user->id)
            ->get()
            ->map(fn (PhotosUnlocked $photosUnlocked) => $photosUnlocked->user)
            ->filter();

        $allUsers = $getSentFriends->merge($getReceivedFriends)->unique();
        $randomProfiles = null;

        if ($domain->profile_use_random) {
            $randomProfiles = $this->userRepository->getRandomProfiles(4, $user);
        }

        return View::make('theme.' . $domain->theme . '.friends.index', [
            'users' => new LengthAwarePaginator($allUsers, $allUsers->count(), $paginate, $page),
            'random_profiles' => $randomProfiles,
            // 'nearby_profiles' => $this->profileRepository->nearbyProfiles(),
        ]);
    }

    public function allNotifications(Request $request): \Illuminate\Contracts\View\View
    {
        $domain = domain();
        /** @var User $user */
        $user = Auth::user();

        return View::make('theme.' . $domain->theme . '.notifications.index', [
            'friendRequests' => $this->userRepository->getFriends($user),
            'menuAll' => true,
            // 'nearby_profiles' => $this->profileRepository->nearbyProfiles(),
            'pendingReqCount' => $this->userRepository->getFriends($user, 0),
            'acceptedRequests' => $this->userRepository->acceptedRequests($user->id),
        ]);
    }

    public function pendingNotifications(Request $request): \Illuminate\Contracts\View\View
    {
        $domain = domain();
        $pending = $this->userRepository->getFriends(Auth::user(), 0);

        return View::make('theme.' . $domain->theme . '.notifications.pending', [
            'pendingRequests' => $pending,
            'menuPending' => true,
            // 'nearby_profiles' => $this->profileRepository->nearbyProfiles(),
            'pendingReqCount' => $pending,
        ]);
    }

    public function readNotifications(Request $request): \Illuminate\Contracts\View\View
    {
        $domain = domain();
        /** @var User $user */
        $user = Auth::user();

        return View::make('theme.' . $domain->theme . '.notifications.read', [
            'readRequests' => $this->userRepository->getFriends($user, [1, -1]),
            'menuRead' => true,
            // 'nearby_profiles' => $this->profileRepository->nearbyProfiles(),
            'pendingReqCount' => $this->userRepository->getFriends($user, 0),
        ]);
    }

    /**
     * @throws ModelException
     */
    public function photo(Request $request)
    {
        if (!$request->ajax()) {
            return Redirect::route('home');
        }

        $request->validate(['name' => 'string|nullable']);

        $domain = domain();
        $user = Auth::user();

        $filename = $request->input('name');
        $profile = $request->input('profile');
        if ($profile) {
            $profile = $this->profileRepository->find($request->input('profile'));
        }

        if ($filename) {
            if ($photo = $this->getUserPhoto($user, $filename, $request->input('fromMessages'))) {
                $chatUserPhoto = $photo['chatUserPhoto'];
            } elseif ($profile) {
                if ($photo = $this->getProfilePhoto($profile, $filename)) {
                    $comments = $this->messageRepository->getPhotoComments($user, $profile, $photo['uploadId']);
                }
            }

            $isFavoritePhoto = $photo && FavoritePhoto::where('user_id', $user->id)
                ->where('upload_id', $photo['uploadId'])->exists();
        }

        return View::make('theme.' . $domain->theme . '.partials.photo', [
            'filename' => $filename,
            'profile' => $profile,
            'photo_url' => $request->url(),
            'comments' => $comments ?? null,
            'upload_id' => $photo['uploadId'] ?? null,
            'isFavoritePhoto' => $isFavoritePhoto ?? false,
            'chatUserPhoto' => $chatUserPhoto ?? false,
        ]);
    }

    private function getUserPhoto($user, string $filename, ?bool $fromMessages): ?array
    {
        if ($filename === $user->profile_image) {
            return ['uploadId' => null, 'chatUserPhoto' => false];
        }

        $photo = Upload::where('member_id', $user->id)->where('file', $filename)->first();
        if ($photo) {
            return ['uploadId' => $photo->id, 'chatUserPhoto' => $fromMessages ?: false];
        }

        return null;
    }

    private function getProfilePhoto($profile, string $filename): ?array
    {
        $photo = Upload::query()
            ->where('member_id', $profile->id)
            ->where('file', $filename)->first();

        return $photo ? ['uploadId' => $photo->id] : null;
    }

    public function updateAboutMe(Request $request): RedirectResponse
    {
        /** @var User $user */
        $user = Auth::user();

        $validator = Validator::make($request->all(), ['about' => 'max:500']);
        if ($validator->fails()) {
            return Redirect::back()->withErrors($validator);
        }

        $about = Functions::removeEmoji(trim($request->get('about')));

        if ($about === '') {
            return Redirect::back()->with('success', __('Your changes have been saved successfully'));
        }

        // Check if there's no e-mail address in about me
        if (preg_match('/\b[A-Z0-9._%+\- ]+@[A-Z0-9.\- ]+\.[A-Z ]{2,4}\b/si', $about)) {
            return Redirect::back()->withErrors(
                [__('It is not allowed to share your e-mail address on your profile page.')]
            )->withInput();
        }

        if ($user->info->about != $about) {
            $user->modified_at = date('Y-m-d H:i:s');

            $this->approvalRepository->create($user);
            $this->trackRecordRepository->create($user, TrackRecord::CHANGED_BIO);
        }

        $user->info->update(['about' => $about]);

        $domain = domain();

        return Redirect::back()->with('success', __('Your changes have been saved successfully'));
    }

    /**
     * @throws ModelException
     */
    public function sendFriendRequest(Request $request): RedirectResponse
    {
        $domain = domain();

        $user = User::find(Auth::user()->id);
        $hasFriendRequest = DB::table('unlock_photos_requests')
            ->where('user_id', $user->id)
            ->where('profile_id', $request->get('profile_id'))->exists();

        if ($hasFriendRequest) {
            return Redirect::back()->with('error', __('There\'s already a pending request from you.'));
        }

        $profile = User::query()
            ->select(['id', 'username', 'online'])
            ->where('id', $request->get('profile_id'))
            ->first();

        if (Auth::check() && $domain->hasModule('friends')) {
            $exceedFriendRequests = $user->countUnlockedPhotoRequests(Carbon::now()->startOfDay());
            if (!Functions::premium($user->id) && $exceedFriendRequests >= 5) {
                return Redirect::back()->with(
                    'error',
                    __(
                        'You\'ve reached the limit of 5 friend requests per day. To send :username a friend request, become a super member!',
                        ['username' => $profile->username]
                    )
                );
            }
        }

        $this->userRepository->sendPhotoRequests($user, $profile, $request->get('message'));

        /*
        AcceptFriendRequestJob::dispatch($user->domain, $friendRequest)
            ->delay(now()->addMinutes(($profile->online) ? 2 : rand(3, 15)))
            ->onQueue('l6_clients_mail_queue');
        */

        return Redirect::back()
            ->with(
                'success',
                __('Your friend request has been sent. You will receive a notification when it\'s accepted.')
            );
    }

    public function updatePreferences(Request $request): RedirectResponse
    {
        $user = Auth::user();
        $inputBirthday = ($request->input('birth_year') && is_numeric($request->input('birth_year')) ? $request->input(
            'birth_year'
        ) : '1970') . '-' . ($request->input('birth_month') && is_numeric(
            $request->input('birth_month')
        ) ? $request->input('birth_month') : '01') . '-' . ($request->input('birth_day') && is_numeric(
            $request->input('birth_day')
        ) ? $request->input('birth_day') : '01');
        $userAge = Functions::returnAge($inputBirthday);

        if ($userAge < 18) {
            return Redirect::back()->with(
                'error',
                __('Registration is only allowed when you\'re at least 18 years old.')
            )->withInput();
        }

        $infoArray = [
            'penis_length' => $request->input('penis_length', ''),
            'pref_kissing' => $request->input('pref_kissing', 0),
            'pref_lingerie' => $request->input('pref_lingerie', 0),
            'pref_safe' => $request->input('pref_safe', 0),
            'pref_oral' => $request->input('pref_oral', 0),
            'pref_public' => $request->input('pref_public', 0),
            'pref_pictures' => $request->input('pref_pictures', 0),
            'pref_anal' => $request->input('pref_anal', 0),
            'pref_threesome' => $request->input('pref_threesome', 0),
            'pref_bondage' => $request->input('pref_bondage', 0),
            'pref_sadomasochism' => $request->input('pref_sadomasochism', 0),
            'pref_massage' => $request->input('pref_massage', 0),
            'pref_group' => $request->input('pref_group', 0),
            'pref_cam' => $request->input('pref_cam', 0),
        ];

        $preferences = array_filter($infoArray, fn ($key) => preg_match('/^pref_.+/', $key) != false, ARRAY_FILTER_USE_KEY);

        $userInfoArray = $user->info->toArray();
        $preferenceKeys = array_keys($preferences);
        $infoPreference = array_filter($userInfoArray, fn ($key) => in_array($key, $preferenceKeys), ARRAY_FILTER_USE_KEY);

        if ($preferences != $infoPreference) {
            $this->trackRecordRepository->create($user, TrackRecord::CHANGED_PREFERENCES);
        }

        if ($request->get('seek') !== null) {
            $preferences['seek'] = $request->get('seek');
        }

        if ($request->get('marital_status') !== null) {
            $preferences['marital_status'] = $request->get('marital_status');
        }

        $preferences['birthdate'] = $inputBirthday;

        $user->info->update($preferences);

        return Redirect::back()->with('success', __('Your changes have been saved successfully'));
    }

    /**
     * @return void
     */
    public function saveNotification(string $type, string $name, string $action)
    {
        $user = Auth::user();
        $this->settingsRepository->saveNotification($user, $type, $name, $action);
    }

    /**
     * @return void
     */
    public function deleteNotification(string $type, string $name, string $action)
    {
        $user = Auth::user();
        $this->settingsRepository->deleteNotification($user, $type, $name);
    }

    public function beacon(): BinaryFileResponse
    {
        $filename = 'spacer.png';
        $spacerPath = \resource_path('images/' . $filename);

        return new BinaryFileResponse($spacerPath, 200, [
            'Content-Type' => 'image/png',
            'Content-Length' => filesize($spacerPath),
            'Content-disposition' => sprintf('inline; filename="%s"', $filename),
        ]);
    }

    public function log(Request $request): void
    {
        Log::info('Frontend Log: ', $request->all());
    }
}
