<?php

namespace App\Http\Controllers;

use App\Exceptions\ModelException;
use App\Models\Domain;
use App\Models\Upload;
use App\Models\User;
use App\Repository\ApprovalRepository;
use App\Repository\TrackRecordRepository;
use App\Repository\UploadRepository;
use App\Repository\UserRepository;
use App\Services\Attachment;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Laravel\Facades\Image;
use Mirzacles\Models\TrackRecord;

class UploadController extends Controller
{
    public function __construct(
        private readonly ApprovalRepository $approvalRepository,
        private readonly Attachment $attachment,
        private readonly TrackRecordRepository $trackRecordRepository,
        private readonly UploadRepository $uploadRepository,
        private readonly UserRepository $userRepository,
    ) {}

    public function profileImage(Request $request)
    {
        $validator = Validator::make($request->all(), ['image' => 'required|image|mimes:jpeg,jpg,png,webp,gif|max:24576']);

        if ($validator->fails()) {

            if ($request->ajax()) {
                return response()->json(['result' => false, 'error' => $validator->errors()->first()]);
            }

            return Redirect::back()->withErrors($validator)->withInput();
        }

        /** @var Domain $domain */
        $domain = domain();

        /** @var User $user */
        $user = $request->user();
        $image = $request->file('image');
        $imageName = $this->attachment->process($image->path());

        if ($user->profile_image) {
            $this->attachment->deleteImageFile($user->profile_image);
        }

        $this->userRepository->updateProfileImage($user, $imageName);
        $this->approvalRepository->create($user);

        $this->trackRecordRepository->create($user, TrackRecord::PROFILE_IMG);

        $successMessage = __('Photo uploaded! Visible after review.');

        if ($request->ajax()) {
            return response()->json([
                'result' => true,
                'success' => $successMessage,
                'url' => thumb($imageName),
            ]);
        }

        return Redirect::back()->with('success', $successMessage);
    }

    public function upload(Request $request)
    {
        /** @var Domain $domain */
        $domain = domain();
        $validator = Validator::make($request->all(), ['image' => 'required|image|mimes:jpeg,jpg,png,webp,gif|max:24576']);

        if ($validator->fails()) {
            return Redirect::back()->withErrors($validator)->withInput();
        }

        /** @var User $user */
        $user = $request->user();
        $type = $request->has('privateimage') ? 'private' : 'upload';

        $image = $request->file('image');
        $imageName = $this->attachment->process($image->path(), $type);
        $file = $request->has('file') ? $request->get('file') : null;
        if (!$imageName) {
            return Redirect::back()->withErrors(__('Image upload failed!'));
        }
        $this->uploadRepository->createorUpdate($user, $imageName, $type, $file);
        $this->userRepository->touchModifiedAt($user);
        $this->approvalRepository->create($user);

        $successMessage = __('Photo uploaded! Visible after review.');

        if ($request->ajax()) {
            return response()->json([
                'result' => true,
                'success' => $successMessage,
                'url' => thumb($imageName),
            ]);
        }

        return Redirect::back()->with('success', __('Your photo has been uploaded successfully! It will be reviewed within 24 hours.'));
    }

    /**
     * @throws Exception
     */
    public function deleteImage(Request $request): RedirectResponse
    {
        $upload = $this->uploadRepository->get($request->input('file'));
        $this->attachment->deleteImageFile($upload->file);
        $this->uploadRepository->delete($upload);

        return Redirect::back()->with('success', __('Your photo has been deleted successfully!'));
    }

    public function deleteProfileImage(Request $request): RedirectResponse
    {
        /** @var User $user */
        $user = $request->user();

        if ($user->profile_image) {
            $this->attachment->deleteImageFile($user->profile_image);
        }
        $this->userRepository->removeProfileImage($user);

        return Redirect::back()->with('success', __('Your photo has been deleted successfully!'));
    }

    /**
     * Switch profile photo with other photo
     *
     *
     * @throws ModelException
     */
    public function setAsProfileImage(Request $request): RedirectResponse
    {
        $domain = domain();

        if ($request->get('file')) {
            if ($request->get('file') == 'profileImage') {
                $user = User::query()->findOrFail($request->get('user'));
                if ($user) {
                    // Set profile image as upload
                    Upload::query()->create([
                        'user_id' => $user->id,
                        'type' => 'upload',
                        'file' => $user->profile_image,
                        'active' => 1,
                    ]);
                    // set profile image blank
                    $user->profile_image = '';
                    $user->save();
                }

                if ($user) {
                    $this->uploadRepository->setUploadImage($user);
                }
            } else {
                // Set upload as profile image
                $upload = Upload::query()->findOrFail($request->get('file'));

                if ($upload) {
                    $this->uploadRepository->setProfileImage($upload);
                }
            }
        }

        return Redirect::back()->with('success', 'The profile photo has been set!');
    }

    public function serveJpgImage($filename)
    {
        try {
            $image = Image::read(public_path('uploads/' . $filename))->toJpeg(70);

            return response()->file($image);
        } catch (Exception $e) {
            Log::error('Image could not be served', [
                'filename' => $filename,
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);
        }
    }
}
