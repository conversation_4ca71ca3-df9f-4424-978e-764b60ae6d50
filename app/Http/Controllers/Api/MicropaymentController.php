<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\EventSourcing\OrderException;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HandlesPayments;
use App\Http\Requests\MicropaymentRequest;
use App\Models\Domain;
use App\Repository\AffiliateRepository;
use App\Repository\OrderRepository;
use App\Repository\TrackRecordRepository;
use App\Repository\UserRepository;
use Illuminate\Http\Response;
use Throwable;

class MicropaymentController extends Controller
{
    use HandlesPayments;

    private const PAYMENT = 'billing';

    private const CANCELLATION = 'storno';

    private const CREDIT_NOTE = 'backpay';

    private ?Domain $domain;

    /**
     * @return void
     */
    public function __construct(
        private readonly OrderRepository $orderRepository,
        private readonly UserRepository $userRepository,
        private readonly TrackRecordRepository $trackRecordRepository,
        private readonly AffiliateRepository $affiliateRepository
    ) {
        $this->domain = request()->attributes->get('domain');
    }

    /**
     * @throws Throwable
     * @throws OrderException
     */
    public function micropaymentNotification(MicropaymentRequest $request): Response
    {
        $userId = $request->input('userid');
        $orderId = $request->input('ordernumber');

        $order = $this->orderRepository->getOrFail($orderId, $userId);

        if (strtolower($request->input('function')) === self::PAYMENT && !$order->isPaid()) {
            $this->handleSuccessfulPayment($order, __METHOD__);
        }

        if (strtolower($request->input('function')) === self::CREDIT_NOTE) {
            $this->handleChargeback($order);
        }

        return response(sprintf(
            'status=ok%surl=%s%sforward=1%starget=self%s',
            PHP_EOL,
            route('login'),
            PHP_EOL,
            PHP_EOL,
            PHP_EOL
        ), 200)
            ->header('Content-Type', 'text/plain');
    }
}
