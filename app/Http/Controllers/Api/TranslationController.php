<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller as BaseController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TranslationController extends BaseController
{
    public function getTranslations(Request $request)
    {
        $token = $request->header('TranslationToken');
        $mode = $request->input('mode');

        if ($token !== 'a7eb07e5026ebbfd261a0d8e53fc97e8') {
            return response('Invalid token', 403)->header('Content-Type', 'text/plain');
        }

        $locales = array_values(array_filter(scandir(resource_path('lang')), function ($item) {
            return Str::contains($item, '.json');
        }));

        $data['locales'] = array_map(function ($item) {
            return Str::replace('.json', '', $item);
        }, $locales);

        foreach ($data['locales'] as $locale) {
            $json = file_get_contents(resource_path('lang/' . $locale . '.json'));
            $data[$locale] = json_decode($json, true);
        }

        return new JsonResponse($data);
    }
}
