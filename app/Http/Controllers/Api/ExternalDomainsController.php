<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Admin\ExternalDomains\CreateDomainRequest;
use App\Http\Resources\CompanyResource;
use App\Http\Resources\CountryResource;
use App\Http\Resources\DomainResource;
use App\Http\Resources\WonderpushResource;
use App\Repository\DomainRepository;
use Illuminate\Http\JsonResponse;

class ExternalDomainsController extends Controller
{
    /**
     * @return void
     */
    public function __construct(private readonly DomainRepository $domainRepository) {}

    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $domains = $this->domainRepository->getDomains();
        $domainsCollection = DomainResource::collection($domains);

        return $domainsCollection->response();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateDomainRequest $request): JsonResponse
    {
        $domain = $this->domainRepository->storeDomain($request->validated());

        return response()->json([
            'data' => new DomainResource($domain),
            'message' => __('The domain was successfully created'),
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $domain = $this->domainRepository->getDomainById($id);

        return response()->json([
            'data' => new DomainResource($domain),
        ]);
    }

    public function getDomainByUrl(string $url): JsonResponse
    {
        $domain = $this->domainRepository->getDomainByUrl($url);

        return response()->json([
            'data' => new DomainResource($domain),
        ]);
    }

    public function listWonderpush(): JsonResponse
    {
        $wonderpush = $this->domainRepository->getWonderpush();

        return response()->json([
            'data' => new WonderpushResource($wonderpush),
        ]);
    }

    public function listCompanies(): JsonResponse
    {
        $companies = $this->domainRepository->getCompanies();

        return response()->json([
            'data' => new CompanyResource($companies),
        ]);
    }

    public function listCountries(): JsonResponse
    {
        $countries = $this->domainRepository->getCountries();

        return response()->json([
            'data' => new CountryResource($countries),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CreateDomainRequest $request, string $id): JsonResponse
    {
        $this->domainRepository->updateDomain($request->validated(), $id);

        return response()->json([
            'message' => __('The domain was successfully updated'),
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        $this->domainRepository->deleteDomain($id);

        return response()->json([
            'message' => __('The domain was successfully deleted'),
        ]);
    }
}
