<?php

namespace App\Http\Controllers\Api\Operators;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Operators\StoreReportMemberRequest;
use App\Jobs\MailJob;
use App\Models\Conversations\ConversationFactory;
use App\Models\User;
use App\Repository\ApprovalRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Config;
use Mirzacles\Emails\Transformers\MailableTransformer;

class ReportMemberController extends Controller
{
    public function __construct(
        private readonly ApprovalRepository $approvalRepository,
    ) {}

    public function __invoke(StoreReportMemberRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $domain = $this->approvalRepository->getDomain();
        $this->approvalRepository->setDomain($domain);
        $conversation = ConversationFactory::generateConversationModel($domain->site_id)
            ->where('user_id', $validated['member_id'])
            ->first();

        /** @var User|null $member */
        $member = User::query()
            ->where('id', $validated['member_id'])
            ->where('status', 1)
            ->where('site_id', $domain->site_id)
            ->first();

        if ($member instanceof User) {
            $this->approvalRepository->create($member);

            $mailable = (new MailableTransformer)->transform([
                'domain' => $domain,
                'user' => $member,
                'to' => 'support@' . $domain->url,
                'from_email' => '<EMAIL>',
                'subject' => 'User Blocked Notification',
                'template' => 'global.blocked-user',
                'additional_payload' => [
                    'reason' => $request->input('note') ?? 'User has been blocked by the operator.',
                    'conversation_url' => config('app.operators_api_url') . '/supervisor/conversations/' . $conversation->id . '?message=' . $conversation->last_message_id,
                ],
            ])->serialize(Config::get('app.mail_key'));

            MailJob::dispatch($mailable)->onQueue('service_lane');

            return response()->json([
                'message' => 'Member has been reported, blocked, and support has been notified.',
            ]);
        }

        return response()->json([
            'message' => 'Member not found or already blocked.',
        ], 404);
    }
}
