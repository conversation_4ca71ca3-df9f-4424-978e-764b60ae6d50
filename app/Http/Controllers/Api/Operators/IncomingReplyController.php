<?php

namespace App\Http\Controllers\Api\Operators;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreIncomingReplyRequest;
use App\Models\Conversations\ConversationFactory;
use App\Models\Profile;
use App\Models\User;
use App\Repository\ChatterRepository;
use App\Repository\ConversationRepository;
use App\Repository\MessageRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use Mirzacles\Models\Chatter;
use Mirzacles\Models\Operator;

class IncomingReplyController extends Controller
{
    public function __construct(
        private readonly ConversationRepository $conversationRepository,
        private readonly MessageRepository $messageRepository,
        private readonly ChatterRepository $chatterRepository
    ) {}

    public function __invoke(StoreIncomingReplyRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $domain = domain();

        $this->conversationRepository->setDomain($domain);
        $this->messageRepository->setDomain($domain);
        $this->chatterRepository->setDomain($domain);

        $conversation = ConversationFactory::generateConversationModel($domain->site_id)
            ->find($validated['conversation_id']);

        $operator = Operator::query()->firstOrCreate(
            attributes: ['id' => $validated['operator_id']],
            values: [
                'email' => $validated['operator_email'],
                'company' => $validated['operator_name'],
                'password' => Str::random(),
                'pass' => Str::random(),
                'cpm' => 1,
                'status' => 1,
                'api' => 0,
                'api_site_ids' => ' ',
                'internal' => true,
            ]
        );

        $chatter = Chatter::query()->firstOrCreate(
            attributes: [
                'id' => $validated['chatter_id'],
            ],
            values: [
                'email' => $validated['chatter_email'],
                'username' => $validated['chatter_username'],
                'role' => 'chatter',
                'status' => true,
                'operator_id' => $operator->id,
            ]
        );

        $this->chatterRepository->touch($chatter);

        if ($conversation) {
            $site_id = $conversation->getSiteIdFromTable();
            $originalMessage = $this->messageRepository->findOrFail($site_id, $validated['original_message_id']);
            $originalMessage->update(['chatter_id' => $chatter->id]);
            $originalMessageContent = $originalMessage->message ?? null;
        } else {
            $user = User::findOrFail($validated['user_id']);
            $profile = Profile::findOrFail($validated['profile_id']);
            $site_id = $user->site_id;
        }

        $message = $this->messageRepository->addMessage(
            user: $conversation ? $conversation->user : $user,
            profile: $conversation ? $conversation->profile : $profile,
            message: $validated['message'],
            uploadId: $validated['upload_id'] ?? null,
            sent_by_user: false,
            isMember: false,
            chatter: $chatter
        );

        $chatter->messages()->create([
            'operator_id' => $chatter->operator_id,
            'messages_table' => $site_id,
            'message_id' => $message->id,
            'answer_id' => isset($originalMessage) ? $originalMessage->id : $message->id,
        ]);

        $message->original_message = $originalMessageContent ?? null;

        return response()->json([
            'message' => $message->toArray(),
        ]);
    }
}
