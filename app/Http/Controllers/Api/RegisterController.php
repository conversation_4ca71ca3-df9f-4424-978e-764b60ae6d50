<?php

namespace App\Http\Controllers\Api;

use App\Events\EmailVerifyRequested;
use App\Helpers\Functions;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\HandlesAffiliatePostback;
use App\Http\Controllers\Traits\VerifyEmailTrait;
use App\Jobs\MailJob;
use App\Jobs\RemoveApiTestSignupJob;
use App\Models\Affiliate;
use App\Models\EmailType;
use App\Models\User;
use App\Repository\AffiliateRepository;
use App\Repository\GeoRepository;
use App\Repository\ListDataRepository;
use App\Repository\ShortUrlRepository;
use App\Repository\TrackRecordRepository;
use App\Repository\UserRepository;
use App\Rules\EmailContainingNumberRule;
use App\Rules\EmailContainingSpecialCharacter;
use App\Rules\EmailHavingValidLength;
use App\Rules\EmailNumericEndingRule;
use App\Rules\EmailToxicityRule;
use App\Rules\ValidBirthdate;
use App\Services\DisablePreviousAccount;
use App\Services\ExecutePostbacks;
use App\ValueObjects\UserRequest;
use Carbon\Carbon;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Mirzacles\Emails\Transformers\MailableTransformer;
use Mirzacles\Models\TrackRecord;
use Mirzacles\PubSub\UserFilterPublisher;
use Mirzacles\PubSub\UserFilterTopicEnum;
use Random\RandomException;

class RegisterController extends Controller
{
    use HandlesAffiliatePostback;
    use VerifyEmailTrait;

    /**
     * @return void
     */
    public function __construct(
        private readonly AffiliateRepository $affiliateRepository,
        private readonly ExecutePostbacks $executePostbacks,
        private readonly GeoRepository $geoRepository,
        private readonly ListDataRepository $listDataRepository,
        private readonly UserRepository $userRepository,
        private readonly ShortUrlRepository $shortUrlRepository,
        private readonly TrackRecordRepository $trackRecordRepository
    ) {}

    /**
     * @throws BindingResolutionException
     * @throws RandomException
     */
    public function register(Request $request)
    {
        $request->getClientIp();

        $domain = domain();

        // @deprecated This cache key format will be replaced in the next version
        $cacheKey = $request->input('username') . '-' . $request->input('email') . '-' . $domain->site_id;

        if (Cache::has($cacheKey)) {
            return new JsonResponse(['error' => 'Request already sent'], 401);
        }

        $uniqueRequestId = (string) Str::uuid7();
        Cache::put($cacheKey, $uniqueRequestId);

        if (Cache::get($cacheKey) !== $uniqueRequestId) {
            return new JsonResponse(['error' => 'Request already sent'], 425);
        }

        $rules = [
            'username' => 'required|min:3|max:24|alpha_dash',
            'email' => [
                'required',
                'email',
                'max:100',
                new EmailToxicityRule,
            ],
            'gender' => 'required',
            'seek' => 'required',
            'password' => 'required',
            'ip' => 'required',
            'aff_id' => 'required',
            'req_id' => 'required',
            'location' => 'required',
            'birth_year' => 'required',
            'birth_month' => 'required',
            'birth_day' => 'required',
        ];

        $clickRequestId = $request->get('req_id');
        $affiliateId = $request->get('aff_id');
        $affiliate = $clickRequestId ? $this->affiliateRepository->findByAlias($clickRequestId) : null;

        $inputBirthday = [
            'birthdate' => $request->only(['birth_year', 'birth_month', 'birth_day']),
        ];

        // New centralized validation:
        $validator = Validator::make($inputBirthday, [
            'birthdate' => [new ValidBirthdate],
        ]);

        if ($validator->fails()) {
            Cache::forget($cacheKey);

            return new JsonResponse([
                'status' => 3,
                'result' => $validator->errors()->first('birthdate'),
            ], 422);
        }

        // Return error if affiliate is not found
        if ($affiliate === null) {
            Cache::forget($cacheKey);

            Log::info('Unknown affiliate found in API signup request.', ['req_id' => $clickRequestId, 'aff_id' => $affiliateId, 'sub_id' => $request->get('sub_id')]);

            return new JsonResponse([
                'status' => 1,
                'result' => 'Failed to process request. Please double check your request data or contact your account manager.',
                'value' => $request->get('req_id'),
            ], 401);
        }

        if (!in_array($clickRequestId, ['fabtrk', 'fabrest'])) {
            $rules['email'][] = new EmailNumericEndingRule;
        }

        $username = $request->get('username');
        $username = Str::replace('.', '', $username); // Strip dots from username
        $request->request->set('username', $username); // Replace value in $request with new $username

        $email = $request->get('email');

        if (Str::contains($clickRequestId, 'mobiptrk')) {
            $rules['email'][] = new EmailContainingNumberRule;
        }

        if (Str::contains($clickRequestId, 'lospollos') && $domain->url == 'usabangpalace.com') {
            $rules['email'][] = new EmailContainingNumberRule;
        }

        if (Str::contains($clickRequestId, 'securereg')) {
            $rules['email'][] = new EmailHavingValidLength;
            $rules['email'][] = new EmailContainingSpecialCharacter;

            // Block AOL
            if (Str::contains($email, 'aol.')) {
                return new JsonResponse([
                    'status' => 2,
                    'result' => 'Unfortunately the given e-mail address is not supported. Please try another e-mail address.',
                    'value' => $email,
                ], 422);
            }
        }

        $shouldIgnorePassword = strtolower($request->input('ignore_password', 'no'));
        if ($shouldIgnorePassword === 'yes') {
            unset($rules['password']);
            $request->request->set('password', '');
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            Cache::forget($cacheKey);

            return new JsonResponse(['status' => 1, 'errors' => $validator->errors()->toArray()], 422);
        }

        if ($affiliate !== null) {
            if ($affiliate->labels->isNotEmpty() && !$affiliate->labels->contains($domain->label_id)) {
                Cache::forget($cacheKey);

                return new JsonResponse(['error' => 'Access Denied'], 401);
            }

            $request->merge([
                'add_affiliate' => true,
                'aff_id' => $affiliateId,
                'req_id' => $clickRequestId,
                'affiliate_id' => $affiliate->id,
            ]);

            if ($clickRequestId === 'toplink' && $this->userRepository->doesEmailExist($email)) {
                Cache::forget($cacheKey);

                return new JsonResponse([
                    'status' => 2,
                    'result' => 'E-mail address already taken',
                    'value' => $email,
                ], 422);
            }
        }

        // Return the Not Allowed error if gender is not male or seek is not female
        $gender = strtolower($request->input('gender'));
        $seek = strtolower($request->input('seek'));
        if ($gender !== User::GENDER_MALE || $seek !== User::GENDER_FEMALE) {
            Cache::forget($cacheKey);

            return new JsonResponse([
                'status' => 6,
                'result' => 'Not Allowed',
                'value' => $email,
            ], 422);
        }

        // Check if user registered
        $alreadyRegistered = User::query()
            ->where([
                'email' => $request->input('email'),
                'site_id' => $domain->site_id,
                'status' => 1,
            ])->first();

        $transactionIdPrefix = null;

        if ($alreadyRegistered && !Str::startsWith($email, User::WHITELIST_MACH3)) {
            if (!DisablePreviousAccount::disable($alreadyRegistered, $clickRequestId, $affiliateId)) {

                if (in_array($clickRequestId, Affiliate::ALIASES_REACTIVATE_SIGNUP_AGAIN)) {
                    $this->userRepository->reEnableEmails($alreadyRegistered); // Touch user + enable emails
                    $this->trackRecordRepository->create($alreadyRegistered, TrackRecord::DID_REGISTER_AGAIN_API);

                    // Queue "welcome back" email
                    if ($domain->hasModule('welcome_back_mail')) {

                        $emailType = EmailType::getByType('welcome_back');
                        $welcomeBackEmailSent = $alreadyRegistered->emailsTracking()
                            ->where('created_at', '>=', Carbon::now()->subMonth())
                            ->where('email_type_id', $emailType->id)
                            ->count();

                        if ($welcomeBackEmailSent === 0) {

                            $token = $alreadyRegistered->getAutologinToken();
                            $domain = $alreadyRegistered->domain;
                            $emailTrackingId = (string) Str::uuid7();
                            $templateView = $domain->theme . '.auth.welcome_back';

                            $serializedData = (new MailableTransformer)
                                ->transform([
                                    'template' => $templateView,
                                    'subject' => "Welcome back on {$domain->name_url}, {$alreadyRegistered->username}!",
                                    'domain' => $domain,
                                    'user' => $alreadyRegistered,
                                    'email_type' => $emailType,
                                    'email_tracking_id' => $emailTrackingId,
                                    'autologin_token' => $token,
                                    'tags' => [
                                        'feedbackId' => 'welcome_back',
                                    ],
                                ])
                                ->serialize(Config::get('app.mail_key'));

                            MailJob::dispatch($serializedData)->onQueue('service_lane');
                        }
                    }
                }

                Cache::forget($cacheKey);

                return new JsonResponse([
                    'status' => 2,
                    'result' => 'E-mail address already taken',
                    'value' => $email,
                ], 422);
            }

            $transactionIdPrefix = (!empty($alreadyRegistered->aff_id))
                ? 'fab_dup_ori_prev_' . $alreadyRegistered->aff_id : 'fab_dup';
        }

        $iterator = 1;

        if ($this->userRepository->doesUsernameExist($username)) {
            do {
                $username .= random_int(1, 999);
                $iterator++;
            } while ($this->userRepository->doesUsernameExist($username) && $iterator <= 30);
        }

        if ($this->userRepository->doesUsernameExist($username)) {
            Cache::forget($cacheKey);

            return new JsonResponse(['error' => 'Username is already in use'], 422);
        }

        $request->request->set('username', $username);

        $emailParts = explode('@', $email);
        $fakeEmails = $this->listDataRepository->getBlacklistedEmails();

        if ($fakeEmails->search($emailParts[1]) !== false) {
            Cache::forget($cacheKey);

            return new JsonResponse([
                'status' => 2,
                'result' => 'Invalid e-mail',
                'value' => $email,
            ], 422);
        }

        if (strtolower($request->input('email_verified', 'no')) === 'no' && !$this->verifyEmail($domain, $email)) {
            Cache::forget($cacheKey);

            return new JsonResponse([
                'status' => 2,
                'result' => 'Invalid e-mail',
                'value' => $email,
            ], 422);
        }

        if ($request->has('location')) {
            $parsedLocation = $this->geoRepository->doesLocationMatch($request->get('location'));
            if ($parsedLocation === false || $parsedLocation === '') {
                Cache::forget($cacheKey);

                return new JsonResponse(['status' => 4, 'result' => 'Invalid location'], 422);
            }
            $request->merge(['region' => $parsedLocation]);
        }

        $userRequest = new UserRequest($request);

        if ($transactionIdPrefix) {
            $userRequest->setTransactionIdPrefix($transactionIdPrefix);
        }

        if (
            config('app.gcp_project_name') === 'chtp-g3-prod-us-6' &&
            $affiliate &&
            in_array($affiliate->alias, ['secureregus', 'securereguspremium']) &&
            $userRequest->getBirthDate()->age < 45
        ) {
            Cache::forget($cacheKey);

            return new JsonResponse([
                'status' => 5,
                'result' => 'Not Allowed',
                'value' => $email,
            ], 422);
        }

        // Exclude requests from Quebec (only relevant for CA)
        if (
            Str::contains(config('app.gcp_project_name'), 'ca') &&
            $affiliate &&
            $request->has('location') &&
            $affiliate->affiliate_type !== 'rev'
        ) {
            $parsedLocation = $this->geoRepository->doesLocationMatch($request->get('location'));
            $region = $this->geoRepository->getRegion($parsedLocation);

            if ($region && $region->code == 'QC') {
                Cache::forget($cacheKey);

                return new JsonResponse([
                    'status' => 5,
                    'result' => 'Not Allowed',
                    'value' => $email,
                ], 422);
            }
        }

        if (
            $affiliate &&
            $userRequest->getBirthDate()->age < 45 &&
            !in_array($affiliate->alias, Affiliate::ALIASES_MEDIABUY) &&
            !in_array($affiliate->alias, Affiliate::ALIASES_LOSPOLLOS) &&
            !Str::startsWith($userRequest->getClickRequestId(), ['omnitrk']) &&
            !Str::startsWith($userRequest->getClickRequestId(), ['crak']) && // Exclude CrakRevenue
            !Str::contains($userRequest->getClickRequestId(), ['admirtrck', 'toplink']) &&
            $affiliate->affiliate_type !== 'rev'
        ) {
            Cache::forget($cacheKey);

            return new JsonResponse([
                'status' => 5,
                'result' => 'Not Allowed',
                'value' => $email,
            ], 422);
        }

        if ($affiliate && !in_array($affiliate->alias, Affiliate::ALIASES_MEDIABUY) && $userRequest->getBirthDate()->age < 40) {
            $hasOtherDisabledAccount = User::query()
                ->where('email', $userRequest->getEmail())
                ->where('status', 0)
                ->exists();

            if ($hasOtherDisabledAccount) {
                Cache::forget($cacheKey);

                return new JsonResponse([
                    'status' => 2,
                    'result' => 'E-mail address already taken',
                    'value' => $email,
                ], 422);
            }
        }

        $this->userRepository->setMailProvider($userRequest, $affiliate, $domain);

        $user = $this->userRepository->create($userRequest);

        if ($affiliate && $affiliate->verify_requests) {
            $user->setMeta('verify_registration', true);
        }

        if ($shouldIgnorePassword === 'yes') {
            $this->userRepository->activateAndConfirmUser($user);
        } else {
            event(new EmailVerifyRequested($user));
        }

        $this->handleAffiliatePostback($userRequest, $user);

        if ($user->domain->hasModule('user_filter')) {
            $userFilterPublisher = app(UserFilterPublisher::class);
            $source = Functions::getGcpProjectName($user->domain->country_code) . '-s' . $user->site_id;
            $userFilterPublisher->publish(UserFilterTopicEnum::REGISTRATION, [
                'source' => $source,
                'email' => $user->email,
                'birth_date' => Carbon::parse($user->info->birthdate)->toISOString(),
                'click_request_id' => $userRequest->getClickRequestId(),
            ]);

            $userFilterPublisher->publishSimple('ecf__prod_chtp_wp_soi_v1', [
                'source' => $source,
                'email' => $user->email,
                'birth_date' => empty($user->info->birthdate) ? null : [
                    'string' => Carbon::parse($user->info->birthdate)->toDateString(),
                ],
            ]);
        }

        $url = $domain->full_url;

        if ($user->click_request_id !== null && $user->click_request_id == 'apitest') {
            \dispatch_sync(new RemoveApiTestSignupJob($user));
        } else {
            DB::table('api_signup')->insert([
                'id' => Str::uuid7(),
                'user_id' => $user->id,
                'site_id' => $user->site_id,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);

            $url = $domain->full_url . '?token=' . $user->getAutologinToken();
        }

        Cache::forget($cacheKey);

        return new Response($url);
    }
}
