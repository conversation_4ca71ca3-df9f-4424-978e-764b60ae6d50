<?php

namespace App\Http\Controllers;

use App\Repository\GeoRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Response;
use Mirzacles\Models\City;
use Mirzacles\Models\Region;

class LocationController extends Controller
{
    public function __construct(private readonly GeoRepository $geoRepository) {}

    /**
     * @return JsonResponse|RedirectResponse
     */
    public function cityAutocomplete(Request $request)
    {
        if (!$request->ajax()) {
            return Redirect::route('home');
        }

        $domain = domain();
        $country = $domain->country;

        $result = $this->geoRepository->search($country->id, addslashes($request->get('term')), null);

        if (!$result || $result->isEmpty()) {
            return Response::json([]);
        }

        $result = $result->map(fn (City $city) => [
            'city' => $city->name,
            'city_id' => $city->id,
            'region' => $city->region->name,
            'region_id' => $city->region->id,
            'country' => $country->name,
            'priority' => $city->priority,
        ]);

        return Response::json($result->toArray());
    }

    public function regionAutocomplete(Request $request)
    {
        if (!$request->ajax()) {
            return Redirect::route('home');
        }

        $domain = domain();
        $country = $domain->country;

        $result = $this->geoRepository->searchRegions($country->id, addslashes($request->get('term')));

        if (!$result || $result->isEmpty()) {
            return Response::json([]);
        }

        $result = $result->map(fn ($region) => [
            'region' => $region->name,
            'region_id' => $region->id,
            'priority' => $region->priority,
        ]);

        return Response::json($result->toArray());
    }

    public function postalCodeLookup(Request $request)
    {
        if (!$request->ajax()) {
            return Redirect::route('home');
        }

        $domain = domain();

        $postalCode = $request->get('postalcode');
        if (empty($postalCode)) {
            return Response::json([]);
        }

        $postalResponse = Http::get("https://api.zippopotam.us/{$domain->country_code}/{$postalCode}");

        $postalData = $postalResponse->ok() ? $postalResponse->json() : null;
        if ($postalData && isset($postalData['places'][0]['state'])) {
            $region = Region::where('name', $postalData['places'][0]['state'])->first();
            if ($region && isset($postalData['places'][0]['place name'])) {
                $city = $region->cities()->whereLike('search', "%{$postalData['places'][0]['place name']}%")->first();
            }
        }

        return Response::json([
            'region' => $region?->name ?? null,
            'region_id' => $region?->id ?? null,
            'city' => $city?->name ?? null,
            'city_id' => $city?->id ?? null,
            'postal_code' => $postalCode,
            'original' => $postalData,
        ]);
    }
}
