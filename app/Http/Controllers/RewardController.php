<?php

namespace App\Http\Controllers;

use App\Mappers\RewardExternMapper;
use App\Mappers\RewardsStatsMapper;
use App\Repository\RewardExternRepository;
use App\Repository\RewardInternRepository;
use App\Repository\TransactionRepository;
use App\Repository\UserRepository;
use App\Services\ReplaceTagsV2;
use App\ValueObjects\Rewards\RewardsTotalCreditsEarned;
use App\ValueObjects\Rewards\RewardTotalStats;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\View;
use Mirzacles\Models\Transactions\Transaction;

class RewardController extends Controller
{
    /**
     * @return void
     */
    public function __construct(
        private readonly RewardExternRepository $rewardExternRepository,
        private readonly RewardInternRepository $rewardInternRepository,
        private readonly UserRepository $userRepository,
        private readonly TransactionRepository $transactionRepository
    ) {}

    public function index(Request $request): \Illuminate\View\View
    {
        $domain = domain();

        $user = Auth::user();

        $internRewards = $this->rewardInternRepository->getVisibleRewards($user->id);
        $interStats = RewardsStatsMapper::map($internRewards);

        $externRewards = $this->rewardExternRepository->getVisibleRewards($user->id);
        $mappedExternRewards = RewardExternMapper::map($externRewards);
        $externStats = RewardsStatsMapper::map($mappedExternRewards);

        return View::make('theme.' . $domain->theme . '.rewards.index', [
            'intern_rewards_stats' => $interStats,
            'intern_rewards' => $internRewards,
            'extern_rewards_stats' => $externStats,
            'extern_rewards' => $mappedExternRewards,
            'total_stats' => new RewardTotalStats($interStats, $externStats),
            'credit_stats' => new RewardsTotalCreditsEarned($internRewards, $mappedExternRewards),
        ]);
    }

    public function collectInternTokens(Request $request, $id): RedirectResponse
    {
        $domain = domain();

        $this->userRepository->setDomain($domain);

        $internReward = $this->rewardInternRepository->find($id);
        $user = $this->userRepository->find(Auth::user()->id);

        $this->rewardInternRepository->markAsCollected($internReward, $user);
        $this->userRepository->updateCredits($user, $user->credits + $internReward->credits);
        $this->transactionRepository->create($user, Transaction::REWARDS_EXTERN, $internReward->credits);
        $this->userRepository->addShadowCredits($user, $internReward->shadow_credits);
        $this->transactionRepository->create($user, Transaction::SHADOW_CREDIT, $internReward->shadow_credits);

        return Redirect::route('rewards')->with('success', 'You\'ve successfully collected your free tokens!');
    }

    public function collectExternTokens(Request $request, $id): RedirectResponse
    {
        $domain = domain();

        $this->userRepository->setDomain($domain);

        $rewardExtern = $this->rewardExternRepository->find($id);
        $user = $this->userRepository->find(Auth::user()->id);

        $this->rewardExternRepository->markAsCollected($rewardExtern, $user);
        $this->userRepository->updateCredits($user, $user->credits + $rewardExtern->credits);
        $this->transactionRepository->create($user, Transaction::REWARDS_EXTERN, $rewardExtern->credits);
        $this->userRepository->addShadowCredits($user, $rewardExtern->shadow_credits);
        $this->transactionRepository->create($user, Transaction::SHADOW_CREDIT, $rewardExtern->shadow_credits);

        return Redirect::route('rewards')->with('success', 'You\'ve successfully collected your free tokens!');
    }

    public function gotoOffer(Request $request): RedirectResponse
    {
        $uuid = $request->get('uuid');
        $reward = $this->rewardExternRepository->findByUuid($uuid);

        $this->rewardExternRepository->addClick($reward);

        $targetUrl = ReplaceTagsV2::replace($reward->target_url, ['uuid' => $uuid, 'geo' => 'uk']);

        return Redirect::away(
            Config::get('rewards.proxy_url') . '/api/proxy/redirection?target=' . urlencode(htmlentities($targetUrl))
        );
    }

    public function postback(Request $request): JsonResponse
    {
        $validate = Validator::make($request->all(), [
            'uuid' => 'required',
        ]);

        $uuid = $request->get('uuid');

        if ($validate->fails()) {
            Log::error('Failed postback of rewards', ['uuid' => $uuid]);

            return new JsonResponse($validate->errors(), 422);
        }

        try {
            $reward = $this->rewardExternRepository->findByUuid($uuid);
        } catch (ModelNotFoundException $exception) {
            Log::error('Could not find reward with', ['uuid' => $uuid]);

            return new JsonResponse(null, 404);
        }

        $grantReward = $this->rewardExternRepository->grantReward(
            $reward->visibilities->first(),
            $reward,
            $request->get('payout')
        );

        if (!$grantReward) {
            Log::error('Reward already granted', ['uuid' => $uuid]);

            return new JsonResponse(null, 400);
        }

        if ($reward->max_cap !== null && $this->rewardExternRepository->addCap($reward) === $reward->max_cap) {
            $this->rewardExternRepository->markAsInvisible($reward);
        }

        return new JsonResponse(null, 204);
    }
}
