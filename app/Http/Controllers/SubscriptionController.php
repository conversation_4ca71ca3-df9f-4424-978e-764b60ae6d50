<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Traits\HandlesPayments;
use App\Http\Requests\BuyProductRequest;
use App\Models\Domain;
use App\Models\User;
use App\Repository\AffiliateRepository;
use App\Repository\OrderRepository;
use App\Repository\PremiumRepository;
use App\Repository\SubscriptionRepository;
use App\Repository\TrackRecordRepository;
use App\Repository\UserRepository;
use App\Services\ClientIp;
use App\Services\Payment\PaymentFactory;
use App\Support\CurrencyCode;
use App\ValueObjects\UserPaymentMethods;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\View;
use Mirzacles\Models\Country;
use Mirzacles\Models\TrackRecord;
use Throwable;

class SubscriptionController extends Controller
{
    use HandlesPayments;

    private ?Domain $domain;

    /**
     * @return void
     */
    public function __construct(
        private readonly PremiumRepository $premiumRepository,
        private readonly SubscriptionRepository $subscriptionRepository,
        private readonly OrderRepository $orderRepository,
        private readonly TrackRecordRepository $trackRecordRepository,
        private readonly UserRepository $userRepository,
        private readonly AffiliateRepository $affiliateRepository
    ) {
        $this->domain = domain();
    }

    /**
     * Show the premium subscriptions page.
     *
     *
     * @return \Illuminate\Contracts\View\View|RedirectResponse
     *
     * @throws BindingResolutionException
     */
    public function products(Request $request)
    {
        /** @var User $user */
        $user = $request->user();
        $domain = domain();

        if (!$domain->use_subscriptions) {
            return Redirect::route('premium');
        }

        if ($user->isPremium()) {
            return Redirect::route('subscriptionOverview');
        }

        $this->trackRecordRepository->create($user, TrackRecord::VISIT_PREMIUM);

        $paymentMethods = app()->make(UserPaymentMethods::class)->get();

        $products = $this->subscriptionRepository->getAvailableProducts();
        $trialSubscription = $this->subscriptionRepository->getTrial();

        $bestSeller = $products->where('initial_period', 90)->first();
        $page_title = __('VIP membership');

        if ($products->count() > 0) {
            $max_ppm = $products->max('price_per_month');
            $min_ppm = $products->min('price_per_month');
            $max_discount = ceil(($max_ppm - $min_ppm) / $max_ppm * 100);
        }

        return View::make('theme.' . $this->domain->theme . '.payment.subscribe', [
            'page_title' => $page_title,
            'products' => $products,
            'productType' => 'subscription',
            'defaultSelected' => $bestSeller?->id,
            'bestseller' => $bestSeller,
            'formRoute' => 'postTransactionPremium',
            'discount' => $user->isEligibleForLoyaltyDiscount() && $this->domain->loyalty_discount > 0,
            'maxDiscount' => $max_discount ?? 0,
            'paymentMethods' => $paymentMethods,
            'ipAddress' => ClientIp::getIp($request),
            'currencyCode' => CurrencyCode::getCurrencyCode($this->domain),
            'trialSubscription' => $trialSubscription,
            'countries' => Country::all(),
        ]);
    }

    /**
     * @return \Illuminate\Contracts\View\View|RedirectResponse
     */
    public function overview()
    {
        if (!$this->domain->hasModule('premium')) {
            return Redirect::route('home');
        }

        /** @var User $user */
        $user = auth()->user();

        if (!$user->isPremium()) {
            return Redirect::route('subscribe');
        }

        return View::make('theme.' . $this->domain->theme . '.payment.subscription', [
            'favorites' => $user->favoriteProfileIds(),
        ]);
    }

    /**
     * @return mixed
     *
     * @throws Throwable
     */
    public function transaction(BuyProductRequest $request)
    {
        /** @var User $user */
        $user = auth()->user();

        $userIp = ClientIp::getIp($request);

        $order = $this->orderRepository->findOrCreate(
            $request->validated(),
            domain(),
            $user,
            'subscription',
            $userIp
        );

        if ($request->has('payment_method_id') && $order->payment_method_id !== $request->input('payment_method_id')) {
            $order->setPaymentMethodId($request->input('payment_method_id'));
        }

        $this->trackRecordRepository->create($user, TrackRecord::VISIT_CHECKOUT);

        return PaymentFactory::create($order->payment_method_id, $request)
            ->generate($order);
    }
}
