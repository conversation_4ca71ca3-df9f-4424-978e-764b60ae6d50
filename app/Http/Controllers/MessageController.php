<?php

namespace App\Http\Controllers;

use App\Exceptions\ModelException;
use App\Helpers\Functions;
use App\Models\ChatGroupPost;
use App\Models\Conversations\ConversationModel;
use App\Models\Upload;
use App\Models\User;
use App\Repository\ConversationRepository;
use App\Repository\FlirtRepository;
use App\Repository\LogRepository;
use App\Repository\MessageRepository;
use App\Repository\ProfileRepository;
use App\Repository\TrackRecordRepository;
use App\Repository\TransactionRepository;
use App\Repository\UploadRepository;
use App\Repository\UserRepository;
use App\Services\Attachment;
use App\Services\MessageExchange;
use App\Support\FlashMessage;
use Carbon\Carbon;
use Exception;
use Faker\Factory;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Mirzacles\Helpers\Services\CacheKeyService;
use Mirzacles\Models\AutoReplyQueue;
use Mirzacles\Models\TrackRecord;
use Mirzacles\Models\Transactions\Transaction;
use Psr\SimpleCache\InvalidArgumentException;

class MessageController extends Controller
{
    /**
     * @return void
     */
    public function __construct(
        private readonly Attachment $attachmentService,
        private readonly ConversationRepository $conversationRepository,
        private readonly FlirtRepository $flirtRepository,
        private readonly LogRepository $userlogRepository,
        private readonly MessageExchange $messageExchange,
        private readonly MessageRepository $messageRepository,
        private readonly ProfileRepository $profileRepository,
        private readonly TrackRecordRepository $trackRecordRepository,
        private readonly TransactionRepository $transactionRepository,
        private readonly UploadRepository $uploadRepository,
        private readonly UserRepository $userRepository,
    ) {}

    public function all(Request $request, string $type = 'inbox', int $subType = 0): RedirectResponse|\Illuminate\Contracts\View\View|JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        if ($type == 'archived' && !$user->profile_image) {
            return redirect()->route('messages', 'all')->withErrors([__('You don\'t have access to the archived messages.')]);
        }

        $domain = domain();

        $page = $request->input('page', 1);
        $conversations = $this->conversationRepository->getConversations($user, $type);

        $conversationsCount = $this->messageRepository->countUnreadMutualConversations($user);

        if ($request->ajax()) {
            $view_name = 'theme.' . $domain->theme . '.message.partials.messages-preview';
            if (View::exists($view_name)) {
                $view = View::make($view_name, [
                    'messages' => $conversations,
                    'type' => $type,
                    'return' => $page,
                ])->render();

                return response()->json([
                    'view' => $view,
                ]);
            }

            return response()->json([
                'view' => '',
            ]);
        }

        return View::make('theme.' . $domain->theme . '.message.index', [
            'page_title' => __('Messages'),
            'messages' => $conversations,
            'page' => $page,
            'return' => $page,
            'type' => $type,
            'subType' => $subType,
            'domain' => $domain,
            'favorites' => $user->favoriteProfileIds(),
            'conversationsCount' => $conversationsCount,
        ]);
    }

    /**
     * @throws ModelException
     */
    public function deleteConversations(Request $request): RedirectResponse
    {
        /** @var User $user */
        $user = $request->user();

        $profileIds = $request->get('archive');
        if (!is_array($profileIds)) {
            $profileIds = explode(',', $profileIds);
        }

        if (empty($profileIds)) {
            return Redirect::back()->with('error', __('Please select an item to delete'));
        }

        $this->messageRepository->deleteConversations($user, $profileIds);
        $this->messageRepository->clearConversationsCache($user); // TODO: remove once the profile caching from the commands is fully implemented

        $this->trackRecordRepository->create($user, 'delete-conversation-' . implode('-', $profileIds));

        if ($request->get('chat_page')) {
            return Redirect::route('messages', ['type' => 'all'])->with('success', __('Message deleted successfully.'));
        }

        return Redirect::back()->with('success', __('The items have been deleted.'));
    }

    public function deleteMessage(string $id): RedirectResponse
    {
        $conversationDeleted = $this->messageRepository->deleteMessage($id);
        $this->trackRecordRepository->create(Auth::user(), 'delete-message-' . $id);

        if ($conversationDeleted) {
            return redirect()->route('messages', 'inbox');
        }

        return redirect()->back()->with('success', __('Message deleted!'));
    }

    /**
     * @throws Exception
     */
    public function add(string $username, Request $request): JsonResponse|RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|min:1|max:850',
            'image' => 'image|mimes:jpeg,jpg,png,webp,gif|max:8192',
        ]);

        if ($validator->fails()) {
            if ($validator->errors()->has('image')) {
                $errorMessage = __('There was an error while uploading your image.');
            } else {
                $errorMessage = __('There was an error while sending your message.');
            }

            if ($request->ajax()) {
                return response()->json(['status' => false, 'message' => $errorMessage, 'enableBtn' => true]);
            }

            return FlashMessage::error($errorMessage);
        }

        $domain = domain();
        /** @var User $user */
        $user = $request->user();
        $profile = $this->profileRepository->find($username);
        $lastMessage = $this->messageRepository->lastMessage($user, $profile);

        $message = $request->get('message');

        if ($this->messageRepository->doesMessageExist($user, $profile, 1, $message)) {
            $successMessage = __('Your message has already been sent!');
            if ($request->ajax()) {
                return response()->json(['status' => true, 'message' => $successMessage, 'enableBtn' => true]);
            }

            return FlashMessage::success($successMessage);
        }

        if ($lastMessage === null) {
            $this->userlogRepository->create($user, $profile);
        }

        if ($this->messageRepository->hasNeverSentAMessage($user)) {
            $this->trackRecordRepository->create($user, TrackRecord::SPENT_CREDIT);
        }

        $upload = null;

        if ($request->hasFile('image')) {
            try {
                $image = $request->file('image');
                $imageName = $this->attachmentService->process($image->path());
                $upload = $this->uploadRepository->createOrUpdate($user, $imageName, 'message');
            } catch (Exception $exception) {
                Log::error($exception, ['user_id' => $user->id, 'profile_id' => $profile->id]);

                $errorMessage = __('The image could not be processed. Please choose a different image.');

                if ($request->ajax()) {
                    return response()->json([
                        'status' => false,
                        'message' => $errorMessage,
                        'enableBtn' => true,
                    ]);
                }

                return Redirect::back()->withErrors($errorMessage);
            }
        }

        if ($request->has('photo') && !$upload) {
            $upload = Upload::where('member_id', $profile->id)->where('file', $request->input('photo'))->first();
            if (!$upload && $request->input('photo') == $profile->profile_image) {
                $upload = Upload::create([
                    'member_id' => $profile->id,
                    'type' => 'message',
                    'file' => $request->input('photo'),
                    'active' => 0,
                    'site_id' => $domain->site_id,
                ]);
            }
        }

        $type = $request->input('type', 'message');

        if ($type == 'post' && $request->has('post_id')) {
            $post = ChatGroupPost::find($request->input('post_id'));
            if ($post) {
                $postMessage = $this->messageRepository->addMessage(
                    $user,
                    $profile,
                    Functions::removeEmoji($post->message),
                    ($post->upload_id === null) ? null : $post->upload_id,
                    $type,
                    false,
                    false
                );

                $postMessage->update(['created_at' => $post->created_at]);
            }
        }

        if ($lastMessage !== null && $lastMessage->type === 'auto_reply') {
            AutoReplyQueue::where('user_id', $user->id)
                ->where('profile_id', $profile->id)
                ->where('is_sent', false)
                ->where('is_sending', false)
                ->delete();
        }

        $message = $this->messageRepository->addMessage(
            $user,
            $profile,
            Functions::removeEmoji($message),
            ($upload === null) ? null : $upload->id,
            $type
        );

        $device = 'desktop';

        if (Functions::isTablet()) {
            $device = 'tablet';
        } elseif (Functions::isMobile()) {
            $device = 'mobile';
        }

        $this->messageRepository->addMessageInfo($user, $message, $device);

        $this->userRepository->pay($user, 1);
        $this->transactionRepository->create($user, Transaction::SEND_MESSAGE, -1);

        $this->messageExchange->queue($domain, $message->fresh(['template', 'user', 'profile']));

        Cache::forget(CacheKeyService::profilesWithNewMessageIds($user));

        if ($request->ajax()) {

            if ($request->has('chatbox')) {
                $messages = $this->messageRepository->all($profile, $user, null, true);
                $messages_html = View::make('theme.' . $domain->theme . '.message.partials.messages', ['messages' => $messages])->render();

                return response()->json([
                    'status' => true,
                    'enableBtn' => true,
                    'messages' => $messages_html,
                    'credits' => $user->credits,
                    'last_massage' => Carbon::now()->format('Y-m-d H:s:i'),
                ]);
            }

            return response()->json(['status' => true, 'enableBtn' => true]);
        }

        if ($request->has('go-to-chat')) {
            return Redirect::route('conversation', ['type' => 'conversation', 'username' => $profile->username]);
        }

        return Redirect::back()->with('success', __('Your message has been sent!'));
    }

    public function addTestMessageForConversationFromProfile(string $conversationId): RedirectResponse
    {
        $conversation = $this->conversationRepository->findOrFail($conversationId);
        $user = $conversation->user;

        $this->messageRepository->addMessage($user, $conversation->profile, Factory::create()->text(50), null, 'message', false);

        return redirect()->back()->with('success', __('Added test message'));
    }

    /**
     * @throws ModelException
     * @throws Exception
     */
    public function conversation(Request $request, string $type, string $username, string $messageType = 'message'): \Illuminate\Contracts\View\View|JsonResponse|RedirectResponse
    {
        /** @var User $user */
        $user = $request->user();
        $domain = domain();
        $profile = $this->profileRepository->find($username);
        $this->flirtRepository->setDomain($domain);

        if ($user->seek !== $profile->gender) {
            return FlashMessage::error(__('You can only chat with members that match your preferences'));
        }

        $conversation = $this->conversationRepository->findConversationFromProfile($profile);

        if ($conversation instanceof ConversationModel && !$conversation->read) {
            $this->messageRepository->markConversationAsRead($conversation);
            View::share('messageCount', $this->messageRepository->countUnreadConversations($user));
        }

        if ($conversation instanceof ConversationModel && !$conversation->last) {
            $conversation = $this->messageRepository->recoverConversation($conversation);
        }

        $online = in_array($profile->id, $this->profileRepository->getCachedOnlineProfileIds($user->site_id), true);
        $summary = null;
        $firstMessage = null;

        /** @var LengthAwarePaginator $messages */
        $messages = $this->messageRepository->all(
            $profile,
            $user
        );

        $back = Str::contains(url()->previous(), ['/messages']) ? 'javascript:history.back();' : route('messages', ['type' => 'all']);

        $showMore = $messages->total() > $domain->items_per_page;
        $messages = $messages->reverse();

        $showEncrypt = $messages->where('sent_by_user', true)->count() == 0;

        return View::make('theme.' . $domain->theme . '.message.conversation', [
            'back' => $back,
            'chatWindow' => true,
            'conversation' => $conversation,
            'domain' => $domain,
            'firstMessage' => $firstMessage,
            'flirts' => $this->flirtRepository->all(),
            'inbox' => $summary,
            'menuMessages' => true,
            'messages' => $messages,
            'msg_type' => $messageType,
            'online' => $online,
            'profile' => $profile,
            'return' => $request->input('return', 1),
            'showEncrypt' => $showEncrypt,
            'showMore' => $showMore,
            'type' => $type,
        ]);
    }

    /**
     * @throws InvalidArgumentException
     */
    public function archiveOrReadMessages(Request $request): RedirectResponse
    {
        $domain = domain();

        $messageIds = $request->get('archive');
        $action = $request->get('submit_button');

        if (empty($messageIds)) {
            return Redirect::back()
                ->with(
                    'error',
                    __('Please select a message', ['action' => $action])
                );
        }

        $user = Auth::user();

        if ($action === 'archive') {
            $this->messageRepository->markAsInvisibleAndRead($user, $messageIds);
        }

        $appUrl = $domain->url;

        $this->messageRepository->clearConversationsCache($user);

        if ($action === 'archive') {
            return Redirect::back()
                ->with('success', Lang::choice('main.messages.message_archived_count', count($messageIds)));
        }

        return Redirect::back()
            ->with('success', Lang::choice('main.messages.message_marked_as_read_count', count($messageIds)));
    }

    public function checkMessages(Request $request): JsonResponse
    {
        $user = Auth::user();

        $profileId = $request->input('profileId');
        $lastUpdate = $request->input('lastUpdate');

        try {
            $domain = domain();
            $profile = $this->profileRepository->find($profileId);
        } catch (ModelNotFoundException) {
            Log::error('Profile not found: ' . $profileId);

            return response()->json([
                'status' => false,
                'message' => 'Profile not found.',
            ]);
        }

        if (!$lastUpdate) {
            return response()->json([
                'status' => false,
            ]);
        }

        $newMessages = $this->messageRepository->getNewMessages($user, $profile, $lastUpdate);

        if ($newMessages->isNotEmpty()) {
            foreach ($newMessages as $idx => $message) {
                $messages[$idx]['wait'] = mb_strlen($message->message) * 350;
                $messages[$idx]['content'] = View::make('theme.' . $domain->theme . '.message.partials.messages', [
                    'messages' => [$message],
                    'humantime' => __('Just now'),
                ])->render();
            }

            $conversation = $this->conversationRepository->findConversationFromProfile($profile);
            $this->messageRepository->markConversationAsRead($conversation);
        }

        if ($request->ajax()) {
            return response()->json([
                'status' => true,
                'messages' => $messages ?? [],
                'lastUpdate' => time(),
                'last_message' => date('Y-m-d H:s:i'),
            ]);
        }

        return response()->json([], 403);
    }

    public function showMoreMessages(Request $request): JsonResponse
    {
        $user = Auth::user();
        $offset = $request->input('offset');
        $profileId = $request->input('profileId');

        try {
            $domain = domain();
            $profile = $this->profileRepository->find($profileId);
        } catch (ModelNotFoundException) {
            Log::error('Profile not found: ' . $profileId);

            return response()->json([
                'status' => false,
                'message' => 'Profile not found.',
            ]);
        }

        $messages = $this->messageRepository->all(
            $profile,
            $user,
            1,
            false,
            0,
            $offset
        );

        $showMore = $messages['total'] > $domain->items_per_page + $offset;
        $messages = $messages['messages']->reverse();
        $messageHtml = View::make('theme.' . $domain->theme . '.message.partials.messages', [
            'messages' => $messages,
            'previousMessages' => true,
            'profile' => $profile,
        ])->render();

        if ($request->ajax()) {
            return response()->json([
                'status' => true,
                'showMore' => $showMore,
                'messages' => $messageHtml,
            ]);
        }

        return response()->json([], 403);
    }
}
