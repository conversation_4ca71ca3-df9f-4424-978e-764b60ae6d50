<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TruevoRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'transactionId' => 'required',
            'rrn' => 'required',
            'transactionStatus' => 'required',
            'responseCode' => 'required',
            'merchantReference' => 'required|exists:orders,order_id',
        ];
    }
}
