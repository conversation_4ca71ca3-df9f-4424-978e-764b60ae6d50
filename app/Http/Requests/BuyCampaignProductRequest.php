<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BuyCampaignProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'ip' => 'required|string',
            'report' => 'required|array',
            'product' => 'required|array',
            'product.amount' => 'required|integer',
            'product.credits' => 'required|integer',
            'product.special_amount' => 'required|integer',
            'product.location' => 'required|string',
        ];
    }
}
