<?php

namespace App\Http\Requests\Api\Admin\ExternalDomains;

use App\Http\Requests\Traits\Admin\FieldsTransformer;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class CreateDomainRequest extends FormRequest
{
    use FieldsTransformer;

    private array $defaultValues = [
        'pokes_status' => 'live',
        'invitation_alias' => 'sex proposal',
        'items_per_page' => 24,
        'uploads_edit_limit' => 4,
        'uploads_edit_types' => 'upload',
        'max_search_distance' => 30,
        'type' => 'whitelabel',
        'https' => true,
    ];

    private array $placeholders = [
        'url' => 'domainname.com',
        'type' => 'whitelabel',
        'whitelabel' => 'whitelabel.domainname.com',
        'name_url' => 'DomainName.com',
        'mail_url' => 'mail.domainname.com',
        'mail_url2' => 'mail2.domainname.com',
        'doi_mail' => '<EMAIL>',
        'name' => 'DomainName',
        'alias' => 'US1',
        'country_code' => 'US',
        'country_code_iso' => 'USA',
        'cdn_url' => 'cdn.domainname.com',
        'cdn_url_landing' => 'cdn2.domainname.com',
        'invitation_alias' => 'sex proposal',
        'messages_order' => 'asc',
        'currency' => 'USD',
        'currency_symbol' => '$',
        'lang' => 'en',
        'locale' => 'en_US',
        'activation_wall' => 0,
        'mailerq_mail_url' => 'domain.com',
        'has_mailerq' => 0,
    ];

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'url' => 'required|string',
            'theme' => 'required|string',
            'stylesheet' => 'nullable|string',
            'type' => 'required|string',
            'whitelabel' => 'required|string',
            'name_url' => 'required|string',
            'mail_url' => 'required|string',
            'mail_url2' => 'required|string',
            'doi_mail' => 'required|email',
            'name' => 'required|string|max:30',
            'https' => 'nullable|boolean',
            'alias' => 'nullable|string|min:0|max:6',
            'site_id' => 'required|numeric',
            'label_id' => 'required|numeric',
            'items_per_page' => 'required|numeric',
            'country_id' => 'required|string|max:36|exists:App\Models\Country,id',
            'country_code' => 'required|string|max:5',
            'country_code_iso' => 'nullable|string|max:5',
            'cdn_url' => 'nullable|string',
            'cdn_url_landing' => 'nullable|string',
            'activation_wall' => 'required|boolean',
            'invitation_alias' => 'required|string|max:50',
            'messages_order' => 'required|string|max:4',
            'messages_pagination' => 'required|boolean',
            'pokes_status' => 'required|string|max:20',
            'uploads_edit_limit' => 'nullable|numeric',
            'uploads_edit_types' => 'nullable|string|max:100',
            'with_about_me' => 'required|boolean',
            'profile_with_messages' => 'required|boolean',
            'use_dynamic_regions' => 'required|boolean',
            'currency' => 'required|string|max:5',
            'currency_symbol' => 'nullable|string|max:10',
            'lang' => 'required|string|max:5',
            'locale' => 'nullable|string|max:5',
            'chat_api_zl' => 'nullable|boolean',
            'dynamic_regions' => 'nullable|boolean',
            'recaptcha_secret' => 'nullable|string',
            'recaptcha_site_key' => 'nullable|string',
            'google_ua_code' => 'nullable|string|max:15',
            'use_google_login' => 'nullable|boolean',
            'google_login_client_id' => 'nullable|string|max:100',
            'google_login_client_secret' => 'nullable|string|max:50',
            'google_login_api_key' => 'nullable|string|max:50',
            'vat' => 'required|numeric',
            'profile_use_random' => 'required|boolean',
            'conversation_with_inbox' => 'required|boolean',
            'max_search_distance' => 'required|numeric',
            'use_distance' => 'required|boolean',
            'company_id' => 'nullable|string|max:36|exists:App\Models\Company,id',
            'loyalty_discount' => 'required|numeric',
            'verification_token' => 'nullable|string|max:36',
            'timezone' => 'nullable|string|max:4',
            'wonderpush_id' => 'nullable|string|max:36|exists:App\Models\DomainWonderpush,id',
            'premium_tokens' => 'required|numeric',
            'is_whitelabel' => 'required|numeric',
            'has_voluum_tracking' => 'required|boolean',
            'use_subscriptions' => 'required|boolean',
            'is_seedlisting' => 'nullable|boolean',
            'has_mailerq' => 'required|boolean',
            'mailerq_mail_url' => 'string|required_if:has_mailerq,1',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->commonValidationPrepare();
    }

    public function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json(['message' => (new ValidationException($validator))->errors()], Response::HTTP_UNPROCESSABLE_ENTITY));
    }
}
