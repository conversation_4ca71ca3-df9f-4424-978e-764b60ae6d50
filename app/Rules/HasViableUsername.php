<?php

namespace App\Rules;

use App\Repository\UserRepository;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Validation\ValidationRule;

class HasViableUsername implements ValidationRule
{
    private UserRepository $userRepository;

    /** @throws BindingResolutionException */
    public function __construct()
    {
        $this->userRepository = app()->make(UserRepository::class);
    }

    /** @throws \Exception */
    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        if ($this->userRepository->generateUsernameWithSuffix($value) === false) {
            $fail(__('Cannot generate a username, please change your username'));
        }
    }
}
