<?php

namespace App\Rules;

use App\Clients\VerifyEmailClient;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidEmailAddress implements ValidationRule
{
    private string $status;

    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        try {
            $emailValidationService = app()->make(VerifyEmailClient::class);
            $verification = $emailValidationService->verify($value);
            $this->status = is_bool($verification)
                ? $verification
                : (property_exists($verification, 'status') ? $verification->status : false);

            if (!($verification === true || $this->status === VerifyEmailClient::STATUS_OK)) {
                if ($this->status === VerifyEmailClient::STATUS_BLACK) {
                    $fail(__('This e-mail provider is not supported. <br />Please use another e-mail address.'));
                } else {
                    $fail(__("This e-mail address is invalid. <br />Please make sure it's spelled correctly."));
                }
            }
        } catch (\Exception $e) {
            $fail(__("This e-mail address is invalid. <br />Please make sure it's spelled correctly."));
        }
    }
}
