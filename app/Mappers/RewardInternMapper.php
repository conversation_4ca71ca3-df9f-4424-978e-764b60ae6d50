<?php

namespace App\Mappers;

use App\Models\User;
use App\ValueObjects\Rewards\DefaultInternReward;
use App\ValueObjects\Rewards\Send5MessagesInternReward;
use Illuminate\Support\Collection;
use Mirzacles\Models\RewardIntern;

class RewardInternMapper
{
    public static function map(Collection $rewards, User $user): Collection
    {
        return $rewards->map(function (RewardIntern $rewardIntern) use ($user) {
            $rewardClass = 'App\\ValueObjects\\Rewards\\'
                . str_replace(' ', '', ucwords($rewardIntern->title)) . 'InternReward';

            if (class_exists($rewardClass) && $rewardClass === Send5MessagesInternReward::class) {
                return new Send5MessagesInternReward($rewardIntern, $user->countSentMessages($rewardIntern->created_at));
            }

            return new DefaultInternReward($rewardIntern);
        });
    }
}
