<?php

namespace App\Mappers;

use Illuminate\Database\Eloquent\Collection;
use Mirzacles\Models\RewardExtern;

class RewardExternMapper
{
    public static function map(Collection $rewards)
    {
        return $rewards->filter(function (RewardExtern $reward) {
            if ($reward->visible) {
                return true;
            }

            return !$reward->rewardedUsers->isEmpty();
        });
    }
}
