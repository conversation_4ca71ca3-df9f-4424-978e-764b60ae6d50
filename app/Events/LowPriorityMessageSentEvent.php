<?php

namespace App\Events;

use App\Models\Domain;
use App\Models\Messages\MessageModel;
use Illuminate\Queue\SerializesModels;

class LowPriorityMessageSentEvent
{
    use SerializesModels;

    private MessageModel $message;

    private Domain $domain;

    public function __construct(Domain $domain, MessageModel $message)
    {
        $this->message = $message;
        $this->domain = $domain;
    }

    public function getMessage(): MessageModel
    {
        return $this->message;
    }

    public function getDomain(): Domain
    {
        return $this->domain;
    }
}
