<?php

namespace App\Transformers;

use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use League\Fractal\TransformerAbstract;
use NumberFormatter;

class ApiOrderTransformer extends TransformerAbstract
{
    public function transform(Order $order): array
    {
        $domain = Auth::user()->domain;

        return [
            'username' => $order->user->username,
            'amount' => (new NumberFormatter($domain->locale, NumberFormatter::CURRENCY))->formatCurrency($order->amount, $domain->currency),
            'date' => $this->getDate($order->updated_at),
            'status' => $order->status,
            'product' => $order->product->credits . ' credits',
            'website' => $order->domain->name_url,
        ];
    }

    private function getDate(Carbon $date): string
    {
        if (Carbon::now()->lte($date->addDays(9))) {
            return $date->format('H:i');
        }

        return $date->format('d-m-Y H:i');
    }
}
