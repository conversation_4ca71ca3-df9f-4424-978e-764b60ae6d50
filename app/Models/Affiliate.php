<?php

namespace App\Models;

class Affiliate extends \Mirzacles\Models\Affiliate
{
    public const ALIAS_7CLICKUS = '7clickus';

    public const ALIAS_AD_NUKE = 'adnuke';

    public const ALIAS_ADTHORITY = 'adthority';

    public const ALIAS_CAPETRACK = 'capetrack';

    public const ALIAS_CDR = 'cdr';

    public const ALIAS_CDREV = 'cdrev';

    public const ALIAS_CDREV2 = 'cdrev2';

    public const ALIAS_CSHARKS = 'csharks';

    public const ALIAS_CSHARKS2 = 'csharks2';

    public const ALIAS_KINGZ_TRAFFIC = 'kingztraffic';

    public const ALIAS_LOSPOLLOS = 'lospollos';

    public const ALIAS_MASON = 'mason';

    public const ALIAS_MEDIA_LIREV = 'medialirev';

    public const ALIAS_MEDIA_REVEXT = 'medialirevext';

    public const ALIAS_MEDIA45 = 'media45';

    public const ALIAS_MIC = 'mic';

    public const ALIAS_MV_SECURE = 'mvsecure';

    public const ALIAS_MV_SECURE_CH = 'mvsecurech';

    public const ALIAS_SECURELANDING = 'securelanding';

    public const ALIAS_SECURETRK = 'securetrk';

    public const ALIAS_SPICY = 'spicy';

    public const ALIAS_STASAFF = 'stasaff';

    public const ALIAS_TRAFFIC_COMPANY = 'trafficcompany';

    public const ALIAS_TRAFFIC_STARS = 'trafficstars';

    public const ALIASES_LOSPOLLOS = ['lospollos', 'lospollosus', 'lospollosrevca', 'lospollosrevau', 'lospollosus', 'lospollossocialau', 'lospollossocialca', 'lospollossocialuk'];

    public const ALIASES_MEDIABUY = ['fabtrk', 'fabapi', 'fabrest', 'nlstrk', 'fabtest'];

    public const ALIASES_REACTIVATE_SIGNUP_AGAIN = [];

    public const ALIASES_TRKADV = ['trkadvus'];

    public const ALIASES_SOYANK = ['soyank', 'soyankuk'];

    public const ALIAS_ZENTRK = 'zentrk';

    public const TYPE_PPSOI = 'ppsoi';

    public const TYPE_PPL = 'ppl';

    public const TYPE_REV = 'rev';

    public const DEFAULT_MAIL_PROVIDER = null;

    public const NETCORE_MAIL_PROVIDER = 3;
}
