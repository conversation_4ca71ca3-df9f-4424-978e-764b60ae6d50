<?php

namespace App\Models\Messages;

use App\Exceptions\ModelException;

class MessageFactory
{
    /**
     * @throws ModelException
     */
    public static function generateMessageModel(int $siteId): MessageModel
    {
        switch ($siteId) {
            case 1:
                return new Message1;
            case 2:
                return new Message2;
            case 3:
                return new Message3;
            case 4:
                return new Message4;
            case 5:
                return new Message5;
            case 6:
                return new Message6;
            case 7:
                return new Message7;
            case 8:
                return new Message8;
            case 9:
                return new Message9;
            case 10:
                return new Message10;
            case 11:
                return new Message11;
            case 12:
                return new Message12;
            case 13:
                return new Message13;
            case 14:
                return new Message14;
            case 15:
                return new Message15;
            case 22:
                return new Message22;
            case 0:
                return new Message;
        }

        throw ModelException::throwModelNotFoundException($siteId);
    }
}
