<?php

namespace App\Models;

use App\Services\TagsReplacerService;

class EmailType extends \Mirzacles\Models\EmailType
{
    public function getSubject(string $domainId, array $payload, string $default = ''): string
    {
        $subjects = $this->subjects()
            ->wherePivot('domain_id', $domainId)
            ->where('active', true)
            ->get();

        if ($subjects->isEmpty()) {
            return TagsReplacerService::replaceV2($default, $payload);
        }

        return TagsReplacerService::replaceV2($subjects->random()->title, $payload);
    }
}
