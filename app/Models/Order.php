<?php

namespace App\Models;

use App\Services\Payment\PaymentFactory;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\Order
 *
 * @property string $id
 * @property string $domain_id
 * @property int $order_id
 * @property string $product_type
 * @property string $product_id
 * @property string $user_id
 * @property int $amount
 * @property int $amount_revshare
 * @property string $payment_profile
 * @property string|null $payment_method_id
 * @property string $status
 * @property string $ip
 * @property int $site_id
 * @property int $updated
 * @property int $custom
 * @property int $mobile
 * @property int $active
 * @property int $archived
 * @property string $device
 * @property string|null $ext_id
 * @property string|null $comments
 * @property string $offer_batch_id
 * @property int|null $vat
 * @property-read int $has_trial
 * @property-read int $is_renewal
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Domain $domain
 * @property-read mixed $amount_ex_vat
 * @property-read int $amount_in_cents
 * @property-read mixed $vat_amount
 * @property-read Model|Eloquent $product
 * @property-read User $user
 *
 * @method static Builder|Order newModelQuery()
 * @method static Builder|Order newQuery()
 * @method static Builder|Order query()
 *
 * @mixin Eloquent
 * @mixin \Mirzacles\Models\Order
 */
class Order extends \Mirzacles\Models\Order
{
    public const STATUS_FAILED = 'Failed';

    public const STATUS_TRIAL = 'Trial';

    public function getRetryRoute(): string
    {
        return route($this->isForDefaultProduct() ? 'postTransaction' : 'postTransactionPremium');
    }

    public function getProductPageRoute(): string
    {
        return route($this->isForDefaultProduct() ? 'pay' : 'premium');
    }

    public function isApproved(?string $id = null): bool
    {
        return PaymentFactory::create($this->payment_method_id, request())
            ->isApproved(is_null($id) ? $this->order_id : $id);
    }

    public function getTransactionStatus(?string $id = null)
    {
        return PaymentFactory::create($this->payment_method_id, request())
            ->getTransactionStatus(
                is_null($id) ? $this->order_id : $id
            );
    }
}
