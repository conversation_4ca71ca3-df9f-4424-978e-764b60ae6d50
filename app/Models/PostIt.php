<?php

namespace App\Models;

/**
 * App\Models\PostIt
 *
 * @property string $id
 * @property string|string[]|null $post
 * @property string $image
 * @property string|null $user_id
 * @property string|null $profile_id
 * @property int $sent_by_user
 * @property int $site_id
 * @property string|null $template_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|\App\Models\Profile $poster
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\User[] $likes
 * @property-read \App\Models\Profile|null $profile
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder|PostIt newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PostIt newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PostIt query()
 *
 * @mixin \Eloquent
 */
class PostIt extends \Mirzacles\Models\PostIt
{
    /**
     * @return string|string[]|null
     */
    public function getPostAttribute($value)
    {
        return strip_private_data($value);
    }
}
