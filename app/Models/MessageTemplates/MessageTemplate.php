<?php

namespace App\Models\MessageTemplates;

use App\Exceptions\ModelException;
use App\Models\Messages\MessageFactory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MessageTemplate extends Model
{
    use HasUuids;

    protected $guarded = ['id'];

    public $timestamps = false;

    /**
     * @throws ModelException
     */
    public function messageModel(): hasMany
    {
        return $this->hasMany(MessageFactory::generateMessageModel($this->getSiteIdFromTable()), 'template_id');
    }

    public function getSiteIdFromTable(): int
    {
        preg_match('/^(?P<prefix>\w+)_(\d+)$/', $this->table, $matches);

        return (int) end($matches);
    }
}
