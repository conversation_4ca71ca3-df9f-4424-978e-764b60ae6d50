<?php

namespace App\Models;

use App\Helpers\Functions;
use Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

/**
 * App\Models\ChatGroupPost
 *
 * @property string $id
 * @property string $message
 * @property string $chatter_id
 * @property int $likes
 * @property string $type
 * @property string $chat_group_id
 * @property string $user_id
 * @property string $profile_id
 *
 * @method static Builder|ChatGroupPost newModelQuery()
 * @method static Builder|ChatGroupPost newQuery()
 * @method static Builder|ChatGroupPost query()
 *
 * @mixin Eloquent
 */
class ChatGroupPost extends \Mirzacles\Models\ChatGroupPost
{
    protected static function boot(): void
    {
        parent::boot();

        static::addGlobalScope('order', function (Builder $builder) {
            $builder->orderByDesc('created_at');
        });
    }

    public function isAuthor(): bool
    {
        return auth()->user()->id === $this->user_id;
    }

    public function userLikesPost(): bool
    {
        return in_array($this->id, Auth::user()->likedPostsArray());
    }

    public function getMessageAttribute($value)
    {
        $value = strip_private_data($value);
        $value = nl2br($value);

        return Functions::makeEmoji($value);
    }

    public function getIsFromProfileAttribute(): bool
    {
        return $this->profile_id !== null;
    }

    public function getIsFromCurrentUserAttribute(): bool
    {
        return $this->user_id == Auth::user()->id;
    }

    public function totalLikes(): int
    {
        return $this->likes()->whereNotNull('profile_id')->count() + ($this->userLikesPost() ? 1 : 0);
    }
}
