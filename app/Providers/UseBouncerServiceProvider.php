<?php

namespace App\Providers;

use App\Clients\UseBouncerClient;
use GuzzleHttp\Client;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;

class UseBouncerServiceProvider extends ServiceProvider implements DeferrableProvider
{
    public function register(): void
    {
        $this->app->bind(UseBouncerClient::class, function () {
            $client = new Client([
                'base_uri' => Config::get('services.usebouncer.url'),
                'headers' => [
                    'x-api-key' => Config::get('services.usebouncer.key'),
                ],
            ]);

            return new UseBouncerClient($client);
        });
    }

    public function provides()
    {
        return [UseBouncerClient::class];
    }
}
