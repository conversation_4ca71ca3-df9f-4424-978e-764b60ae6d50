<?php

namespace App\Providers;

use Gateway\App;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\ServiceProvider;

class GatewayAppServiceProvider extends ServiceProvider implements DeferrableProvider
{
    /**
     * {@inheritDoc}
     */
    public function register(): void
    {
        $this->app->bind(App::class, function () {
            Request::get('domain');
        });
    }

    /**
     * {@inheritDoc}
     */
    public function provides()
    {
        return [App::class];
    }
}
