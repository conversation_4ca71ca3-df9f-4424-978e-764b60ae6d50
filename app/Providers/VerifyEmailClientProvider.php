<?php

namespace App\Providers;

use App\Clients\VerifyEmailClient;
use GuzzleHttp\Client;
use Illuminate\Support\ServiceProvider;

class VerifyEmailClientProvider extends ServiceProvider
{
    /**
     * {@inheritDoc}
     */
    public function register(): void
    {
        $this->app->bind(VerifyEmailClient::class, fn () => new VerifyEmailClient(
            new Client(['base_uri' => config('verify_email_client.base_url')]),
            config('verify_email_client.api_key')
        ));
    }

    /**
     * {@inheritDoc}
     */
    public function provides(): array
    {
        return [VerifyEmailClient::class];
    }
}
