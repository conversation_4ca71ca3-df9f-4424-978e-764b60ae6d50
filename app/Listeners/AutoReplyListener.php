<?php

namespace App\Listeners;

use App\Events\HighPriorityMessageSentEvent;
use App\Events\LowPriorityMessageSentEvent;
use App\Repository\AutoReplyRepository;
use Carbon\Carbon;

class AutoReplyListener
{
    private AutoReplyRepository $autoReplyRepository;

    public function __construct(AutoReplyRepository $autoReplyRepository)
    {
        $this->autoReplyRepository = $autoReplyRepository;
    }

    public function handle(LowPriorityMessageSentEvent $event): void
    {
        $message = $event->getMessage();
        $user = $message->user;
        $type = $message->getRawOriginal('message');
        $autoReply = $this->autoReplyRepository->getRandomAutoReply($user, $type);

        if ($autoReply === null || !$user->hasAffiliate()) {
            event(new HighPriorityMessageSentEvent($event->getDomain(), $message));

            return;
        }

        $this->autoReplyRepository->queue(
            $autoReply,
            $user->id,
            $message->profile_id,
            Carbon::parse('+' . \rand(2, 6) . ' minutes')
        );
    }
}
