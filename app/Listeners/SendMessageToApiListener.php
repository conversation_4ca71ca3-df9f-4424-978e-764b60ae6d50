<?php

namespace App\Listeners;

use App\Clients\ModerationClient;
use App\Events\HighPriorityMessageSentEvent;
use App\Models\Domain;
use App\Models\Messages\MessageFactory;
use App\Models\Messages\MessageModel;
use App\Models\Profile;
use App\Models\Upload;
use App\Models\User;
use App\Repository\LogRepository;
use App\Repository\MessageApiRepository;
use App\Repository\MessageRepository;
use App\Repository\ProfileThemeRepository;
use App\Repository\UploadRepository;
use App\Services\Attachment;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Mirzacles\Models\MessageApiIncoming;
use Mirzacles\Models\Userlog;

class SendMessageToApiListener implements ShouldQueue
{
    private LogRepository $logRepository;

    private MessageApiRepository $messageApiRepository;

    private MessageRepository $messageRepository;

    private ModerationClient $moderationClient;

    private UploadRepository $uploadRepository;

    private ProfileThemeRepository $profileThemeRepository;

    private Attachment $attachment;

    /**
     * @var string
     */
    public $queue = 'p1_clients_chat_messages';

    public function __construct(
        LogRepository $logRepository,
        MessageApiRepository $messageApiRepository,
        MessageRepository $messageRepository,
        ModerationClient $moderationClient,
        UploadRepository $uploadRepository,
        ProfileThemeRepository $profileThemeRepository,
        Attachment $attachment
    ) {
        $this->logRepository = $logRepository;
        $this->messageApiRepository = $messageApiRepository;
        $this->messageRepository = $messageRepository;
        $this->moderationClient = $moderationClient;
        $this->uploadRepository = $uploadRepository;
        $this->profileThemeRepository = $profileThemeRepository;
        $this->attachment = $attachment;
    }

    public function handle(HighPriorityMessageSentEvent $messageCreated): void
    {
        $message = $messageCreated->getMessage();
        $domain = $messageCreated->getDomain();

        $this->logRepository->setDomain($domain);
        $this->messageApiRepository->setDomain($domain);
        $this->uploadRepository->setDomain($domain);
        $this->profileThemeRepository->setDomain($domain);

        $dayLimit = $domain->chatToolSetting->day_limit;
        $hasDayLimit = ($dayLimit > 0);
        $hasApiConversation = ($this->messageApiRepository->hasConversation($message->user, $message->profile));

        $apiMaxMessagesPerDay = $hasDayLimit ? $dayLimit : 999999;
        if ($hasApiConversation || !$hasDayLimit) {
            $apiMessagesToday = 0;
        } elseif ($hasDayLimit) {
            $apiMessagesToday = $this->messageApiRepository->countMessagesToday();
        }

        if (!$hasApiConversation && $apiMessagesToday > $apiMaxMessagesPerDay) {
            Log::info('Message not send to api because there is no conversation and the messages did exceed', [
                'message_id' => $message->id,
                'domain_id' => $domain->id,
            ]);

            return;
        }

        if ($hasApiConversation) {
            $latest = MessageApiIncoming::query()
                ->where('user_id', $message->user->id)
                ->where('profile_id', $message->profile->id)
                ->latest()
                ->first();

            $newerThan = $latest->created_at;
        }

        $transactionGuid = $this->createTransactionGuid($message);

        $this->messageApiRepository->create($message, $transactionGuid);

        $this->sendConversations($message, $domain, $transactionGuid, $hasApiConversation, $newerThan ?? null);
        $this->sendUsers($message->user, $message->profile, $domain, $transactionGuid);
    }

    private function createTransactionGuid(MessageModel $message): string
    {
        $messageApi = $this->messageApiRepository->getLatestTransactionGuid($message);

        if ($messageApi !== null) {
            return $messageApi->transaction_guid;
        }

        return sha1($message->id . $message->user->username . Carbon::now()->format('YmdHis'));
    }

    private function sendConversations(
        MessageModel $message,
        Domain $domain,
        string $transactionGuid,
        bool $hasApiConversation,
        $newerThan
    ) {
        $themeId = $this->getThemeId($message, $domain);

        /** @var \Illuminate\Database\Eloquent\Collection $messages */
        $messages = MessageFactory::generateMessageModel($message->user->site_id)
            ->select([
                'id',
                'message',
                'created_at',
                'user_id',
                'profile_id',
                'template_id',
                'planning_id',
                'type',
                'upload_id',
                'sent_by_user',
            ])
            ->where('user_id', $message->user->id)
            ->where('profile_id', $message->profile->id)
            ->where('visible', true)
            ->when($hasApiConversation, function (Builder $query) use ($newerThan) {
                $query->where('created_at', '>', $newerThan);
            })
            ->orderBy('created_at')
            ->get();

        if ($messages->isEmpty()) {
            return;
        }

        $lastId = $messages->last()->id ?? null;

        $fields = $messages->reduce(
            function ($carry, $message) use ($domain, $transactionGuid, $themeId, $lastId) {
                $carry[] = $this->prepareConversation(
                    $message,
                    $domain,
                    $transactionGuid,
                    $themeId,
                    ($lastId === $message->id)
                );

                return $carry;
            },
            []
        );

        $this->moderationClient->sendConversation(array_chunk($fields, 50));
    }

    private function getThemeId(MessageModel $message, Domain $domain): ?string
    {
        if (($profileTheme = $this->profileThemeRepository->get($message->profile)) !== null) {
            $this->profileThemeRepository->touch($profileTheme);

            return $profileTheme->theme_id;
        }

        $uploads = $this->uploadRepository->getUploadsOfUser($message->profile, ['upload', 'private']);

        if ($uploads->isEmpty()) {
            return null;
        }

        $profile = $message->profile;
        $url = $domain->full_url . '/uploads/';
        $mediaFiles = $uploads->reduce(function (array $carry, Upload $upload) use ($url) {
            $carry[$upload->id] = ['originalUrl' => $url . $upload->file];

            return $carry;
        }, []);

        $fields = [
            'name' => $profile->username,
            'description' => 'Attachment photos for ' . $profile->username,
            'mediaFiles' => $mediaFiles,
        ];

        $response = $this->moderationClient->sendTheme($fields);

        if ($response->getStatusCode() !== 200) {
            Log::error('Failed sending theme', [
                'status_code' => $response->getStatusCode(),
                'body' => $response->getBody()->getContents(),
            ]);

            return null;
        }

        $response = json_decode($response->getBody()->getContents(), true);

        $profileTheme = $this->profileThemeRepository->create($profile, (int) $response['data']['id']);

        return $profileTheme->id;
    }

    private function prepareConversation(
        MessageModel $message,
        Domain $domain,
        string $transactionGuid,
        ?string $themeId,
        bool $deliver = false
    ): array {
        $fields = [
            'transactionGuid' => $transactionGuid,
            'messageGuid' => sha1($message->id . Carbon::now()->format('YmdHis')),
            'message' => $message->message,
            'style' => 'normal',
            'allowedThemes' => [
                $themeId ?? 'false',
            ],
            'deliver' => (int) $deliver,
        ];

        if (
            !$message->sent_by_user
        ) {
            $fields['style'] = 'trigger';
        }

        if ($message->type == 'photo') {
            $fields['style'] = 'attention';
        }

        if ($message->upload instanceof Upload) {
            $uploadFile = $message->upload->file;
            if (Str::endsWith($uploadFile, '.webp')) {
                $uploadFile = $this->changeWebpToJpg($uploadFile);
                $message->upload->update(['file' => $uploadFile]);
            }
            $fields['attachments'] = [
                $domain->full_url . '/uploads/' . $uploadFile,
            ];
        }

        return $fields;
    }

    private function sendUsers(User $user, Profile $profile, Domain $domain, string $transactionGuid): void
    {
        $userLogs = $this->logRepository->lastLogs($user, $profile)->reduce(function ($carry, Userlog $userLog) {
            $carry[] = $userLog->created_at->format('d-m-Y') . ': ' . $userLog->log;

            return $carry;
        }, []);

        $this->sendUser($user, $userLogs, $domain, $transactionGuid);

        $profileLogs = $this->logRepository->lastLogs(null, $profile)->reduce(function ($carry, Userlog $userLog) {
            $carry[] = $userLog->created_at->format('d-m-Y') . ': ' . $userLog->log;

            return $carry;
        }, []);

        $this->sendOperator($profile, $user, $profileLogs, $domain, $transactionGuid);
    }

    private function sendUser(User $user, array $logs, Domain $domain, string $transactionGuid): void
    {
        $userInfo = $user->info;
        $region = $userInfo->region;
        $country = $region->country->name;

        if ($domain->use_distance === 1) {
            $city = ''; // Don't send the city in this case - the distance will be sent with the profile info
        } elseif ($userInfo->city === null) {
            $city = $region->region . ' (' . $country . ')';
        } else {
            $city = $userInfo->city->name . ', ' . $region->region . ' (' . $country . ')';
        }

        $fields = [
            'name' => (empty($user->username) ? null : $user->username),
            'sex' => ($user->gender == 'female' ? 'f' : 'm'),
            'city' => $city,
            'relationship' => (empty($userInfo->marital_status) ? null : $userInfo->marital_status),
            'hobbys' => (empty($userInfo->hobby) ? null : $userInfo->hobby),
            'description' => (empty($userInfo->about) ? null : $userInfo->about) . PHP_EOL . PHP_EOL . implode(PHP_EOL, $logs),
            'age' => (empty($userInfo->birthdate) ? null : $userInfo->birthdate),
            'custom' => 'Enter new logs here (remove this text)',
        ];

        $profileImage = $user->profile_image;

        if (!empty($profileImage)) {
            if (Str::endsWith($profileImage, '.webp')) {
                $profileImage = $this->changeWebpToJpg($profileImage);
                $user->update(['profile_image' => $profileImage]);
            }
            $fields['images'][] = $domain->full_url . '/uploads/150/' . $profileImage;
        }

        $result = $this->moderationClient->sendUser($fields, $transactionGuid);
        $resultData = json_decode($result->getBody()->getContents(), true);

        if ($resultData['code'] !== 200) {
            Log::error('failed to send user info', ['fields' => $fields]);
        }
    }

    private function sendOperator(Profile $profile, User $user, array $logs, Domain $domain, string $transactionGuid): void
    {
        $profileInfo = $profile->info;
        $region = $profileInfo->region;

        if ($domain->use_distance === 1) {
            $city = 'Distance from member: ' . $profileInfo->distance . ' ' . $domain->distance_unit . ' away';
        } elseif ($profileInfo->city !== null) {
            $city = $profileInfo->city->name . ', ' . $region->name . ' (' . $region->country->name . ')';
        } elseif ($profile->info->region !== null) {
            $city = $region->name . ' (' . $region->country->name . ')';
        } else { // Grab data from user if user has no region info
            $userInfo = $user->info;
            $region = $userInfo->region;
            $country = $region->country->name;
            if ($userInfo->city === null) {
                $city = $region->region . ' (' . $country . ')';
            } else {
                $city = $userInfo->city->name . ', ' . $region->region . ' (' . $country . ')';
            }
        }

        $fields = [
            'name' => $profile->username . (empty($profile->email) ? '' : ' (' . $profile->email . ')'),
            'sex' => ($profile->gender == 'female' ? 'f' : 'm'),
            'city' => $city,
            'relationship' => (empty($profile->infomarital_status) ? null : $profile->infomarital_status),
            'hobbys' => (empty($profileInfo->hobby) ? null : $profileInfo->hobby),
            'description' => (empty($profileInfo->about) ? null : $profileInfo->about) . PHP_EOL . PHP_EOL . implode(PHP_EOL, $logs),
            'age' => (empty($profileInfo->age) ? null : $profileInfo->age),
            'custom' => 'Enter new logs here (remove this text)',
            'size' => (empty($profileInfo->length) ? null : $profileInfo->length),
            'hair' => (empty($profileInfo->hair_color) ? null : $profileInfo->hair_color),
            'eyes' => (empty($profileInfo->eye_color) ? null : $profileInfo->eye_color),
        ];

        if (!empty($profile->profile_image)) {
            $fields['images'][] = $domain->full_url . '/uploads/150/' . $profile->profile_image;
        }

        $result = $this->moderationClient->sendModerator($fields, $transactionGuid);
        $resultData = json_decode($result->getBody()->getContents(), true);

        if ($resultData['code'] !== 200) {
            Log::error('failed to send user info', ['fields' => $fields]);
        }
    }

    public function changeWebpToJpg(string $filename): string
    {
        $filenameJpg = $this->attachment->generateThumbnails($filename);
        $this->attachment->deleteImageFile($filename);

        return $filenameJpg;
    }
}
