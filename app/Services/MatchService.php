<?php

namespace App\Services;

class MatchService
{
    public static function shouldMatch(int $totalMatches): bool
    {
        $totalMatches = ($totalMatches > 299) ? $totalMatches - 299 : $totalMatches;

        if ($totalMatches > 299) {
            return false;
        }

        return in_array($totalMatches, [7, 23, 42, 64, 75, 94, 105, 132, 152, 153, 170, 186, 204, 228, 252, 281, 299]);
    }
}
