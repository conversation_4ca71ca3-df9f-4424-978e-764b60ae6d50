<?php

namespace App\Services;

use App\Models\Affiliate;
use App\Models\User;

class DisablePreviousAccount
{
    protected const RULES = [
        ['aliases' => Affiliate::ALIASES_MEDIABUY],
        ['aliases' => Affiliate::ALIASES_SOYANK],
        ['aliases' => Affiliate::ALIASES_TRKADV],
        ['aliases' => [Affiliate::ALIAS_ZENTRK]],
        ['aliases' => [Affiliate::ALIAS_MASON], 'subs' => [1 => '4', 4 => 'clean']],
        ['aliases' => [], 'subs' => [2 => 'cross']],
    ];

    public static function disable(User $user, ?string $affiliateAlias, ?string $affiliateSource = null): bool
    {
        $subs = $affiliateSource !== null ? explode('_', $affiliateSource) : [];

        $disableAccount = false;

        foreach (self::RULES as $rule) {
            $ruleHasAliases = isset($rule['aliases']) && $rule['aliases'] !== [];
            $ruleHasSubs = isset($rule['subs']) && $rule['subs'] !== [];

            if (!$ruleHasAliases && !$ruleHasSubs) {
                continue;
            }

            $passesAliasRule = in_array($affiliateAlias, $rule['aliases']) || $rule['aliases'] === [];

            $passesSubsRules = !$ruleHasSubs || array_intersect($subs, $rule['subs']) === $rule['subs'];

            if ($passesAliasRule && $passesSubsRules) {
                $disableAccount = true;
            }
        }

        if (!$disableAccount) {
            return false;
        }

        // SOI can be signed up again after 24 hours
        if ($user->active === 0 && $user->created_at->gt(now()->subHours(24))) {
            return false;
        }

        // DOI can be signed again when no credits are left or more than 30 days inactive
        if (
            $user->active === 1 &&
            ($user->credits > 0 || $user->updated_at->gt(now()->subDays(30)))
        ) {
            return false;
        }

        $user->update([
            'username' => $user->username . '_rm' . now()->getTimestamp(),
            'status' => false,
            'unsubscribed_at' => now(),
            'deleted_at' => now()->getTimestamp(),
        ]);

        return true;
    }
}
