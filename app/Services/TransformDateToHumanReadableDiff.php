<?php

namespace App\Services;

use Carbon\Carbon;

class TransformDateToHumanReadableDiff
{
    public static function transform(Carbon $date): string
    {
        $now = Carbon::now();

        $diffInYears = (int) $now->diffInYears($date, true);
        $subtractedYears = $now->copy()->subYears($diffInYears);

        if ($diffInYears !== 0) {
            $yearText = ($diffInYears === 1) ? 'year' : 'years';

            return $diffInYears . ' ' . $yearText . ' ago';
        }

        $diffInMonths = (int) $subtractedYears->diffInMonths($date, true);
        $subtractedMonths = $now->copy()->subMonths($diffInMonths);

        if ($diffInMonths !== 0) {
            $monthText = ($diffInMonths === 1) ? 'month' : 'months';

            return $diffInMonths . ' ' . $monthText . ' ago';
        }

        $diffInDays = (int) $subtractedMonths->diffInDays($date, true);
        $subtractedDays = $subtractedMonths->copy()->subDays($diffInDays);

        if ($diffInDays !== 0 || $diffInMonths !== 0) {
            $dayText = ($diffInDays <= 1) ? 'day' : 'days';

            return $diffInDays . ' ' . $dayText . '  ago';
        }

        $diffInHours = (int) $subtractedDays->diffInHours($date, true);
        $subtractedHours = $subtractedDays->copy()->subHours($diffInHours);

        if ($diffInHours !== 0) {
            $hourText = ($diffInHours <= 1) ? 'hour' : 'hours';

            return $diffInHours . ' ' . $hourText . ' ago';
        }

        $diffInMinutes = (int) $subtractedHours->diffInMinutes($date, true);
        $minuteText = ($diffInMinutes === 1) ? 'minute' : 'minutes';

        return $diffInMinutes . ' ' . $minuteText . ' ago';
    }
}
