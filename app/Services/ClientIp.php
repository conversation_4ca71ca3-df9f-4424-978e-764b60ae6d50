<?php

namespace App\Services;

use Illuminate\Http\Request;

class ClientIp
{
    public static function getIp(Request $request): ?string
    {
        // Custom header for client IP
        if ($request->headers->has('X-Client-IP')) {
            return $request->header('X-Client-IP');
        }

        // Header for clients IP through Cloudflare
        if ($request->server->has('HTTP_CF_CONNECTING_IP')) {
            return $request->server->get('HTTP_CF_CONNECTING_IP');
        }

        // Header for clients IP through Sucuri
        if ($request->server->has('HTTP_X_SUCURI_CLIENTIP')) {
            return $request->server->get('HTTP_X_SUCURI_CLIENTIP');
        }

        return $request->getClientIp();
    }
}
