<?php

namespace App\Services;

use App\Events\HighPriorityMessageSentEvent;
use App\Events\LowPriorityMessageSentEvent;
use App\Models\Domain;
use App\Models\Messages\MessageModel;
use App\Repository\AutoReplyRepository;
use App\Repository\ConversationRepository;
use App\Repository\MessageRepository;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Mirzacles\Models\Chatter;

class MessageExchange
{
    private AutoReplyRepository $autoReplyRepository;

    private MessageRepository $messageRepository;

    private ConversationRepository $conversationRepository;

    public function __construct(
        AutoReplyRepository $autoReplyRepository,
        MessageRepository $messageRepository,
        ConversationRepository $conversationRepository
    ) {
        $this->autoReplyRepository = $autoReplyRepository;
        $this->messageRepository = $messageRepository;
        $this->conversationRepository = $conversationRepository;
    }

    public function queue(Domain $domain, MessageModel $message): void
    {
        // if (app()->environment() === 'local') {
        //     return;
        // }

        if (
            $message->type === MessageModel::TYPE_FLIRT &&
            $this->shouldSendLowPriorityMessage($message)
        ) {
            event(new LowPriorityMessageSentEvent($domain, $message));

            return;
        }

        // Get chat tool settings from the database
        $chatToolSetting = $domain->chatToolSetting;

        // If chat tool settings exist and percentage is larger than 0 then external chat API is available
        $isChatApiAvailable = $chatToolSetting && $chatToolSetting->percentage > 0;

        // Operators percentage is the remaining of the chat API percentage, when available
        $operatorsPercentage = $isChatApiAvailable ? (100 - $chatToolSetting->percentage) : 100;

        // If external API chat is not available then message is sent to internal API
        $sendToOperators = !$isChatApiAvailable;

        // Get the chatter_id from the last latest message that has one
        $chatterId = $this->conversationRepository->getChatterId($message);

        if ($isChatApiAvailable && $operatorsPercentage > 0) {
            // If external chat API is available and chatter_id exists
            // Check if last chatter belongs to internal API
            // If chatter belongs to internal API is sent there otherwise it is according to its precentage
            // If internal API percentage is 0 then it is sent to external chat API
            $sendToOperators = $chatterId ? (bool) Chatter::find($chatterId)?->operator?->internal : rand(1, 100) <= $operatorsPercentage;
        }

        if ($sendToOperators && !empty(config('app.operators_api_url'))) {
            Log::info('High priority message sent to operators');
            $url = Str::of('https://')->append(config('app.operators_api_url'))->append('/api/incoming-message')->toString();

            $restricted = $domain->hasMeta('use_restricted_messages') && !$message->user->hasAffiliate();

            $data = [
                'domain_id' => domain()->id,
                'user_id' => $message->user->id,
                'profile_id' => $message->profile->id,
                'conversation_id' => $message->conversation->id,
                'message_id' => $message->id,
                'message' => $message->message ?? null,
                'upload' => $message->upload->file ?? null,
                'chatter_id' => $chatterId ?? null,
                'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                'restricted' => $restricted,
            ];

            try {
                $response = Http::retry(3, 100)->withHeaders([
                    'Accept' => 'application/json',
                    'Accept-Encoding' => 'gzip, deflate, br',
                    'Authorization' => 'Bearer ' . config('app.operators_bearer_token'),
                ])->asJson()->post($url, $data);
            } catch (RequestException $e) {
                Log::error('RequestException occurred while sending high priority message to operators', [
                    'url' => $url,
                    'data' => $data,
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                return;
            }

            if (!$response->successful()) {
                Log::info('Request not successful while sending message to operators', [
                    'url' => $url,
                    'data' => $data,
                    'response' => $response,
                    'json' => $response->json(),
                    'exception' => $response->toException() instanceof RequestException ? $response->toException()->getTrace() : null,
                ]);
            }

        } elseif ($isChatApiAvailable) {
            Log::info('High priority message sent to chat API');
            event(new HighPriorityMessageSentEvent($domain, $message));
        }

    }

    private function shouldSendLowPriorityMessage(MessageModel $message): bool
    {
        $user = $message->user;
        $flirtName = $message->getRawOriginal('message');

        if ($this->messageRepository->countExistingMessages($user, $message->profile, $flirtName) > 1) {
            return false;
        }

        return !$this->autoReplyRepository->areAllRepliesUsed($user, $flirtName);
    }
}
