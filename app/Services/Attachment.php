<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Intervention\Image\Laravel\Facades\Image;
use Mirzacles\Helpers\Factories\GCSDisk;

class Attachment
{
    public const IMAGE_QUALITY = 75;

    public function __construct()
    {
        // This ini settings only needs when file size is bigger a
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', '100');
    }

    public function process(string $path, string $type = 'upload'): ?string
    {
        try {
            if (!File::exists($path)) {
                throw new Exception('File not found: ' . $path);
            }

            $imgName = hash('md5', Str::uuid()->toString()) . '.jpg';

            $this->createThumb($path, public_path('uploads') . '/150/' . $imgName, 150, $type);
            $this->createThumb($path, public_path('uploads') . '/300/' . $imgName, 300, $type);
            $this->createThumb($path, 'uploads/150/' . $imgName, 150, $type, gcs: true);
            $this->createThumb($path, 'uploads/300/' . $imgName, 300, $type, gcs: true);
            $this->createThumb($path, 'uploads/thumb/' . $imgName, 300, $type, gcs: true);

            $image = Image::read($path);
            $width = $image->width();
            $height = $image->height();
            $resize = 900;

            if ($width > $height || $width === $height) {
                $width = $resize;
                $height = null;
            } elseif ($width < $height) {
                $height = $resize;
                $width = null;
            }

            $this->createImage($path, public_path('uploads') . '/' . $imgName, $width, $height, $type);
            $this->createImage($path, 'uploads/' . $imgName, $width, $height, $type, gcs: true);

            return $imgName;

        } catch (Exception $e) {
            Log::error('Attachment process failed: ' . $e->getMessage(), [
                'path' => $path,
                'type' => $type,
            ]);

            return null;
        }
    }

    public function deleteImageFile($filename): void
    {
        $folders = [
            '/uploads/',
            '/uploads/crop/',
            '/uploads/150/',
            '/uploads/150/crop/',
            '/uploads/300/',
            '/uploads/300/crop/',
            '/uploads/thumb/',
            '/uploads/thumb/crop/',
        ];

        $disk = GCSDisk::build('users');

        foreach ($folders as $folder) {
            $localFile = public_path($folder . $filename);

            if (File::exists($localFile)) {
                File::delete($localFile);
            }

            try {
                if ($disk->exists($folder . $filename)) {
                    $disk->delete($folder . $filename);
                }
            } catch (Exception $e) {
                Log::error('GCS delete failed: ' . $e->getMessage(), [
                    'path' => $folder . $filename,
                ]);
            }
        }
    }

    private function createImage(
        string $path,
        string $target,
        ?int $width,
        ?int $height,
        string $type,
        string $format = 'jpeg',
        bool $gcs = false
    ): void {
        $image = Image::read($path);

        $this->applyOrientation($image, $type);

        $image->scaleDown($width, $height);
        $encoded = $image->encodeByExtension($format, quality: self::IMAGE_QUALITY);

        if ($gcs) {
            $this->uploadToGCS($target, (string) $encoded);
        } else {
            $encoded->save($target);
        }
    }

    /**
     * @throws Exception
     */
    private function createThumb(string $path, string $target, int $size, string $type, string $format = 'jpeg', bool $gcs = false): void
    {
        $image = Image::read($path);

        $this->applyOrientation($image, $type);

        $encoded = $image->cover($size, $size)->encodeByExtension($format, quality: self::IMAGE_QUALITY);

        if ($gcs) {
            $this->uploadToGCS($target, (string) $encoded);
        } else {
            $encoded->save($target);
        }
    }

    /**
     * Applies EXIF orientation to the image if type is not 'private'.
     */
    private function applyOrientation($image, string $type): void
    {
        if ($type !== 'private') {
            $orientation = $image->exif('Orientation');

            if ($orientation === 3) {
                $image->rotate(180);
            } elseif ($orientation === 6) {
                $image->rotate(-90);
            } elseif ($orientation === 8) {
                $image->rotate(90);
            }
        }
    }

    private function uploadToGCS(string $target, string $image): void
    {
        try {
            $disk = GCSDisk::build('users');
            $disk->put($target, (string) $image);
            $disk->setVisibility($target, 'public');
        } catch (Exception $e) {
            Log::error('GCS upload failed: ' . $e->getMessage(), [
                'path' => $target,
                'type' => $image,
            ]);
        }
    }

    public function generateThumbnails($filename): string
    {
        // Change filename to .jpg
        $filenameJpg = Str::beforeLast($filename, '.') . '.jpg';
        $format = 'jpeg';

        // Encode image to jpeg
        $image = Image::read(public_path('uploads/' . $filename));
        $image->encodeByExtension($format, quality: self::IMAGE_QUALITY)->save(public_path('uploads/' . $filenameJpg));
        $image->cover(150, 150)->encodeByExtension($format, quality: self::IMAGE_QUALITY)->save(public_path('uploads/150/' . $filenameJpg));
        $image->cover(300, 300)->encodeByExtension($format, quality: self::IMAGE_QUALITY)->save(public_path('uploads/300/' . $filenameJpg));

        return $filenameJpg;
    }
}
