<?php

namespace App\Services\Segpay;

use App\Models\Order;

class SegpayTransactionSRSService extends SegpayAbstractSRSService
{
    public string $address = 'TransactionByTransID';

    public array $queryParameters = ['TransID'];

    public array $xmlChildren = ['TransID', 'Date', 'Time', 'WebSite', 'Type', 'Source', 'Auth', 'Amount', 'AuthCode',
        'Curr', 'CardType', 'CustomerName', 'Country', 'PurchaseID', 'AffiliateID', 'MerchantPartnerID', 'eticketID', 'REF1', ];

    public function __construct($order, $id)
    {
        if ($order instanceof Order) {
            $this->generateUrlFromOrder($order, ['TransID' => $id]);
        }
    }
}
