<?php

namespace App\Services\Payment;

use App\Models\Order;
use App\ValueObjects\Payments\Truevo;

class TruevoApplePayPaymentService extends TruevoPaymentService
{
    public const FAILURE = 'FAILURE';

    public const SUCCESS = 'SUCCESS';

    private Truevo $payment;

    public function __construct(Truevo $payment)
    {
        parent::__construct($payment);
        $this->payment = $payment;
    }

    /**
     * {@inheritDoc}
     */
    public function generate(Order $order)
    {
        $details = [
            'transactionType' => 'final',
            'storeName' => $this->getDomain()->url,
            'countryCode' => strtoupper($this->getDomain()->country_code),
            'merchantReference' => $order->order_id,
            'IdempotencyKey' => $order->id,
            'paymentReference' => $this->getDomain()->name,
            'url' => $this->getDomain()->url,
            'amount' => $order->product->toPay(),
            'currencyAlphaCode' => strtoupper($this->domain->currency),
            'mid' => $this->payment->getMid(),
            'tid' => $this->payment->getTid(),
            'token' => sprintf('Bearer %s', $this->payment->getToken()),
            'cancelUrl' => $this->getReturnUrls($order),
            'email' => $order->user->email,
        ];

        return response()->json(['details' => $details]);
    }

    /**
     * @return array|\Gateway\Entities\Url[]|string|string[]
     */
    public function getReturnUrls(Order $order)
    {
        return route('payCancel', ['order_number' => $order->order_id]);
    }
}
