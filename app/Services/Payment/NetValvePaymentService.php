<?php

namespace App\Services\Payment;

use App\Models\Order;
use App\ValueObjects\Payments\NetValve;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;

class NetValvePaymentService extends AbstractPayment implements PaymentInterface
{
    public const FAILURE = 'FAILURE';

    public const CREATED = 'CREATED';

    private NetValve $payment;

    public function __construct(NetValve $payment)
    {
        $this->payment = $payment;
    }

    /**
     * {@inheritDoc}
     */
    public function generate(Order $order)
    {
        $url = $this->getReturnUrls($order);

        $attributes = [
            'amount' => $order->amount,
            'currency' => strtoupper($this->domain->currency),
            'siteId' => $this->payment->getSiteId(),
            'clientOrderId' => $order->order_id,
            'orderDesc' => $order->product->name,
            'successUrl' => $url['successUrl'],
            'failedUrl' => $url['failedUrl'],
            'cancelUrl' => $url['cancelUrl'],
            'customerDetails' => [
                'customerIp' => request()->getClientIp(),
                'customerEmail' => $order->user->email,
                'customerCity' => $order->user->info->city->name,
                'customerState' => $order->user->info->region->name,
                'customerCountryCode' => $this->getDomain()->country_code,
            ],
        ];

        return Redirect::to(
            $this->getRedirectionUrl($attributes, $order)
        );
    }

    /**
     * @return array|\Gateway\Entities\Url[]|string|string[]
     */
    public function getReturnUrls(Order $order)
    {
        $url = $this->getDomain()->full_url;

        return [
            'successUrl' => sprintf('%s/credits/netvalve/return?orderNumber=%s', $url, $order->order_id),
            'failedUrl' => sprintf('%s/credits/failed?orderNumber=%s', $url, $order->order_id),
            'cancelUrl' => sprintf('%s/credits/netvalve/cancel?orderNumber=%s', $url, $order->order_id),
        ];
    }

    public function getRedirectionUrl($attributes, Order $order)
    {
        $response = Http::withHeaders([
            'Accept' => 'application/json',
            'Accept-Encoding' => 'gzip, deflate, br',
            'Connection' => 'keep-alive',
            'Content-Type' => 'application/json',
            'User-Agent' => request()->server('HTTP_USER_AGENT'),
            'netvalve-api-key' => $this->payment->getApiKey(),
            'netvalve-client-id' => $this->payment->getClientId(),
        ])->post($this->payment->getUrl(), $attributes);

        $orderNumber = $attributes['clientOrderId'];

        if ($response->successful()) {
            $body = $response->json();

            if (isset($body['orderId'])) {
                $order->update(['ext_id' => $body['transactionID']]);
            }

            if (strtoupper($body['orderState']) === self::CREATED) {
                return $body['redirectUrl'];
            }
        }

        Log::info('NetValve Request Error', $response->json());

        return route('payCancel', ['order_number' => $orderNumber]);
    }
}
