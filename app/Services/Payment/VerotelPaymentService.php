<?php

namespace App\Services\Payment;

use App\Models\Order;
use App\Models\Product;
use App\Repository\SubscriptionRepository;
use App\ValueObjects\Payments\Verotel;
use Gateway\Entities\Url;
use Illuminate\Support\Facades\Redirect;

class VerotelPaymentService extends AbstractPayment implements PaymentInterface
{
    private Verotel $payment;

    public function __construct(Verotel $payment)
    {
        $this->payment = $payment;
    }

    /**
     * {@inheritDoc}
     */
    public function generate(Order $order)
    {
        /** @var Product $product */
        $product = $order->product;
        $urls = $this->getReturnUrls($order);
        $attributes = [
            'type' => 'purchase',
            'priceAmount' => $product->toPay(),
            'priceCurrency' => strtoupper($this->domain->currency),
            'description' => $this->getDescription($order),
            'referenceID' => $order->order_id,
            'successURL' => $urls['success'],
            'declineURL' => $urls['fail'],
            'paymentMethod' => $this->payment->getPaymentMethod(),
            'oneClickToken' => $order->user->verotel_oneclicktoken,
            'custom1' => $order->user->id,
        ];

        $attributes['signature'] = $this->getSignature($attributes);
        $attributes['email'] = $order->user->email;

        // Handle recurring payments
        $isSubscription = false;
        if ($order->isForDefaultProduct() && $order->has_trial) {
            $isSubscription = true;
            $productSubscription = app()->make(SubscriptionRepository::class)->getTrial();

            $attributes['trialAmount'] = $product->toPay();
            $attributes['priceAmount'] = $productSubscription->recurring_price;
            $attributes['trialPeriod'] = 'P' . $productSubscription->recurring_period . 'D';
            $attributes['period'] = 'P' . $productSubscription->recurring_period . 'D';
        }

        if ($order->isForSubscriptionProduct()) {
            $isSubscription = true;
            $attributes['priceAmount'] = $product->toPay();
            $attributes['period'] = 'P' . $product->recurring_period . 'D';
        }

        if ($isSubscription) {
            $attributes['type'] = 'subscription';
            $attributes['subscriptionType'] = 'recurring';
            $redirectUrl = $this->payment->getClient()->get_subscription_URL($attributes);
        } else {
            $attributes['type'] = 'purchase';
            $redirectUrl = $this->payment->getClient()->get_purchase_URL($attributes);
        }

        return Redirect::to($redirectUrl);
    }

    private function getSignature(array $attributes): string
    {
        return $this->payment->getClient()->get_signature($attributes);
    }

    public function isApproved(string $orderNumber): bool
    {
        $statusPageData = $this->getTransactionStatus($orderNumber);

        return (bool) strpos($statusPageData, 'saleResult: APPROVED');
    }

    /**
     * @return array|Url[]|string[]
     */
    public function getReturnUrls(Order $order): array
    {
        $url = ($this->getDomain()->https ? 'https://' : 'http://') . $this->getDomain()->url;
        if ($order->isForPremiumProduct()) {
            $back_url = $url . '/premium';
        } else {
            $back_url = $url . ($order->product->location == 'ab' ? '/credits2' : '/credits');
        }

        return [
            'success' => sprintf('%s/%s/return?order_number=%s', $url, $order->getProductTypeName(), $order->order_id),
            'fail' => sprintf('%s/%s/failed?orderNumber=%s', $url, $order->getProductTypeName(), $order->order_id),
            'back' => $back_url,
            'notification' => sprintf(
                '%s/credits/exchange?order_number=%s&user_id=%s&event=payment',
                $url,
                $order->order_id,
                $order->user->id
            ),
        ];
    }

    public function getTransactionStatus(string $orderNumber)
    {
        return file_get_contents(
            $this->payment->getClient()->get_status_URL(['referenceID' => $orderNumber])
        );
    }
}
