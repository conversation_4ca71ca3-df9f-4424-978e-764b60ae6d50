<?php

namespace App\Services\Payment;

use App\Models\Order;
use Gateway\Entities\Url;

class CelerispayIframePaymentService extends CelerispayAbstractPayment implements PaymentInterface
{
    /**
     * {@inheritDoc}
     */
    public function generate(Order $order)
    {
        if (request()->ajax()) {
            return response()->json(['orderId' => $order->order_id]);
        }

        return redirect()->route(
            $order->isForDefaultProduct() ? 'creditsOffer' : 'premiumOffer',
            [
                'offer_id' => $order->product_id,
                'order_id' => $order->id,
            ]
        );
    }

    /**
     * @return array|Url[]|string[]
     */
    public function getReturnUrls(Order $order): array
    {
        $url = $this->gatewayApp->getReturnUrls($order);

        return [
            'url' => $url->iFrame(),
        ];
    }
}
