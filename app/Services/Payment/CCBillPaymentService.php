<?php

namespace App\Services\Payment;

use App\Models\Order;
use App\Repository\SubscriptionRepository;
use App\ValueObjects\Payments\CCBill;
use Illuminate\Support\Facades\Redirect;

class CCBillPaymentService extends AbstractPayment implements PaymentInterface
{
    public const SANDBOX_URL = 'https://sandbox-api.ccbill.com/wap-frontflex/flexforms/';

    public const LIVE_URL = 'https://api.ccbill.com/wap-frontflex/flexforms/';

    public const PRICE_PERIOD = '2';

    public array $currencies = [
        'USD' => '840',
        'EUR' => '978',
        'GBP' => '826',
        'CAD' => '124',
        'AUD' => '036',
        'JPY' => '392',
    ];

    public function __construct(
        private CCBill $payment,
    ) {}

    /**
     * {@inheritDoc}
     */
    public function generate(Order $order)
    {
        $query = [
            'clientSubacc' => $this->payment->getSubAccount(),
            'initialPrice' => number_format($order->product->toPay(), 2),
            'initialPeriod' => self::PRICE_PERIOD,
            'currencyCode' => $this->currencies[strtoupper($this->domain->currency)],
            'email' => $order->user->email,
            'order_number' => $order->order_id,
            'formDigest' => $this->generateFormDigest([
                number_format($order->product->toPay(), 2),
                self::PRICE_PERIOD,
                $this->currencies[strtoupper($this->domain->currency)],
            ]),
        ];

        $productSubscription = null;
        if ($order->isForDefaultProduct() && $order->has_trial) {
            $productSubscription = app()->make(SubscriptionRepository::class)->getTrial();
        }

        if ($order->isForSubscriptionProduct()) {
            $productSubscription = $order->product;
        }

        if ($productSubscription) {
            $query = array_merge($query, [
                'clientSubacc' => $this->payment->getSubAccountRecurring(),
                'initialPrice' => number_format($productSubscription->initial_price, 2),
                'initialPeriod' => $productSubscription->initial_period,
                'recurringPrice' => number_format($productSubscription->recurring_price, 2),
                'recurringPeriod' => $productSubscription->recurring_period,
                'numRebills' => 99,
                'formDigest' => $this->generateFormDigest([
                    number_format($productSubscription->initial_price, 2),
                    $productSubscription->initial_period,
                    number_format($productSubscription->recurring_price, 2),
                    $productSubscription->recurring_period,
                    99,
                    $this->currencies[strtoupper($this->domain->currency)],
                ]),
            ]);
        }

        $link = sprintf('%s%s?%s', $this->getUrl(), $this->payment->getFlexFormId(), http_build_query($query));

        return Redirect::to($link);
    }

    public function getReturnUrls(Order $order)
    {
        return [];
    }

    public function getTransactionStatus(string $orderNumber)
    {
        return false;
    }

    public function generateFormDigest(array $args): string
    {
        $args[] = $this->payment->getEncryptionKey();

        return md5(implode('', $args));
    }

    public function getUrl(): string
    {
        return $this->payment->getLive() === 1 ? self::LIVE_URL : self::SANDBOX_URL;
    }
}
