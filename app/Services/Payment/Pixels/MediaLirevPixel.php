<?php

namespace App\Services\Payment\Pixels;

use App\Models\Affiliate;
use App\Models\Order;
use Illuminate\Support\Collection;

class MediaLirevPixel extends AbstractPixel
{
    private ?string $environment = null;

    /**
     * {@inheritDoc}
     */
    protected function initialize(Order $order): void
    {
        $this->environment = env('APP_LOCATION');
    }

    /**
     * {@inheritDoc}
     */
    protected function mapUrls(Affiliate $affiliate, Order $order, $user, bool $isChargeBack): Collection
    {
        $currency = null;

        if ($this->environment == 'DE') {
            $currency = ($user->site_id === 4) ? 'CHF' : 'EUR';
        }

        $amount = $this->getAmount($order, $affiliate);

        return $this->getPostbackUrls($affiliate, $order, $user, $isChargeBack)
            ->map(fn ($url) => $this->replaceUrl($order, $url, $amount, $user, $currency));
    }
}
