<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ExecuteSqlDumpCommand extends Command
{
    protected $signature = 'app:execute-sql-dump-command';

    protected $description = 'Execute sql dump';

    public function handle(): int
    {
        try {
            DB::unprepared(file_get_contents(database_path('dumps/platform_clients.sql')));
        } catch (\Throwable $throwable) {
            dd($throwable->getMessage());
        }

        return 0;
    }
}
