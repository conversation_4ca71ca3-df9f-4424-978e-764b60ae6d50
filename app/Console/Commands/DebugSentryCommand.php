<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class DebugSentryCommand extends Command
{
    protected $name = 'sentry:debug';

    protected $description = 'Test how exception is send to Sentry';

    public function handle(): void
    {
        try {
            $this->functionFailsForSure();
        } catch (\Throwable $exception) {
            \Sentry\captureException($exception);
        }
    }

    /**
     * @return mixed
     *
     * @throws \Exception
     */
    private function functionFailsForSure()
    {
        throw new \Exception('Debug sentry exception');
    }
}
