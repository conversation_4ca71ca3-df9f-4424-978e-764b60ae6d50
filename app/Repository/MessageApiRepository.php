<?php

namespace App\Repository;

use App\Models\Messages\MessageModel;
use App\Models\Profile;
use App\Models\User;
use Carbon\Carbon;
use Mirzacles\Models\MessageApiIncoming;

class MessageApiRepository extends BaseRepository
{
    public function hasConversation(User $user, Profile $profile): bool
    {
        return MessageApiIncoming::query()
            ->where('user_id', $user->id)
            ->where('profile_id', $profile->id)
            ->exists();
    }

    public function countMessagesToday(): int
    {
        return MessageApiIncoming::query()
            ->where('incoming', false)
            ->where('created_at', '>=', Carbon::now()->startOfDay())
            ->count();
    }

    public function getLatestTransactionGuid(MessageModel $message): ?MessageApiIncoming
    {
        return MessageApiIncoming::query()
            ->select(['transaction_guid'])
            ->where('user_id', $message->user_id)
            ->where('profile_id', $message->profile_id)
            ->where('type', MessageApiIncoming::TYPE_MESSAGE)
            ->orderByDesc('created_at')
            ->first();
    }

    public function create(
        MessageModel $message,
        string $transactionGuid,
        bool $incoming = false,
        ?string $sessionId = null,
        ?string $uploadId = null,
        string $type = 'message'
    ): void {
        MessageApiIncoming::query()
            ->create(
                [
                    'message_id' => $message->id,
                    'site_id' => $message->user->site_id,
                    'user_id' => $message->user_id,
                    'profile_id' => $message->profile_id,
                    'transaction_guid' => $transactionGuid,
                    'incoming' => $incoming,
                    'session_id' => $sessionId,
                    'upload_id' => $uploadId,
                    'type' => $type,
                ]
            );
    }

    public function get(string $transactionGuid): ?MessageApiIncoming
    {
        /** @var MessageApiIncoming $message */
        $message = MessageApiIncoming::query()
            ->where('transaction_guid', $transactionGuid)
            ->where('incoming', false)
            ->where('type', MessageApiIncoming::TYPE_MESSAGE)
            ->orderByDesc('id')
            ->first();

        return $message;
    }

    public function markAsProcessed(MessageApiIncoming $messageApiIncoming): void
    {
        $messageApiIncoming->update(['processed' => true]);
    }

    public function getIncoming(string $transactionGuid): ?MessageApiIncoming
    {
        /** @var MessageApiIncoming $message */
        $message = MessageApiIncoming::query()
            ->where('transaction_guid', $transactionGuid)
            ->where('incoming', true)
            ->whereNotNull('upload_id')
            ->where('processed', false)
            ->first();

        return $message;
    }
}
