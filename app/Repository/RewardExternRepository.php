<?php

namespace App\Repository;

use App\Models\User;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Mirzacles\Models\RewardExtern;
use Mirzacles\Models\UserExternReward;
use Ramsey\Uuid\Uuid;

use function json_encode;

class RewardExternRepository extends BaseRepository
{
    public function all(): Collection
    {
        return RewardExtern::all();
    }

    public function create(array $input): void
    {
        RewardExtern::query()
            ->create(
                [
                    'title' => $input['title'],
                    'note' => (isset($input['note'])) ? $input['note'] : null,
                    'target_url' => $input['target_url'],
                    'active' => (isset($input['active'])) ? $input['active'] : false,
                    'sort' => $input['sort'],
                    'age_range' => json_encode($this->createAgeRange($input)),
                    'must_have_purchased_credits' => isset($input['must_have_purchased_credits']),
                    'hours_after_spent_last_credit' => $input['hours_after_spent_last_credit'],
                    'icon' => $input['icon'],
                    'credits' => $input['credits'],
                    'shadow_credits' => $input['shadow_credits'],
                    'site_id' => $input['site_id'],
                    'max_cap' => (!empty($input['max_cap'])) ? $input['max_cap'] : null,
                    'available_after_signup' => isset($input['available_after_signup']),
                ]
            );
    }

    /**
     * @return bool[]
     */
    private function createAgeRange(array $input): array
    {
        return [
            '18-25' => (isset($input['target_age_1'])),
            '26-35' => (isset($input['target_age_2'])),
            '36-45' => (isset($input['target_age_3'])),
            '46-54' => (isset($input['target_age_4'])),
            '55-99' => (isset($input['target_age_5'])),
        ];
    }

    public function find(string $id): RewardExtern
    {
        return RewardExtern::query()->findOrFail($id);
    }

    public function update(string $id, array $input): void
    {
        RewardExtern::query()
            ->where('id', $id)
            ->update([
                'title' => $input['title'],
                'note' => (isset($input['note'])) ? $input['note'] : null,
                'target_url' => $input['target_url'],
                'active' => (isset($input['active'])) ? $input['active'] : false,
                'sort' => $input['sort'],
                'age_range' => json_encode($this->createAgeRange($input)),
                'must_have_purchased_credits' => isset($input['must_have_purchased_credits']),
                'hours_after_spent_last_credit' => $input['hours_after_spent_last_credit'],
                'icon' => $input['icon'],
                'credits' => $input['credits'],
                'shadow_credits' => $input['shadow_credits'],
                'site_id' => $input['site_id'],
                'max_cap' => (!empty($input['max_cap'])) ? $input['max_cap'] : null,
                'available_after_signup' => isset($input['available_after_signup']),
            ]);
    }

    public function toggleActive(string $id): bool
    {
        $reward = RewardExtern::find($id);

        if ($reward === null) {
            return false;
        }

        return (bool) $reward->update(['active' => !$reward->active]);
    }

    public function allActive(int $siteId): Collection
    {
        return RewardExtern::query()
            ->where('active', true)
            ->where('available_after_signup', false)
            ->where('site_id', $siteId)
            ->orderBy('sort', 'ASC')
            ->get();
    }

    /**
     * @throws Exception
     */
    public function addVisibilities(RewardExtern $rewardExtern, Collection $users)
    {
        $visibilities = $rewardExtern->visibilities();

        $users->each(function ($user) use ($visibilities) {
            $visibilities->attach($user, ['uuid' => Uuid::uuid4()]);
        });
    }

    public function getVisibleRewards(string $userId): Collection
    {
        return RewardExtern::query()
            ->with([
                'rewardedUsers' => function ($query) use ($userId) {
                    $query->where('user_id', $userId);
                },
                'visibilities' => function ($query) use ($userId) {
                    $query->where('user_id', $userId);
                },
            ])
            ->where('active', true)
            ->whereHas('visibilities', function ($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->get();
    }

    public function markAsCollected(RewardExtern $rewardExtern, object $user): void
    {
        UserExternReward::query()
            ->where('user_id', $user->id)
            ->where('reward_extern_id', $rewardExtern->id)
            ->update(['collected' => true, 'credits' => $rewardExtern->credits]);
    }

    public function findByUuid(string $uuid): RewardExtern
    {
        return RewardExtern::query()
            ->where('active', true)
            ->with([
                'visibilities' => function ($query) use ($uuid) {
                    $query->where('uuid', $uuid);
                },
            ])
            ->whereHas('visibilities', function ($query) use ($uuid) {
                $query->where('uuid', $uuid);
            })
            ->firstOrFail();
    }

    public function addClick(RewardExtern $reward): void
    {
        $reward->update(['clicked' => $reward->clicked + 1]);
    }

    public function grantReward(User $user, RewardExtern $reward, ?int $payout): bool
    {
        $exists = UserExternReward::query()
            ->where('user_id', $user->id)
            ->where('reward_extern_id', $reward->id)
            ->exists();

        if ($exists) {
            return false;
        }

        UserExternReward::create([
            'user_id' => $user->id,
            'reward_extern_id' => $reward->id,
            'collected' => false,
            'payout' => $payout,
        ]);

        return true;
    }

    public function addCap(RewardExtern $rewardExtern): int
    {
        $newCap = $rewardExtern->cap + 1;
        $rewardExtern->update(['cap' => $newCap]);

        return $newCap;
    }

    public function markAsInvisible(RewardExtern $reward): void
    {
        $reward->update(['visible' => false]);
    }

    public function resetInvisibleRewards()
    {
        RewardExtern::query()
            ->where('visible', false)
            ->update(['cap' => 0, 'visible' => true]);
    }

    public function getSignUpRewards(int $siteId): Collection
    {
        return RewardExtern::query()
            ->where('available_after_signup', true)
            ->where('active', true)
            ->where('site_id', $siteId)
            ->get();
    }

    public function addVisibility(RewardExtern $rewardExtern, User $user): void
    {
        $rewardExtern->visibilities()->attach($user);
    }
}
