<?php

namespace App\Repository;

use App\Models\Domain;

class BaseRepository
{
    protected ?Domain $domain;

    /**
     * Create a new repository instance.
     */
    public function __construct()
    {
        $this->domain = domain();
    }

    public function getDomain(): Domain
    {
        return $this->domain;
    }

    public function setDomain(?Domain $domain): BaseRepository
    {
        $this->domain = $domain;

        return $this;
    }
}
