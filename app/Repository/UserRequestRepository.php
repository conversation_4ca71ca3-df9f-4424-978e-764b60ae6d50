<?php

namespace App\Repository;

use App\Models\Profile;
use App\Models\User;
use App\Models\UserRequest;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class UserRequestRepository extends BaseRepository
{
    public function create(
        User $user,
        Profile $profile,
        string $message,
        bool $isAttachmentRequired,
        ?string $uploadId = null
    ): UserRequest {
        return UserRequest::create([
            'user_id' => $user->id,
            'profile_id' => $profile->id,
            'send_by_user' => true,
            'status' => 0,
            'message' => $message,
            'attachment_required' => $isAttachmentRequired,
            'upload_id' => $uploadId,
        ]);
    }

    public function open(User $user): Collection
    {
        return UserRequest::query()
            ->where('user_id', $user->id)
            ->where('status', 0)
            ->where('sent_by_user', false)
            ->orderBy('created_at', 'DESC')
            ->get();
    }

    public function pending(User $user): Collection
    {
        return UserRequest::query()
            ->where('user_id', $user->id)
            ->where('status', 0)
            ->where('sent_by_user', true)
            ->orderBy('created_at', 'DESC')
            ->get();
    }

    public function accept(UserRequest $userRequest)
    {
        $userRequest->update(['status' => 1]);
    }

    /**
     * @throws ModelNotFoundException
     */
    public function find(string $id): UserRequest
    {
        /** @var UserRequest $req */
        $req = UserRequest::query()->findOrFail($id);

        return $req;
    }

    public function deny(UserRequest $userRequest): void
    {
        $userRequest->update(['status' => -1]);
    }

    public function receivedHistory(User $user): Collection
    {
        return UserRequest::query()
            ->where('user_id', $user->id)
            ->where('status', '<>', 0)
            ->orderBy('created_at', 'DESC')
            ->get();
    }

    public function sentHistory(User $user): Collection
    {
        return UserRequest::query()
            ->where('user_id', $user->id)
            ->where('status', '<>', 0)
            ->orderBy('created_at', 'DESC')
            ->get();
    }
}
