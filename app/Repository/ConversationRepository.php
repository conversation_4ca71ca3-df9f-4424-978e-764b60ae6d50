<?php

namespace App\Repository;

use App\Exceptions\ModelException;
use App\Models\Conversations\ConversationFactory;
use App\Models\Conversations\ConversationModel;
use App\Models\Messages\MessageModel;
use App\Models\Profile;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Mirzacles\Helpers\Services\CacheKeyService;
use Mirzacles\Helpers\Services\CacheTagsService;

class ConversationRepository extends BaseRepository
{
    public function allPaginated(): LengthAwarePaginator
    {
        return ConversationFactory::generateConversationModel($this->domain->site_id)->paginate();
    }

    public function getConversations(User $user, string $type): Collection|LengthAwarePaginator
    {
        $page = request()->get('page', 1);
        if (!is_numeric($page) || $page < 1) {
            $page = 1;
        }
        $cacheKey = CacheKeyService::userConversationsPerPage($user, $type, $page);
        $cacheTTL = CacheKeyService::ttl('user_conversations_per_page');

        return Cache::tags(CacheTagsService::userConversations($user))->remember($cacheKey, $cacheTTL, function () use ($user, $type) {
            return $this->conversationQuery($user, $type)
                ->orderBy('last_sent_at', 'desc')
                ->paginate(domain()->items_per_page);
        });
    }

    public function findOrFail(string $id)
    {
        return ConversationFactory::generateConversationModel($this->domain->site_id)->findOrFail($id);
    }

    public function findConversationFromProfile(Profile $profile): ?ConversationModel
    {
        return ConversationFactory::generateConversationModel($profile->site_id)
            ->where('user_id', Auth::user()->id)
            ->where('profile_id', $profile->id)
            ->first();
    }

    public function get(User $user, Profile $profile): ?ConversationModel
    {
        return ConversationFactory::generateConversationModel($user->site_id)
            ->where('user_id', $user->id)
            ->where('profile_id', $profile->id)
            ->first();
    }

    public function archiveConversation(ConversationModel $conversationModel): void
    {
        $conversationModel->update([
            'is_archived' => true,
        ]);
    }

    public function unArchiveConversation(ConversationModel $conversationModel): void
    {
        $conversationModel->update([
            'is_archived' => false,
        ]);
    }

    public function conversationQuery(User $user, string $type)
    {
        return ConversationFactory::generateConversationModel($user->site_id)
            ->with([
                'profile',
                'last',
                'last.upload',
                'last.template',
            ])
            ->select([
                'id AS conversation_id',
                'user_id',
                'profile_id',
                'last_message_id',
                'last_sent_by_user',
                'last_sent_at',
                'from_user',
                'from_profile',
                'read',
                'is_archived',
                'created_at',
            ])
            ->where('user_id', $user->id)
            ->when($type == 'unread', function (Builder $builder) {
                $builder->where('from_profile', true)->where('read', false);
            })
            ->when($type == 'conversations', function (Builder $builder) {
                $builder->where('from_user', true)->where('from_profile', true);
            })
            ->when($type == 'favorites', function (Builder $builder) use ($user) {
                $builder->whereIn('profile_id', $user->favoriteProfileIds());
            })
            ->when($type == 'matches', function (Builder $builder) use ($user) {
                $matchIds = App::make(MatchesRepository::class)->matchIds($user);
                $builder->whereIn('profile_id', $matchIds);
            })
            ->when($type == 'archived', function (Builder $builder) {
                $builder->where('is_archived', true);
            })
            ->when(!in_array($type, ['archived', 'unread']) && $this->domain->hasModule('message_archive'), function (Builder $builder) {
                $builder->where('is_archived', false);
            });
    }

    public function hasActiveConversation(Profile $profile, User $user): bool
    {
        return ConversationFactory::generateConversationModel($user->site_id)
            ->where('user_id', $user->id)
            ->where('profile_id', $profile->id)
            ->where('from_profile', true)
            ->where('from_user', true)
            ->exists();
    }

    /**
     * @throws ModelException
     */
    public function getChatterId(MessageModel $message): ?string
    {
        $lastMessageWithChatterId = $message->conversation->messages()->whereNotNull('chatter_id')->latest()->first();

        if ($lastMessageWithChatterId instanceof MessageModel) {
            return $lastMessageWithChatterId->chatter_id;
        }

        return null;
    }

    public function unreadMessagesProfileIds(User $user): array
    {
        $cacheKey = CacheKeyService::unreadMessagesProfileIds($user);
        $cacheTTL = CacheKeyService::ttl('unread_messages_profile_ids');

        return Cache::remember($cacheKey, $cacheTTL, function () use ($user) {
            return ConversationFactory::generateConversationModel($user->site_id)
                ->select([
                    'profile_id',
                ])
                ->whereIn('profile_id', $user->favoriteProfileIds())
                ->where('read', false)
                ->pluck('profile_id')
                ->toArray();
        });
    }
}
