<?php

namespace App\Repository;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;
use Mirzacles\Models\City;
use Mirzacles\Models\Country;
use Mirzacles\Models\Region;

class GeoRepository extends BaseRepository
{
    public function getRegion(string $regionId): ?Region
    {
        /** @var Region $region */
        $region = Region::query()->find($regionId);

        return $region;
    }

    public function searchRegions(string $countryId, ?string $term)
    {
        if (!$term || strlen(trim($term)) < 3) {
            return false;
        }

        $term = Str::replace([' ', ','], '%', $term);

        return Region::query()->whereLike('name', '%' . $term . '%')
            ->where('country_id', $countryId)
            ->orderBy('name', 'desc')
            ->distinct()
            ->limit(20)
            ->get();
    }

    /**
     * @return Collection|bool
     */
    public function search(string $countryId, ?string $term, ?string $regionId)
    {
        if (!$term || strlen(trim($term)) < 3) {
            return false;
        }

        $term = Str::replace([' ', ','], '%', $term);

        return City::query()->with('region')
            ->where('search', 'LIKE', Str::title($term) . '%')
            ->when($regionId, function (Builder $query) use ($regionId) {
                $query->where('region_id', $regionId);
            })
            ->whereHas('region', function (Builder $query) use ($countryId) {
                $query->where('country_id', $countryId);
            })
            ->orderBy('priority', 'desc')
            ->orderBy('search')
            ->distinct()
            ->limit(20)
            ->get();
    }

    public function doesRegionExist(string $regionId): bool
    {
        return Region::query()->where('id', $regionId)->exists();
    }

    /**
     * @return string|bool
     */
    public function doesLocationMatch(string $location)
    {
        $locationArray = explode('-', $this->sanitizeString($location), 3);
        $locationArray = array_map('trim', $locationArray);
        $locationArray = array_map('ucfirst', $locationArray);

        if (count($locationArray) < 2) {
            return false;
        }

        $countryName = $this->translateCountryName($locationArray[0]);
        $regionName = $locationArray[1];
        $cityName = $locationArray[2] ?? false;

        $regionName = $regionName == 'Washington, D.C.' ? 'D.C.' : $regionName;
        $cityName = $cityName == 'Washington, D.C.' ? 'Washington' : $cityName;

        if (!$cityName && Str::contains($regionName, ',')) {
            $cityName = ucfirst(trim(explode(',', $regionName)[0]));
            $regionName = ucfirst(trim(explode(',', $regionName)[1]));
        }

        $locationId = false;
        $country = Country::query()->firstWhere(['name' => $countryName]);
        if (!$country) {
            return false;
        }

        if (isset($locationArray[1])) {
            $region = Region::query()->firstWhere(['name' => $regionName]);
            if ($region) {
                $locationId = $region->id;
            }
            if (!$region && strlen($regionName) == 2) {
                $region = Region::query()->firstWhere(['code' => strtoupper($regionName)]);
                if ($region) {
                    $locationId = $region->id;
                }
            }
            if (!$region && $cityName) {
                $region = Region::query()->firstWhere(['name' => $cityName]);
                if ($region) {
                    $locationId = $region->id;
                    $cityName = $regionName;
                }
            }
            if (!$region && strlen($cityName) == 2) {
                $region = Region::query()->firstWhere(['code' => $cityName]);
                if ($region) {
                    $locationId = $region->id;
                    $cityName = $regionName;
                }
            }
        }

        if ($cityName && $locationId) {
            $city = City::query()->firstWhere(['name' => $cityName, 'region_id' => $locationId]);
            if ($city) {
                return $city->id;
            }
        }

        return $locationId;
    }

    public function sanitizeString($location)
    {
        $location = str_replace(["\r", "\n"], '', $location);
        $location = preg_replace('/\s+/', ' ', $location);

        return $location;
    }

    public function translateCountryName($countryName)
    {
        $countryNames = [
            'Deutschland' => 'Germany',
            'Österreich' => 'Austria',
            'Osterreich' => 'Austria',
            'Schweiz' => 'Switzerland',
            'Italia' => 'Italy',
        ];

        return isset($countryNames[$countryName]) ? $countryNames[$countryName] : $countryName;
    }
}
