<?php

namespace App\Repository;

use Exception;
use Illuminate\Database\Eloquent\Builder;
use Mirzacles\Models\EverflowOffer;

class EverflowRepository extends BaseRepository
{
    public function createOffer(
        string $offerId,
        ?string $device,
        string $payoutType,
        string $entryName,
        string $label
    ): void {
        EverflowOffer::query()->create([
            'device' => $device,
            'payout_type' => $payoutType,
            'entry_name' => $entryName,
            'label' => $label,
            'offer_id' => $offerId,
        ]);
    }

    public function getOffer(string $offerId, ?string $device, string $payoutType, string $entryName, string $label): ?EverflowOffer
    {
        /** @var ?EverflowOffer $ef */
        $ef = EverflowOffer::query()
            ->where('offer_id', $offerId)
            ->where('device', $device)
            ->where('entry_name', $entryName)
            ->where('payout_type', $payoutType)
            ->where('label', $label)
            ->first();

        return $ef;
    }

    /**
     * @throws Exception
     */
    public function deleteOffer(string $id): void
    {
        EverflowOffer::query()->where('id', $id)->delete();
    }

    /**
     * @param  null  $payoutType
     */
    public function search(string $label, string $affiliateType, ?string $device, $payoutType = null): ?EverflowOffer
    {
        $query = EverflowOffer::query()
            ->where('entry_name', $affiliateType)
            ->where('label', $label)
            ->when(
                $payoutType,
                fn (Builder $q) => $q->where('payout_type', $payoutType)
            );

        if ($payoutType !== 'cps') {
            if ($affiliateType === 'Revshare') {
                $query->whereNull('device');
            } else {
                $query->where('device', $device);
            }
        }

        return $query->first();
    }
}
