<?php

namespace App\Repository;

use Illuminate\Support\Str;
use Mirzacles\Models\ShortUrl;
use Ramsey\Uuid\Uuid;

use function route;

class ShortUrlRepository
{
    public function createShortUrl(string $url): ?string
    {
        if (empty($url)) {
            return null;
        }

        $short = ShortUrl::query()->firstOrCreate([
            'url' => $url,
        ]);

        return Uuid::fromString($short->id)->getHex();
    }

    public function getFullUrl(string $shortUrl): bool|string
    {
        $urlParts = explode('/', $shortUrl);

        if (count($urlParts) < 2) {
            return false;
        }

        $url = ShortUrl::query()
            ->where('id', $urlParts[0])
            ->where('key', $urlParts[1])
            ->first();

        if ($url === null) {
            return false;
        }

        $url->update(['open' => true]);

        return $url->url;
    }

    public function getUrl(string $id): bool|string
    {
        if (Str::isValidUuid($id)) {
            $url = ShortUrl::find(Uuid::fromString($id)->toString());
            if ($url === null) {
                return false;
            }

            $url->update(['open' => true]);

            return $url->url;
        }

        return false;
    }

    public function createActivationUrl($domain, string $token, string $emailTrackingId): string
    {
        $queryString = [
            'utm_source' => 'email',
            'utm_medium' => 'email_button',
            'utm_campaign' => 'email_activation',
            'token' => $token,
            'et' => $emailTrackingId,
            'clicked' => 1,
        ];

        $activationUrl = $domain->full_url . route('activate', $queryString, false);

        return $domain->full_url . '/url/' . $this->createShortUrl($activationUrl);
    }
}
