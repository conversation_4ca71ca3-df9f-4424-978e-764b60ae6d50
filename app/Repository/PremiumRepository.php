<?php

namespace App\Repository;

use App\Models\ProductPremium;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Mirzacles\Helpers\Services\CacheKeyService;

class PremiumRepository extends BaseRepository
{
    public function getAvailableProducts(): Collection
    {
        $cacheKey = CacheKeyService::premiumProductsBySiteId($this->domain->site_id);
        $cacheTTL = CacheKeyService::ttl('premium_products_by_site_id');

        return Cache::remember($cacheKey, $cacheTTL, function () {
            return ProductPremium::query()
                ->where('active', true)
                ->where('site_id', $this->domain->site_id)
                ->orderBy('months')
                ->get();
        });
    }

    public function getBestSeller(): ?ProductPremium
    {
        $cacheKey = CacheKeyService::bestSellerProduct($this->domain->site_id, 'product_premium');
        $cacheTTL = CacheKeyService::ttl('best_seller_product');

        return Cache::remember($cacheKey, $cacheTTL, function () {
            return ProductPremium::query()
                ->where('site_id', $this->domain->site_id)
                ->where('active', true)
                ->where('best_seller', true)
                ->first();
        });
    }

    public function find(string $id): ProductPremium
    {
        $cacheKey = CacheKeyService::premiumProductWithId($id);
        $cacheTTL = CacheKeyService::ttl('premium_product_with_id');

        return Cache::remember($cacheKey, $cacheTTL, function () use ($id) {
            return ProductPremium::query()
                ->where('active', true)
                ->where('site_id', $this->domain->site_id)
                ->where('id', $id)
                ->firstOrFail();
        });
    }
}
