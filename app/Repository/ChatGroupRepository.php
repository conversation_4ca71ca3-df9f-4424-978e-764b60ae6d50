<?php

namespace App\Repository;

use App\Models\ChatGroup;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Cache;
use Mirzacles\Helpers\Services\CacheKeyService;

class ChatGroupRepository extends BaseRepository
{
    public function getPaginatedFemaleMembers(ChatGroup $chatGroup): LengthAwarePaginator
    {
        $cacheKey = CacheKeyService::femaleMembers($chatGroup);
        $cacheTTL = CacheKeyService::ttl('female_members');

        return Cache::remember($cacheKey, $cacheTTL, function () use ($chatGroup) {
            return $chatGroup->femaleMembers()
                ->with('profile.info')
                ->inRandomOrder()
                ->paginate(domain()->items_per_page);
        });
    }

    public function getPaginatedMaleMembers(ChatGroup $chatGroup): LengthAwarePaginator
    {
        $cacheKey = CacheKeyService::maleMembers($chatGroup);
        $cacheTTL = CacheKeyService::ttl('male_members');

        return Cache::remember($cacheKey, $cacheTTL, function () use ($chatGroup) {
            return $chatGroup->maleMembers()
                ->with('profile.info')
                ->inRandomOrder()
                ->paginate(domain()->items_per_page);
        });
    }

    public function all(): Collection
    {
        return ChatGroup::query()
            ->where('site_id', $this->domain->site_id)
            ->orderBy('total_members', 'DESC')
            ->get();
    }

    public function findOrFail(string $chatGroupId)
    {
        return ChatGroup::query()->findOrFail($chatGroupId);
    }

    public function join(string $groupId, User $user): ChatGroup
    {
        /** @var ChatGroup $chatGroup */
        $chatGroup = $this->findOrFail($groupId);

        $chatGroup->members()->create(['user_id' => $user->id]);

        return $chatGroup;
    }

    public function leave(ChatGroup $chatGroup): ChatGroup
    {
        /** @var User $user */
        $user = auth()->user();

        $user->groups()->detach($chatGroup->id);

        Cache::forget(CacheKeyService::userJoinedGroups($user));

        return $chatGroup;
    }

    /**
     * @throws ModelNotFoundException
     */
    public function get(string $id, User $user): ChatGroup
    {
        /** @var ChatGroup $group */
        $group = ChatGroup::query()
            ->where('site_id', $user->site_id)
            ->where('id', $id)
            ->firstOrFail();

        return $group;
    }

    public function getPosts(ChatGroup $chatGroup): LengthAwarePaginator
    {
        $cacheKey = CacheKeyService::chatGroupPosts($chatGroup);
        $cacheTTL = CacheKeyService::ttl('chat_group_posts');

        return Cache::remember($cacheKey, $cacheTTL, function () use ($chatGroup) {
            return $chatGroup->recentPosts()
                ->where(function (Builder $builder) {
                    $builder->whereNull('user_id')->orWhere('user_id', auth()->id());
                })
                ->where('created_at', '>=', Carbon::today()->subDays(90))
                ->orderBy('created_at', 'desc')
                ->paginate(domain()->items_per_page);
        });
    }

    public function myPosts(ChatGroup $chatGroup): LengthAwarePaginator
    {
        return $chatGroup->recentPosts()
            ->where('user_id', auth()->id())
            ->paginate(domain()->items_per_page);
    }
}
