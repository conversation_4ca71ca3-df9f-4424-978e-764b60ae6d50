<?php

namespace App\Repository;

use App\Models\Favorite;
use App\Models\Profile;
use App\Models\User;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Mirzacles\Helpers\Services\CacheKeyService;
use Mirzacles\Helpers\Services\CacheTagsService;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\SimpleCache\InvalidArgumentException;

class FavoriteRepository extends BaseRepository
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function all(): LengthAwarePaginator
    {
        /** @var User $user */
        $user = Auth::user();
        $page = request()->get('page', 1);
        $cacheKey = CacheKeyService::favoriteProfilesPerPage($user, $page);
        $cacheTTL = CacheKeyService::ttl('favorite_profiles_per_page');

        return Cache::tags([CacheTagsService::favoriteProfiles($user)])->remember($cacheKey, $cacheTTL,
            function () use ($user) {
                return $user->favoriteProfiles()
                    ->select(
                        [
                            'profiles.id',
                            'username',
                            'profile_image',
                            'favorites.created_at',
                            'favorites.user_action',
                            'favorites.profile_action',
                        ]
                    )
                    ->where('user_action', true)
                    ->where('status', true)
                    ->orderBy('favorites.created_at', 'desc')
                    ->with(['info'])
                    ->paginate($this->domain->items_per_page);
            }
        );
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function favoriteBack(): LengthAwarePaginator
    {
        /** @var User $user */
        $user = Auth::user();
        $page = request()->get('page', 1);
        $cacheKey = CacheKeyService::favoriteBackProfilesPerPage($user, $page);
        $cacheTTL = CacheKeyService::ttl('favorite_back_profiles_per_page');

        return Cache::tags([CacheTagsService::favoriteProfiles($user)])->remember($cacheKey, $cacheTTL,
            function () use ($user) {
                return $user->favoriteProfiles()
                    ->select([
                        'profiles.id',
                        'username',
                        'profile_image',
                        'favorites.created_at',
                        'favorites.user_action',
                        'favorites.profile_action',
                    ])
                    ->where('profile_action', true)
                    ->where('status', true)
                    ->orderBy('favorites.created_at', 'desc')
                    ->with(['info'])
                    ->paginate($this->domain->items_per_page);
            }
        );
    }

    public function exists(User $user, Profile $profile): bool
    {
        return Favorite::query()
            ->where('user_id', $user->id)
            ->where('profile_id', $profile->id)
            ->exists();
    }

    /**
     * @throws InvalidArgumentException
     */
    public function create(User $user, Profile $profile): void
    {
        $favorite = Favorite::query()
            ->firstOrNew(['user_id' => $user->id, 'profile_id' => $profile->id]);

        if (!$favorite->exists || !$favorite->user_action) {
            $favorite->fill([
                'site_id' => $user->site_id,
                'user_action' => true,
            ])->save();
        }

        $this->clearFavoritesCache($user);
    }

    /**
     * @throws Exception
     */
    public function delete(User $user, Profile $profile): bool
    {
        $favorite = Favorite::query()
            ->where('user_id', $user->id)
            ->where('profile_id', $profile->id)
            ->where('site_id', $user->site_id)
            ->first();

        if (!$favorite) {
            return false;
        }

        if ($favorite->profile_action) {
            $favorite->update(['user_action' => false]);
        } else {
            $favorite->delete();
        }

        $this->clearFavoritesCache($user);

        return true;
    }

    /**
     * @throws ModelNotFoundException
     */
    public function count(User $user): int
    {
        return Favorite::query()
            ->where('user_id', $user->id)
            ->count();
    }

    public function clearFavoritesCache(User $user): void
    {
        Cache::forget(CacheKeyService::favoriteProfileIds($user));
        Cache::tags([CacheTagsService::favoriteProfiles($user)])->flush();
    }
}
