<?php

namespace App\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Mirzacles\Models\EmailsTracking;

class EmailsTrackingJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public const QUEUE = 'emails-stat-tracking';

    private array $requestQuery;

    private ?string $userId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $requestQuery, ?string $userId)
    {
        $this->requestQuery = $requestQuery;
        $this->userId = $userId;
        $this->onQueue(static::QUEUE);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $emailTracking = EmailsTracking::query()->find($this->requestQuery['et'] ?? null);
            if ($emailTracking && $this->isUserNeed($emailTracking)) {
                /** @var EmailsTracking $emailTracking */
                $data = $this->getTrackingData($emailTracking);
                if (count($data)) {
                    $emailTracking->update($this->prepareData($data));
                }
            }
        } catch (\Exception $e) {
            Log::error('EmailsTrackingJob', ['message' => $e->getMessage(), 'requestQuery' => $this->requestQuery, 'userId' => $this->userId]);
        }
    }

    /**
     * Get tracking data from request query
     */
    private function getTrackingData(EmailsTracking $emailTracking): array
    {
        $data = [];
        // get fields for update by query params
        foreach ($this->requestQuery as $k => $v) {
            if (Arr::exists(EmailsTracking::ACTIONS, $k) && $emailTracking->{EmailsTracking::ACTIONS[$k]} == null) {
                $data[EmailsTracking::ACTIONS[$k]] = $v;
            }
        }

        return $data;
    }

    /**
     * Prepare data formats
     */
    private function prepareData(array $data): array
    {
        $prepared = [];
        foreach ($data as $k => $v) {
            switch ($k) {
                case 'opened_at':
                case 'clicked_at':
                case 'unsubscribed_at':
                    $prepared[$k] = Carbon::now();

                    break;
                    // TODO: - implement other fields
                default:
                    $prepared[$k] = $v;

                    break;
            }
        }

        return $prepared;
    }

    protected function isUserNeed($emailTracking): bool
    {
        return !$this->userId || $this->userId == $emailTracking->user_id;
    }
}
