<?php

namespace App\Jobs;

use App\Models\Profile;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class TouchProfileJob implements ShouldQueue
{
    use Dispatchable;
    use Queueable;

    public $tries = 3;

    public function __construct(private readonly Profile $profile)
    {
        $this->queue = 'profile_touch';
    }

    /**
     * @throws \Throwable
     */
    public function handle(): void
    {
        $this->profile->setAttribute('online', true)->touch();
    }
}
