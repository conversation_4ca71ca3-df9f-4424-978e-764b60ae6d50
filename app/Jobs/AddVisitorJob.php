<?php

namespace App\Jobs;

use App\Models\User;
use App\Models\Visitor;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Mirzacles\Helpers\Services\CacheKeyService;

class AddVisitorJob implements ShouldBeUnique, ShouldQueue
{
    use InteractsWithQueue;
    use Queueable;

    public $tries = 3;

    public function __construct(
        private readonly string $profileId,
        private readonly string $userId,
        private readonly string $timestamp
    ) {
        $this->queue = 'main_commands_queue';
    }

    public function handle(): void
    {
        $exists = Visitor::query()
            ->where('user_id', $this->userId)
            ->where('profile_id', $this->profileId)
            ->exists();

        if ($exists) {
            return;
        }

        Visitor::query()->create([
            'user_id' => $this->userId,
            'profile_id' => $this->profileId,
            'read' => false,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $user = User::find($this->userId);

        $cacheKey = CacheKeyService::newVisitorsCount($user);
        cache()->forget($cacheKey);
    }

    public function uniqueId(): string
    {
        return sprintf('unique_visitor_%s-%s', $this->profileId, $this->userId);
    }
}
