<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Mirzacles\Models\TrackRecord;

class SaveTrackRecord implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public $tries = 1;

    public string $userId;

    public string $event;

    public function __construct(string $userId, string $event)
    {
        $this->userId = $userId;
        $this->event = $event;
    }

    /**
     * Execute the job.
     *
     * @throws \Throwable
     */
    public function handle(): void
    {
        $attributes = [
            'user_id' => $this->userId,
            'event' => $this->event,
        ];

        TrackRecord::query()
            ->updateOrCreate($attributes)
            ->increment('count');
    }
}
