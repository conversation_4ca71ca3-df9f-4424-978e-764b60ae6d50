<?php

namespace App\Jobs;

use App\Models\Order;
use App\Models\User;
use App\Repository\RewardInternRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Mirzacles\Models\RewardIntern;

class GrantRewardToUserJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private User $user;

    private string $routeName;

    private ?Order $order;

    public $tries = 3;

    public function __construct(
        User $user,
        string $routeName,
        ?Order $order
    ) {
        $this->user = $user->withoutRelations();
        $this->routeName = $routeName;
        $this->order = ($order !== null) ? $order->withoutRelations() : null;
    }

    /**
     * @param  $job
     * @param  $data
     */
    public function handle(RewardInternRepository $rewardInternRepository): void
    {
        $data = [
            'user' => $this->user,
            'order' => $this->order,
            'route_name' => $this->routeName,
        ];

        $rewardInternRepository->getByRouteName($this->routeName, $this->user->site_id)
            ->each(function (RewardIntern $reward) use ($rewardInternRepository, $data) {
                if ($rewardInternRepository->userAlreadyHaveReward($this->user, $reward)) {
                    return;
                }

                /** @var string $criteriaClass */
                $criteriaClass = $reward->criteriaClass->class;

                if (!class_exists($criteriaClass)) {
                    Log::error('Could not find criteria class', ['reward_id' => $reward->id, 'class' => $criteriaClass]);

                    return;
                }

                $rewardData = $data;
                $rewardData['reward'] = $reward;

                if ($criteriaClass::isGrantedReward($rewardData)) {
                    if ($reward->required_rewards !== null) {
                        $required_rewards = $reward->getRequiredRewards();

                        if ($rewardInternRepository->isPreviousRewardsGranted($this->user, $required_rewards)) {
                            Log::info('inside previous reward granted');
                            $rewardInternRepository->grantReward($this->user, $reward, true);

                            return;
                        }

                        $rewardInternRepository->grantReward($this->user, $reward, false);

                        return;
                    }

                    $rewardInternRepository->grantReward($this->user, $reward);
                }
            });

        $this->delete();
    }
}
