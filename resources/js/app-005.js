import { processUploads } from "./helpers";

$(document).ready(function () {

    // resize textarea while typing
    $('.autoresizing').on('input', function () {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    /* hide and show mobile header */
    $(window).scroll(function() {
        if ($(window).scrollTop() < 150) {
            $('.main-header').addClass('d-none d-lg-block');
            $('.main-header').removeClass('d-block');
        }
        else{
            $('.main-header').removeClass('d-none d-lg-block');
            $('.main-header').addClass('d-block');
        }
        if ($(window).scrollTop() < 150) {
            $('.mobile-filter-name').addClass('d-none');
            $('.mobile-filter-icon').addClass('d-none');
            $('.mobile-filter-logo').removeClass('d-none');
        }
        else{
            $('.mobile-filter-name').removeClass('d-none');
            $('.mobile-filter-icon').removeClass('d-none');
            $('.mobile-filter-logo').addClass('d-none');
        }
    });

    /* hide and show show more button in about me when resizing window */
    $(window).resize(function() {
        // Handle show more/less behavior
        if ($('#profile-about').length > 0) {
            if ($(".about").height() > $("#profile-about").height()) {
                $("#profile-about").addClass('show-more');
                $(".show-more-button").removeClass('d-none');
            }
            else{
                $("#profile-about").removeClass('show-more');
                $(".show-more-button").addClass('d-none');
            }
        }
    });

    /* scroll to top when clicking filter button */
    $(document).on("click", "#mobile-filter-button", function () {
        $("html,body").animate({scrollTop:0}, 500);
		e.preventDefault();
    });

    // Prevent context menu
    document.addEventListener("contextmenu", (event) => event.preventDefault());

    // Prevent images from being dragged
    $("img").attr("draggable", false);

    // Select preferences upon registering
    var current_fs, next_fs, previous_fs;
    var left, opacity, scale;
    var animating;

    $(".page-preferences .next").click(function () {
        current_fs = $(this).parents('fieldset');
        next_fs = $(this).parents('fieldset').next();
        $("#progressbar li")
            .eq($("fieldset").index(next_fs))
            .addClass("active");
        next_fs.show();
        current_fs.hide();
    });

    $(".page-preferences .previous").click(function () {
        current_fs = $(this).parents('fieldset');
        previous_fs = $(this).parents('fieldset').prev();
        $("#progressbar li")
            .eq($("fieldset").index(current_fs))
            .removeClass("active");

        previous_fs.show();
        current_fs.hide();
    });

    // Prevent dragging of images
    document.querySelector("img").addEventListener("dragstart", (event) => {
        event.preventDefault();
    });

    // Initialize select2 elements
    $(".dropdown-select").select2({
        language: momentLang,
        width: "resolve",
        minimumResultsForSearch: -1,
    });

    $(".dropdown-select-search").select2({
        language: momentLang,
        width: "resolve",
        dropdownParent: $("#filterModal")
    });

    $("#payaccept").modal("show");
    $(".pay-accept .btn-close").click(function () {
        $("#payaccept").modal("hide");
    });

    $("#payfaild").modal("show");
    $(".pay-model .btn-close").click(function () {
        $("#payfaild").modal("hide");
    });

    // OFI Browser
    objectFitImages();

    // Age slider
    var getValues = function () {
        var values = ageSlider.getValue();
        let minValue = values[0];
        let maxValue = values[1];

        $(".selected-age").html(minValue + "-" + maxValue);
        $("#age-min").val(minValue);
        $("#age-max").val(maxValue);
    };

    var ageSlider = $("#age-slider")
        .slider({
            min: 18,
            max: 99,
            step: 1,
            value: [18, 99],
            tooltip: "hide",
        })
        .on("slide slideStop", getValues)
        .data("slider");

    $("#age-slider").removeClass("d-none");
    if ($("#age-slider").length > 0) {
        ageSlider.setValue([$("#age-min").val() * 1, $("#age-max").val() * 1]);
    }

    // Toggle collapse for filter accordion
    function showAccordion () {
        if ($('#headingFilter').is(":visible")) {
            $('#accordionBody').addClass('collapse');
        } else {
            $('#accordionBody').removeClass('collapse');
        }
    }

    showAccordion();

    $(window).on('resize', function () {
        showAccordion();
    });

    // Toggle switches in registration form
    $(document).on("click", ".gender, .seek", function () {
        $($(this).data("bs-target")).val($(this).data("option"));
    });

    // Recaptcha
    $(document).on("submit", ".recaptcha-form", function (event) {
        event.preventDefault();
        var form = event.target;
        grecaptcha.ready(function () {
            grecaptcha
                .execute(recaptchaKey, { action: "submit" })
                .then(function (token) {
                    $(form).prepend(
                        $(document.createElement("input"))
                            .prop("type", "hidden")
                            .prop("name", "g-recaptcha-response")
                            .addClass("g-recaptcha-response")
                            .val(token)
                    );

                    event.currentTarget.submit();
                });
        });
    });

    // Login form
    $(document).on("submit", ".ajax-form", function (event) {
        event.preventDefault();
        var submitButton = $(this).find('button[type="submit"]');
        $(submitButton).addClass("loading");
        $.ajax({
            type: $(this).data("method"),
            url: $(this).attr("action"),
            data: $(this).serialize(),
            success: function (data) {
                toastr.success(data.message);
                if (data.redirect) {
                    location.href = data.redirect;
                }
            },
            error: function (data) {
                $(submitButton).removeClass("loading");
                toastr.error(data.responseJSON.message);
            },
        });
    });

    $(document).on('click', '#login-button', function (ev) {
        if ($(this).hasClass("loading")) {
            ev.preventDefault();
            return false;
        }
    });

    // Add and remove favorite
    $(document).on("click", ".favorite", function (ev) {
        ev.preventDefault();
        if (favoriteInProgress) {
            return false;
        }
        var elem = $(this);
        $(elem).addClass("loading");
        var url = $(elem).hasClass("is-favorite")
            ? $(elem).data("remove")
            : $(elem).data("add");

        favoriteInProgress = true;
        $.ajax({
            type: "GET",
            url: url,
            beforeSend: function () {
                if ($("#chatPageModal")) {
                    $("#chatPageModal").modal("hide");
                }
            },
            success: function (data) {
                $(elem).toggleClass("add-favorite is-favorite");
                $(elem).parents('.profile-card').toggleClass("favorite-profile");
                $(elem).removeClass("loading");
                favoriteInProgress = false;
                toastr.success(data.message);
            },
            error: function (data) {
                toastr.error(data.responseJSON.message);
            },
        });
    });

    // Tribes
    $(document).on("click", "#all-tribes", function () {
        $(".tribe").removeClass("d-none");
        $(".content-tabs li a").removeClass("active");
        $("#not-joined").addClass('d-none');
        $(this).addClass("active");
    });

    $(document).on("click", "#my-tribes", function () {
        $(".tribe-not-joined").addClass("d-none");
        $(".content-tabs li a").removeClass("active");
        if ($(".tribe-joined").length == 0) {
            $("#not-joined").removeClass("d-none");
        }
        $(this).addClass("active");
    });

    $(document).on("click", ".join-tribe", function (ev) {
        ev.preventDefault();
        $.ajax({
            url: $(this).prop("href"),
            dataType: "json",
            type: "POST",
            success: function (data, textStatus, jQxhr) {
                window.location.reload();
            },
            error: function (jqXhr, textStatus, errorThrown) { },
        });
    });

    $(document).on("click", ".leave-tribe", function (ev) {
        ev.preventDefault();
        $.ajax({
            url: $(this).prop("href"),
            dataType: "json",
            type: "POST",
            success: function (data, textStatus, jQxhr) {
                window.location.reload();
            },
            error: function (jqXhr, textStatus, errorThrown) { },
        });
    });

    $(document).on("click", ".like-post", function (ev) {
        ev.preventDefault();
        var likeButton = $(this);
        $.ajax({
            url: $(this).data("likeurl"),
            dataType: "json",
            type: "POST",
            success: function (data, textStatus, jQxhr) {
                likeButton.toggleClass("like-post unlike-post");
                likeButton.find(".like-counter").html(data.count);
            },
            error: function (jqXhr, textStatus, errorThrown) { },
        });
    });

    $(document).on("click", ".unlike-post", function (ev) {
        ev.preventDefault();
        var likeButton = $(this);
        $.ajax({
            url: $(this).data("unlikeurl"),
            dataType: "json",
            type: "DELETE",
            success: function (data, textStatus, jQxhr) {
                likeButton.toggleClass("like-post unlike-post");
                likeButton.find(".like-counter").html(data.count);
            },
            error: function (jqXhr, textStatus, errorThrown) { },
        });
    });

    // Magnific popup
    $(".magnific-ajax").magnificPopup({
        type: "ajax",
        tClose: magnificClose,
        tLoading: '<i class="fas fa-circle-notch fa-spin fa-2x"></i>',
        gallery: {
            enabled: true,
            tPrev: magnificPrevious,
            tNext: magnificNext,
            tCounter: magnificCounter,
        },
        ajax: {
            tError: magnificAjaxError,
        },
        callbacks: {
            buildControls: function () {
                this.contentContainer.append(this.arrowLeft);
                this.contentContainer.append(this.arrowRight);
            },
        },
    });

    $(".magnific-tribes").magnificPopup({
        type: "image",
        tClose: magnificClose,
        tLoading: '<i class="fas fa-circle-notch fa-spin fa-2x"></i>',
    });

    const actionBar = document.getElementById('action-bar');
    if (actionBar) {
        const selectAllText = actionBar.dataset.selectAll;
        const deselectAllText = actionBar.dataset.deselectAll;
        const deleteText = actionBar.dataset.delete;
        // Message list actions
        // Select All or Deselect All functionality
        $(document).on("click", "#select-all-conversations", function () {
            const allCheckboxes = $(".select-message");
            const allSelected = $(".select-message:checked").length === allCheckboxes.length;

            if (!allSelected) {
                allCheckboxes.prop("checked", true);
                $("#select-all-conversations").text(deselectAllText);
            } else {
                allCheckboxes.prop("checked", false);
                $("#select-all-conversations").text(selectAllText);
            }
            updateActionBar();
        });

        // Individual checkbox click functionality
        $(document).on("click", ".select-message", function () {
            updateActionBar();
        });

        // Delete conversations
        $(document).on("click", "#delete-conversations", function (ev) {
            ev.preventDefault();
            const totalSelected = $(".select-message:checked").length;

            if (totalSelected === 0) {
                $("#conversations").val("");
                return false;
            }

            const convos = [];
            let username = "";

            $(".select-message").each(function () {
                if ($(this).prop("checked")) {
                    convos.push($(this).val());
                    username = $(this).data("username");
                }
            });

            $("#conversations").val(convos.join(","));

            if (convos.length > 0) {
                $(".remove-conversation-modal .delete-items").addClass("d-none");
                if (totalSelected === 1) {
                    $(".remove-conversation-modal .delete-one").removeClass("d-none");
                } else {
                    $(".remove-conversation-modal .item-count").html(totalSelected);
                    $(".remove-conversation-modal .delete-many").removeClass("d-none");
                }
                $(".remove-conversation-modal").modal("show");
            }
        });

        // Clear selections
        $(document).on("click", ".keep-conversation", function () {
            $(".select-message").prop("checked", false);
            updateActionBar();
        });

        // Submit form to remove conversations
        $(document).on("click", ".remove-conversation", function () {
            $("#form-delete-conversations").submit();
        });

        // Function to update the action bar
        function updateActionBar() {
            const selectedCount = $(".select-message:checked").length;
            const totalCheckboxes = $(".select-message").length;

            if (selectedCount > 0) {
                $("#delete-conversations").text(`${deleteText} (${selectedCount})`);
                $("#action-bar").removeClass("d-none");
            } else {
                $("#delete-conversations").text(deleteText);
                $("#action-bar").addClass("d-none");
            }

            const allSelected = selectedCount === totalCheckboxes;
            $("#select-all-conversations").text(allSelected ? deselectAllText : selectAllText);
        }
    }

    // Submit form to remove conversations
    $(document).on("click", ".remove-conversation", function () {
        console.log('Click!');
        $("#form-delete-conversations").submit();
    });

    // Archive popup
    $(document).on("show.bs.modal", "#message-archive", function (e) {
        var invoker = $(e.relatedTarget);
        var archiveType = invoker.data("archived");
        var conversationId = invoker.data("conversation");
        $("#conversation-id").val(conversationId);
        $("button[data-archived=" + archiveType + "]").prop("disabled", true);
    });

    // Contact form
    $(document).on("click", ".show-phone", function (ev) {
        ev.preventDefault();
        $(".phone-number, .show-phone").toggleClass("d-none");
    });

    // Upload handling
    $(document).on("click", ".select-upload", function (ev) {
        ev.preventDefault();
        ev.stopPropagation();
        if ($(this).hasClass('loading')) {
            return false;
        }
        var parentForm = $(this).parents("form");
        var fileInput = $(parentForm).find(".file-upload-field");
        $(fileInput).trigger("click");
    });

    // Preview upload
    $(document).on("change", ".file-upload-field", async function (ev) {
        ev.target.files = await processUploads(ev.target.files);
        var parentForm = $(this).parents("form");
        var selectUploadButton = $(parentForm).find(".select-upload");
        if ($(this).hasClass("upload-instantly")) {
            $(selectUploadButton).addClass("loading");
            $(parentForm).submit();
        } else {
            if (typeof FileReader != "undefined") {
                var reader = new FileReader();
                reader.onload = function (e) {
                    $(parentForm)
                        .find(".photo-preview img")
                        .attr("src", e.target.result);
                    $(parentForm).find(".photo-preview").removeClass("d-none");
                    $(parentForm).find(".select-upload").addClass("d-none");
                };
                reader.readAsDataURL($(this)[0].files[0]);
            }
        }
    });

    // Remove upload from form
    $(".remove-attachment").on("click", function (ev) {
        ev.preventDefault();
        var parentForm = $(this).parents("form");
        var messageBox = $(parentForm).find("#message");
        var fileInput = $(parentForm).find('input[type="file"]');

        $(fileInput).val("");
        $(parentForm).find(".photo-preview").addClass("d-none");
        $(parentForm).find(".select-upload").removeClass("d-none");
    });

    $(document).on("click", ".disabled", function (ev) {
        ev.preventDefault();
        return false;
    });

    $(document).on("click", ".no-credits", function (ev) {
        ev.preventDefault();
        if ($("#noCreditsPopup").length > 0) {
            $("#noCreditsPopup").modal("show");
        }
        if ($("#store").length > 0) {
            $("#store").modal("show");
            $('.outofcredits').removeClass('d-none');
        }
        return false;
    });

    $(document).on("click", ".no-archive", function (ev) {
        ev.preventDefault();
        $(".enable-archive").modal("show");
        return false;
    });

    // Terms and privacy modal
    $(document).on("click", ".terms-modal, .privacy-modal", function (ev) {
        ev.preventDefault();
        if ($(this).hasClass("terms-modal")) {
            $(".cms-terms").modal("show");
        }
        if ($(this).hasClass("privacy-modal")) {
            $(".cms-privacy").modal("show");
        }
    });

    // City autocomplete
    $(document).on("keyup", "#cityAutocomplete", function (e) {
        const container = $(".popup-add-city #results-container");
        const list = $(".popup-add-city ul.results");
        const term = $("#cityAutocomplete").val();

        if (term.length <= 2) {
            // Clear Suggested Cities
            $("#results-container").addClass("d-none");
            container.animate({ height: 0 }, 150);
            return;
        }

        // Debounce: to prevent multiple calls to the server
        clearTimeout(cityAutocompleteTimeout);
        cityAutocompleteTimeout = setTimeout(function () {
            $.ajax({
                url: "/cityAutocomplete",
                dataType: "json",
                data: {
                    term,
                },
                success: function (data) {
                    $("#results-container .results").empty();

                    if (data.length == 0) {
                        $('#cityAutocomplete').addClass('text-danger bg-light');
                        $("#results-container").addClass("d-none");
                        container.animate({ height: 0 }, 150);
                        return;
                    }

                    $("#cityAutocomplete").removeClass("text-danger bg-light");

                    $("#results-container").removeClass("d-none");
                    data.forEach(function (item) {
                        let result =
                            item.priority == 1
                                ? `<strong>${item.city}, ${item.region}</strong>`
                                : `${item.city}, ${item.region}`;
                        const opt = `<li data-value="${item.city_id}"><span class="city">${result}</span></li>`;
                        $("#results-container .results")[0].innerHTML +=
                            opt;
                    });

                    $("#results-container .results li").click(
                        function () {
                            $("#cityAutocomplete").val(
                                $(this).find(".city").text()
                            );
                            $("#cityInput").val(
                                $(this).attr("data-value")
                            );
                            $("#results-container").addClass("d-none");
                            container.animate({ height: 0 }, 150);
                        }
                    );

                    let listMaxHeight = getComputedStyle(
                        document.documentElement
                    ).getPropertyValue("--locations-list-height");
                    listMaxHeight =
                        list.height() > 200
                            ? `${listMaxHeight.trim()}`
                            : list.height();

                    container.animate(
                        {
                            height: listMaxHeight,
                        },
                        150
                    );
                },
            });
        }, 400);
    });


    // Submit message
    $(".send-message").on("click", function (ev) {
        if ($(this).hasClass("disabled") || $(this).hasClass("loading")) {
            ev.preventDefault();
            return false;
        }
    });

    $(document).on('submit', '.message-form', function (ev) {
        var messageButton = $(this).find('.send-message');
        $(messageButton).addClass('loading');
    });

    function updateMessageTimestamps () {
        $(".message-box").each(function () {
            let duration = Date.now() - ($(this).data("humanize") * 1);
            let parts = 2;
            if (duration <= 0) {
                duration = 1000;
            }
            if (duration <= 1000 * 60 * 60) {
                parts = 1;
            }
            if (duration <= 1000) {
                $(this).find(".humantime").html(justNow);
                return;
            }
            let humanized = humanizeDuration(duration, {
                language: momentLang,
                largest: parts,
                round: true
            });
            var timeFormatted = appLocale == 'de-DE' ? timeAgo + ' ' + humanized : humanized + timeAgo;
            $(this)
                .find(".humantime")
                .html(timeFormatted);
        });
    }

    function scrollTop (animate = true) {
        var height = $('#messages').innerHeight();
        var scrollHeight = $('#messages').prop('scrollHeight');

        if (height >= scrollHeight) {
            return false;
        }

        if (animate == true) {
            $('#messages').animate({ scrollTop: $('#messages').prop("scrollHeight") }, 1000);
        } else {
            $('#messages').scrollTop($('#messages').prop("scrollHeight"));
        }
        $('.showlatest-box').addClass('d-none');
    }

    // Start Conversation
    if ($("#chat-form").length > 0) {

        function chatMagnificPopup () {

            console.log('Initializing Magnific Popup');
            $(".magnific-user").magnificPopup({
                type: "ajax",
                tClose: magnificClose,
                tLoading:
                    '<i class="fas fa-circle-notch fa-spin fa-2x"></i>',
                gallery: {
                    enabled: true,
                    tPrev: magnificPrevious,
                    tNext: magnificNext,
                    tCounter: magnificCounter,
                },
                callbacks: {
                    buildControls: function () {
                        this.contentContainer.append(this.arrowLeft);
                        this.contentContainer.append(this.arrowRight);
                    },
                },
            });

            $(".magnific-profile").magnificPopup({
                type: "ajax",
                tClose: magnificClose,
                tLoading:
                    '<i class="fas fa-circle-notch fa-spin fa-2x"></i>',
                gallery: {
                    enabled: true,
                    tPrev: magnificPrevious,
                    tNext: magnificNext,
                    tCounter: magnificCounter,
                },
                callbacks: {
                    buildControls: function () {
                        this.contentContainer.append(this.arrowLeft);
                        this.contentContainer.append(this.arrowRight);
                    },
                },
            });
        }

        updateMessageTimestamps();
        scrollTop(false);
        chatMagnificPopup();
        var userExpectsMessage = false;

        var isTabActive = true;

        document.addEventListener('visibilitychange', function (event) {
            if (document.hidden) {
                isTabActive = false;
            } else {
                isTabActive = true;
            }
        });

        $("#chat-form").on("submit", function (ev, elem) {
            ev.preventDefault();

            if (
                $("#send-message").hasClass("disabled") ||
                $("#send-message").hasClass("loading")
            ) {
                return false;
            }
            if ($("#send-message").hasClass("no-credits")) {
                $("#noCreditsPopup").modal("show");
                return false;
            }
            $($("#send-message")).addClass("loading");

            var chatForm = $("#chat-form")[0];

            // $("#send-message .fas").toggleClass("d-none");
            var formData = new FormData(chatForm);
            var postUrl = $("#chat-form").attr("action");

            //$("#last_message").val($(".message-box").last().data("created"));

            $.ajax({
                url: postUrl,
                dataType: "json",
                type: "POST",
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data, textStatus, jQxhr) {
                    if (data.status) {
                        $("#message-box").append(data.messages);
                        updateMessageTimestamps();
                        chatMagnificPopup();
                        $("#chat-form").trigger("reset");
                        if ($('#sendWithEnter').length && localStorage.getItem('sendWithEnter')) {
                            $('#sendWithEnter').prop('checked', true);
                        }
                        $(chatForm).find(".photo-preview").addClass("d-none");
                        $(chatForm).find(".photo-preview img").prop("src", "");
                        scrollTop();
                        $(".credit-amount").html(data.credits);
                        var profile_id = $("#profile_id").val();
                        sessionStorage.removeItem("message_to_" + profile_id);

                        userExpectsMessage = true;
                        if (data.message) {
                            toastr.success(data.message);
                        }

                        if ($('.messageEncrypt').length) {
                            $('.messageEncrypt').addClass('d-none');
                        }
                        $(".select-upload").removeClass("d-none");
                        $('.autoresizing').height("auto");
                    } else {
                        if (data.nocredits) {
                            $("#send-message").addClass("no-credits");
                            $("#noCreditsPopup").modal("show");
                        } else {
                            toastr.error(data.message);
                        }
                    }
                    if (data.enableBtn) {
                        $("#send-message").removeClass("disabled loading");
                    }

                    $("#send-message .fas").toggleClass("d-none");
                },
                error: function (jqXhr, textStatus, errorThrown) {
                    $("#send-message .fas").toggleClass("d-none");
                    $("#send-message").removeClass("disabled");

                    $(".message-error").fadeTo(500, 1);
                    setTimeout(function () {
                        $(".message-error").fadeOut();
                    }, 3000);
                    return false;
                },
            });
        });

        function initScroll () {
            scrollTop(false);
        }

        $('#messages').scroll(function () {
            var messageWindow = $(this);
            var height = messageWindow.innerHeight();
            var scrollHeight = messageWindow.prop('scrollHeight');

            if (height >= scrollHeight || $('.message').length < 2) {
                return false;
            }

            var distanceFromBottom = Math.floor(scrollHeight - (messageWindow.scrollTop() + height));
            if (distanceFromBottom < 2) {
                $('.showlatest-box').addClass('d-none');
            } else {
                $('.showlatest-box').removeClass('d-none');
            }
        });

        $('.showlatest').on('click', function () {
            $('.showlatest-box').addClass('d-none');
            scrollTop();
        });

        $(".showmore").on("click", function (ev) {
            if ($(".showmore").hasClass("loading")) {
                return false;
            }
            $(".showmore").addClass("loading");

            $.ajax({
                url: showMoreMessagesRoute,
                dataType: "json",
                type: "POST",
                data: {
                    offset: $(".message").length,
                    profileId: $("#profile_id").val(),
                },
                success: function (data) {
                    if (data.status) {
                        $("#message-box").prepend(data.messages);
                        updateMessageTimestamps();
                        chatMagnificPopup();
                        if (!data.showMore) {
                            $(".showmore").addClass("d-none");
                        }
                    }

                    $(".showmore").removeClass("loading");
                },
                error: function (jqXhr, textStatus, errorThrown) { },
            });
        });

        var lastUpdate = parseInt(Date.now() / 1000);

        const checkMessages = setInterval(() => {
            if ($("#member-is-typing").hasClass("d-none") && lastUpdate !== null) {
                $.ajax({
                    url: checkMessageRoute,
                    dataType: "json",
                    type: "POST",
                    data: {
                        lastUpdate: lastUpdate,
                        profileId: $("#profile_id").val(),
                    },
                    success: function (data) {
                        if (data.status) {
                            lastUpdate = data.lastUpdate;

                            if (data.messages.length > 0) {
                                data.messages.forEach(function (message, idx) {
                                    $("#member-is-typing").removeClass("d-none");
                                    setTimeout(() => {
                                        $("#member-is-typing").addClass("d-none");
                                        $("#message-box").append(message.content);
                                        updateMessageTimestamps();
                                        userExpectsMessage = false;
                                        scrollTop();
                                    }, message.wait);
                                });
                            }
                        }
                    },
                    error: function (x, status, error) {
                        toastr.error(x.responseJSON.message, "", {
                            timeOut: 0,
                            extendedTimeOut: 0,
                            closeButton: true,
                        });
                        clearInterval(checkMessages);
                    },
                });
            }
        }, 10000);

        setInterval(() => {
            updateMessageTimestamps();
        }, 30000);

        // Save messages
        $('#message').on('keyup paste change', function (ev, elem) {
            let message = $('#message').val();
            var profile_id = $('#profile_id').val();
            sessionStorage.setItem('message_to_' + profile_id, $('#message').val());
        });

        // Show saved message
        $(document).ready(function () {
            initScroll();
            var profile_id = $('#profile_id').val();
            if (sessionStorage.getItem('message_to_' + profile_id) !== null) {
                $('#message').val(sessionStorage.getItem('message_to_' + profile_id));
            }
        });
    }
    // End Conversation

    // Preference number check
    $(document).on('click', '.preferences .btn, .preferences .btn-check', function (ev) {
        var submitButton = $(this).parents('form').find('button[type="submit"]');
        if ($('.preferences input:checkbox:checked').length < 4) {
            $(submitButton).addClass('disabled');
        } else {
            $(submitButton).removeClass("disabled");
        }
    });

    // Signup and login buttons
    $(document).on('submit', '#signup-form', function () {
        $('#signup-button').addClass('loading');
    });

    $(document).on("click", "#signup-button", function (ev) {
        if ($(this).hasClass('loading')) {
            ev.preventDefault();
            return false;
        }
    });


    // Extend HTML browser basic email validation with @/. validation
    $(document).ready(function () {
        // Attach an event listener to the email input field with the type=email
        $('input[type="email"]').on("input", function () {
            var email = $(this).val();
            var strictEmailRegex = /^[A-Za-z0-9._%+-]+@(?:[A-Za-z0-9-]+\.)+[A-Za-z]{2,}$/;

            // It uses a gobal message if defined, otherwise the default
            var errorMsg = emailValidationMessage;
            var $input = $(this);

            // Try to retrieve an associated error container from this input's data cache.
            var $errorDiv = $input.data('errorDiv');
            if (!$errorDiv) {
            // Look for an already provided error container immediately following the input.
            $errorDiv = $input.next('div[data-extend-email-type-error]');
            if ($errorDiv.length === 0) {
                // None found—create one automatically.
                $errorDiv = $('<div data-extend-email-type-error style="display:none; color:#D81C48; margin-top:2px; font-size: .875em;"></div>');
                $input.after($errorDiv);
            }
            // Cache the error container for future events on this input.
            $input.data('errorDiv', $errorDiv);
            }

            // Validate the email; show or hide the error container accordingly.
            if (!strictEmailRegex.test(email) && email.length > 0) {
            $errorDiv.text(errorMsg).show();
            } else {
            $errorDiv.hide();
            }
        });
    });


    // Scroll when opening footer
    $(document).on("shown.bs.collapse", "#accordionFooterContent", function () {
        $(document).scrollTop($(document).height());
    });

    // Handle show more/less behavior
    if ($('#profile-about').length > 0) {
        if ($(".about").height() > $("#profile-about").height()) {
            $("#profile-about").addClass('show-more');
            $(".show-more-button").removeClass('d-none');
        }
    }

    $(document).on('click', '.show-more-button', function (ev) {
        ev.preventDefault();
        $("#profile-about").removeClass('show-more').addClass('show-less');
        $(".show-more-button").addClass("d-none");
        $(".show-less-button").removeClass("d-none");
    });

    $(document).on('click', '.show-less-button', function (ev) {
        ev.preventDefault();
        $("#profile-about").removeClass('show-less').addClass('show-more');
        $(".show-less-button").addClass("d-none");
        $(".show-more-button").removeClass("d-none");
    });

    // Toggle password
    $(document).on('click', '.toggle-password', function () {
        $(this).toggleClass("fa-eye fa-eye-slash");
        var input = $($(this).attr("toggle"));
        if (input.attr("type") == "password") {
            input.attr("type", "text");
        } else {
            input.attr("type", "password");
        }
    });

    // Emoji dropdown
    $(document).on('click', '.emojiPopup-content li a', function () {
        var messageTextarea = $(this).parents('form').find('textarea[name="message"]');
        var message = $(messageTextarea).val();
        $(messageTextarea)
            .val(message + $(this).html())
            .trigger("change");
    });

    // Add Flirt in textarea
    $(document).on('input', '.select-flirt', function () {
        var value = $(this).val();
        var flirt = $(this).find('option[value="' + value + '"]').html().trim();

        var messageTextarea = $('form').find('textarea[name="message"]');
        $(messageTextarea)
            .val(flirt)
            .trigger("input");
    });

    // var initElements = function(context, animateScroll, currMsg) {
    //     $('.messages-wrapper').addClass('open');
    //     // Send message form
    //     context.find('#chat-form').on('submit', function (e) {
    //         e.preventDefault();
    //
    //         // Disable send button until successful response - or after 5 sec
    //         $('button[type=submit]#send-message').attr('disabled', 'disabled');
    //         setTimeout(function(){ $('button[type=submit]#send-message').removeAttr('disabled'); }, 5000);
    //
    //         const data = new FormData(this);
    //         const chat_id = 'chat_' + data.get('id');
    //
    //         /**
    //          *  I found jQuery Ajax to be a pain in the ass when uploading files
    //          *  Nothing is as good as a good old custom/raw/plain piece of code
    //          */
    //         const Request = new XMLHttpRequest();
    //         Request.open('POST', this.action, true);
    //         Request.setRequestHeader('Accept', 'application/json');
    //         Request.onreadystatechange = function() {
    //             if(Request.readyState == 4 && Request.status == 200) {
    //                 const Response = JSON.parse(Request.responseText);
    //
    //                 if (Response.error && Response.error.includes('D-Currency')) {
    //                     var now = new Date();
    //                     now.setHours(now.getHours() + 1);
    //
    //                     document.cookie = chat_id + '=' + data.get('message') + ';path=/;expires=' + now.toUTCString() + ';';
    //                     return;
    //                 }
    //
    //                 if (document.cookie.match("(^| )" + chat_id + "=([^;]+)")) {
    //                     document.cookie = chat_id + '=;max-age=-1;path=/;';
    //                 }
    //
    //                 if(Response.enableBtn) {
    //                     //re-enable send button until gets the response
    //                     //console.log('Re-enable send btn');
    //                     $('button[type=submit]#send-message').removeAttr('disabled');
    //                 }
    //
    //                 loadConversation($('.loadConversation.active')[0], false);
    //             }
    //         }
    //         Request.send(data);
    //     });
    //
    //     // Scroll to end of conversation
    //     if(animateScroll) {
    //         $(".chatmessages").animate({
    //             scrollTop: $('.chatmessages')[0].scrollHeight
    //         }, 'normal');
    //     }
    //
    //     if (window.location.pathname.startsWith('/messages')) {
    //         document.cookie.split(';').forEach(function (cookie) {
    //             const regex = RegExp('^chat_[a-zA-Z0-9]+');
    //
    //             if (!regex.test(cookie.trim())) {
    //                 return;
    //             }
    //
    //             const cookieParts = cookie.split('=');
    //
    //             if (cookieParts.length < 2) {
    //                 return;
    //             }
    //
    //             const key = cookieParts[0];
    //             const keyParts = key.split('_');
    //             const id = keyParts[1];
    //
    //             $("#message_" + id + '_textarea').val(cookieParts[1]);
    //         });
    //     }
    //
    //     if ($("#chat-form").length > 0) {
    //
    //         updateMessageTimestamps();
    //
    //         $('#messages').scroll(function() {
    //             var messageWindow = $(this);
    //             var height = messageWindow.innerHeight();
    //             var scrollHeight = messageWindow.prop('scrollHeight');
    //
    //             if (height >= scrollHeight || $('.message').length < 2) {
    //                 return false;
    //             }
    //
    //             var distanceFromBottom = Math.floor(scrollHeight - (messageWindow.scrollTop() + height));
    //             if (distanceFromBottom < 2) {
    //                 $('.showlatest-box').addClass('d-none');
    //             } else {
    //                 $('.showlatest-box').removeClass('d-none');
    //             }
    //         });
    //
    //         $('.showlatest').on('click', function() {
    //             $('.showlatest-box').addClass('d-none');
    //             scrollTop();
    //         });
    //
    //     }
    // }
    //
    // $(document).on('click', '.messages-go-back', function (event) {
    //     event.preventDefault();
    //     $('.messages-wrapper').removeClass('open');
    // });
    //
    // $(document).on('click', '.loadConversation', function (event) {
    //     event.preventDefault();
    //     loadConversation(this, true, '');
    // });
    //
    // var makeRequest = true;
    // var lastMessage = null;
    //
    // var loadMorePosts = function() {
    //     if(makeRequest) {
    //         $.ajax({
    //             url: showMoreMessagesRoute,
    //             dataType: "json",
    //             type: "POST",
    //             data: {
    //                 offset: $(".message").length,
    //                 profileId: $("#profile_id").val(),
    //             },
    //             beforeSend: function () {
    //                 lastMessage = $('.message:first');
    //                 $('.chating-box .loader').removeClass('d-none');
    //                 $('.chatmessages').css('opacity', '0.3');
    //             },
    //             success: function (data) {
    //                 if (data.status) {
    //                     $("#message-box").prepend(data.messages);
    //                     $('.chatmessages').scrollTop(Math.floor(lastMessage.position().top) - 15);
    //                     $('.chating-box .loader').addClass('d-none');
    //                     $('.chatmessages').css('opacity', '1');
    //                     updateMessageTimestamps();
    //                     if (!data.showMore) {
    //                         makeRequest = false
    //                     }
    //                 }
    //             },
    //             error: function (jqXhr, textStatus, errorThrown) {},
    //         });
    //     }
    // }
    //
    // var loadConversation = function(entry, animateScroll = true, currMsg = '') {
    //     // Load conversation from server
    //     $.ajax({
    //         url: entry.href,
    //         headers: {
    //             'Accept': 'application/json'
    //         },
    //         success: function (data) {
    //             $('#chat-body-container').empty();
    //             const context = $($.parseHTML(data.view));
    //             $('#chat-body-container').append(context);
    //             initElements(context, animateScroll, currMsg);
    //
    //             $(".chatmessages").animate({
    //                 scrollTop: $('.chatmessages')[0].scrollHeight
    //             }, 'normal');
    //             $('.chatmessages').scroll(function(){
    //                 if($(this).scrollTop() < 2){
    //                     if(makeRequest) {
    //                         loadMorePosts();
    //                     }
    //                 }
    //             });
    //             $(".message-inbox.active").removeClass('active');
    //             $('.' + entry.dataset.parent).addClass('active')
    //         },
    //         error: function (error) {
    //             console.error(error);
    //         }
    //     });
    // }
    //
    // if ($(window).width() > 991) {
    //     if($('.loadConversation')[0]) {
    //         $('.loadConversation')[0].click();
    //     }
    // }

    // Handle form validation

    document.querySelectorAll('.needs-validation').forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();

                form.querySelectorAll(':invalid').forEach(field => {
                    field.classList.add('is-invalid');
                });

                // Scroll to the first invalid field smoothly
                const firstErrorElement = form.querySelector('.is-invalid');
                if (firstErrorElement) {
                    firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }

                toastr.error('Please fill out all required fields correctly before submitting.');
            }

            // Add Bootstrap's validation class to the form
            form.classList.add('was-validated');
        });
    });

    //Check if button is near footer

    const $stickyButtonWrapper = $('.sticky-submit-button-wrapper');
    const $footer = $('.footer-profile');
    const offsetFromFooter = 5;

    function isDesktop () {
        return window.matchMedia('(min-width: 992px)').matches;
    }

    function checkStickyButton () {
        if (!isDesktop() || !$('.sticky-submit-button-wrapper').length) {
            return;
        }

        const footerTop = $footer.offset().top;
        const windowHeight = $(window).height();
        const scrollTop = $(window).scrollTop();
        const buttonHeight = $stickyButtonWrapper.outerHeight();
        const buttonBottomPosition = scrollTop + windowHeight - buttonHeight;

        var isNearFooter = buttonBottomPosition >= footerTop - offsetFromFooter;
        if (!$footer.is(":visible")) { isNearFooter = false; }

        $stickyButtonWrapper.css({
            bottom: isNearFooter ? `${$(document).height() - footerTop + offsetFromFooter}px` : '2.5rem'
        });
    }

    function recalculateOnResize () {
        $stickyButtonWrapper.css({
            position: '',
            bottom: ''
        });
        checkStickyButton();
    }

    $(window).on('scroll resize', checkStickyButton);
    $(window).on('resize', recalculateOnResize);

    checkStickyButton();

    // Check if form data has changed and then enable/disable the submit button

    const $form = $('#personal-information-form');
    const $submitButton = $('.sticky-submit-button');
    const initialFormData = $form.serialize();

    function isFormChanged () {
        return $form.serialize() !== initialFormData;
    }
    function toggleSubmitButton () {
        $submitButton.prop('disabled', !isFormChanged());
    }

    $form.on('input change', toggleSubmitButton);

    typeof Livewire !== 'undefined' && Livewire.on('success', (message) => {
        toastr.success(message);
    });

    typeof Livewire !== 'undefined' && Livewire.on('error', (message) => {
        toastr.error(message);
    });

    let favoritePhotoInProgress = false;

    $(document).ready(function () {
        $(document).on("click", ".favorite-photo", function (ev) {
            ev.preventDefault();

            if (favoritePhotoInProgress) {
                return false;
            }

            const elem = $(this);
            const uploadId = elem.data("upload-id") || null;
            const profileId = elem.data("profile-id") || null;

            const url = elem.hasClass("is-favorite-photo")
                ? elem.data("remove")
                : elem.data("add");

            favoritePhotoInProgress = true;

            $.ajax({
                type: "POST",
                url: url,
                data: {
                    upload_id: uploadId,
                    profile_id: profileId,
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
                beforeSend: function () {
                    elem.addClass("loading");
                },
                success: function (response) {
                    elem.toggleClass("add-favorite-photo is-favorite-photo");
                    elem.removeClass("loading");

                    elem.find(".text-is-favorite-photo").toggleClass("d-none");
                    elem.find(".text-add-favorite-photo").toggleClass("d-none");

                    if (response.success) {
                        toastr.success(response.message, "", {
                            closeButton: true,
                            onclick: function () {
                                window.location.href = response.url;
                            },
                        });
                    } else {
                        toastr.error(response.message || "An error occurred.");
                    }
                },
                error: function (xhr) {
                    elem.removeClass("loading");

                    const errorMessage =
                        xhr.responseJSON && xhr.responseJSON.message
                            ? xhr.responseJSON.message
                            : "An unexpected error occurred.";
                    toastr.error(errorMessage);
                },
                complete: function () {
                    favoritePhotoInProgress = false;
                },
            });
        });
    });

});
