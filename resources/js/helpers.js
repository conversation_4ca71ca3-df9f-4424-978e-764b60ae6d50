// helpers.js
/**
 * Processes an array of files, resizing images and returning a DataTransfer object.
 * Non-image files are added directly without resizing.
 * @param {File[]} files - Array of files to process.
 * @returns {Promise<FileList>} - A Promise that resolves to a FileList containing processed files.
 */
export async function processUploads(files){
    if (!files.length) return;
    const dataTransfer = new DataTransfer();
    for (const file of files) {
        if (!file.type.startsWith('image')) {
            dataTransfer.items.add(file); // Add non-image files directly to the DataTransfer
            continue;
        }
        const resizedFile = await resizeImage(file, { quality: 0.8, type: file.type });
        dataTransfer.items.add(resizedFile); // Add resized image to the DataTransfer
    }
    return dataTransfer.files; // Return the files from the DataTransfer object
}

/**
 * Resizes an image file to fit within specified dimensions while maintaining aspect ratio.
 * @param {File} file - The image file to resize.
 * @param {Object} options - Options for resizing.
 * @param {number} options.quality - Quality of the resized image (0 to 1).
 * @param {string} options.type - MIME type of the resized image (default is 'image/jpg').
 * @param {number} options.maxWidth - Maximum width of the resized image (default is 900).
 * @param {number} options.maxHeight - Maximum height of the resized image (default is 900).
 * @returns {Promise<File>} - A Promise that resolves to a resized image file.
 */
async function resizeImage(file,{ quality = 1, type = 'image/jpg', maxWidth = 900, maxHeight = 900 }) {
    const bitmap = await createImageBitmap(file);
    const canvas = document.createElement("canvas");

    let width = bitmap.width;
    let height = bitmap.height;
    if (width > height) {
        if (width > maxWidth) {
            height *= maxWidth / width;
            width = maxWidth;
        }
    } else {
        if (height > maxHeight) {
            width *= maxHeight / height;
            height = maxHeight;
        }
    }
    if (width <= 0 || height <= 0) {
        console.warn('Invalid dimensions for resizing:', { width, height });
        return file; // Return original file if dimensions are invalid
    }
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext("2d");
    ctx.drawImage(bitmap, 0, 0, width, height);

    const blob = await new Promise((resolve) => {
        canvas.toBlob((blob) => {
            resolve(blob);
        }, type, quality);
    });

    return new File([blob], file.name, { type: blob.type });
}