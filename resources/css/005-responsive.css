/*////////////////////////////////////////////////////// Responsive CSS Structure //////////////////////////////////////////////////////*/

@media (min-width: 1300px) {
    .container {
        max-width: 1224px;
    }
}

@media (min-width: 1200px) and (max-width: 1299px) {
    .navbar {
        padding: 8px 0px;
    }

    .navbar-brand span {
        font-size: 35px;
        line-height: 60px;
    }

    .all-filed .btn {
        padding: 12px 8px;
    }

    .footer {
        padding: 30px 0px;
    }

    .confirm-account {
        padding: 50px 40px;
    }

    .confirm-account .account-btn {
        padding: 14px 40px;
    }

    .verification {
        padding: 30px 25px;
    }

    .head {
        margin-bottom: 25px;
    }

    .phone-number .form-control {
        padding: 10px 8px;
        line-height: 30px;
    }

    .phone-number .send-sms {
        font-size: 22px;
        line-height: 32px;
        padding: 12px 16px;
    }

    .confirm-account p {
        margin-bottom: 40px;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .navbar {
        padding: 8px 0px;
    }

    .navbar-brand span {
        font-size: 35px;
        line-height: 60px;
    }

    .all-filed .btn {
        padding: 10px 8px;
        font-size: 22px;
        line-height: 32px;
    }

    .login .btn {
        padding: 3px 32px 4px;
    }

    .signup-form .nav-tabs .nav-item .nav-link {
        padding: 4px 16px;
    }

    .signup-form .all-btn {
        padding: 18px 0px;
    }

    .signup-form .nav-tabs span {
        margin-bottom: 6px;
    }

    .signup-form .interested {
        padding: 18px 22px;
    }

    .signup-form .age-intrest {
        padding: 6px 12px;
    }

    .signup-form .all-filed .form-control {
        padding: 8px 10px;
    }

    .copyright-link .all-menu li {
        margin: 0 10px;
    }


    .footer {
        padding: 25px 0px;
    }

    .confirm-account {
        padding: 35px 30px;
    }

    .verification {
        padding: 25px 20px;
    }

    .form-signup {
        bottom: 130px;
    }

    .head {
        margin-bottom: 15px;
        font-size: 22px;
        line-height: 26px;
    }

    .verification p {
        font-size: 18px;
        line-height: 24px;
    }

    .phone-number .form-control {
        padding: 8px 8px;
        line-height: 24px;
        font-size: 18px;
    }

    .phone-number .send-sms {
        font-size: 18px;
        line-height: 26px;
        padding: 8px 12px;
    }

    .confirm-account p {
        margin-bottom: 20px;
        font-size: 22px;
        line-height: 28px;
    }

    .girls-image::after {
        width: 345px;
        height: 345px;
    }

    .verification .simple-box {
        width: 205px;
        margin: 0 auto;
    }

    .interest-find .select2 {
        width: 150px !important;
    }

    .all-dropdown .find-btn {
        width: 140px;
    }

    .category-box {
        padding: 15px 10px;
    }

    .category-box ul li a {
        font-size: 16px;
        line-height: 20px;
        padding: 6px 10px;
    }

    .select2-container .selection .select2-selection {
        padding: 6px 8px;
    }

    .select2-container--default
        .select2-selection--single
        .select2-selection__arrow {
        top: 6px !important;
    }

    .chat-starter
        .select2-container--default
        .select2-selection--single
        .select2-selection__arrow {
        right: 2px !important;
        top: 10px !important;
    }

    .chat-starter
        .select2-container--default
        .select2-selection--single[aria-expanded="true"]
        .select2-selection__arrow {
        right: 8px !important;
        top: 8px !important;
    }

    .button .btn {
        padding: 7px 10px;
        margin-top: 8px;
    }

    .favorite-sendmess .image-text .name {
        font-size: 18px;
        line-height: 22px;
    }

    .favorite-sendmess .image-text .text {
        padding: 10px 12px 15px;
    }

    .favorite-sendmess .image-text .tag span {
        padding: 4px 16px;
    }

    .pagination .page-item .page-link {
        width: 38px;
        height: 37px;
    }

    .pagination .page-item.previ {
        margin-right: 15px;
    }

    .pagination .page-item.next {
        margin-left: 15px;
    }

    .chat-section .nav-tabs .nav-item .nav-link {
        padding: 8px 16px;
    }

    .chating-box .comment-box {
        padding: 12px 8px;
    }

    .chating-box .comment-box br {
        display: none;
    }

    .chat-section .chating-box {
        padding: 20px 15px;
    }

    .f-message .uploade-btn .link {
        width: 45px;
        height: 45px;
    }

    .chating-box .chat-detail {
        padding: 15px 15px 15px;
        height: 630px;
    }

    .chat-section .profile-pic {
        flex-wrap: wrap;
    }

    .profile-pic .location-date {
        margin-left: 0;
        margin-top: 8px;
    }

    .chating-box .chat-detail .all-message {
        height: 465px;
    }

    .chat-section .right-btntext {
        margin-top: 15px;
        margin-left: unset;
    }

    .chat-section .right-btntext .add-credits {
        padding: 8px 18px;
    }

    .chat-section .right-btntext .add-token {
        padding: 8px 18px;
    }

    .chating-box .all-file span {
        font-size: 16px;
        line-height: 18px;
        margin-left: 10px;
    }

    .chating-box.archived-box {
        height: 800px;
    }

    .chating-box .all-file .file-icon svg {
        font-size: 30px;
    }

    .chating-box .all-file .more-icon {
        font-size: 20px;
    }

    .chat-detail .message-box .text-box textarea {
        padding: 12px;
    }

    .img-send .image {
        font-size: 16px;
        line-height: 20px;
    }

    .img-send .emoji .btn-drop {
        padding: 4px 6px;
    }

    .chating-box .all-commentbox {
        height: 765px;
    }

    .img-send .send-message {
        padding: 8px 12px;
    }

    .full-profile .profile {
        padding: 15px 10px;
    }

    .profile .h-title h5 {
        font-size: 22px;
        line-height: 26px;
        margin-bottom: 8px;
    }

    .profile .h-title h5 span {
        padding: 4px 8px;
        font-size: 14px;
        line-height: 16px;
    }

    .profile .right-text .year-wilts {
        margin-bottom: 8px;
    }

    .profile .button {
        margin-top: 15px;
    }

    .full-profile .interest {
        padding: 15px 10px;
    }

    .full-profile ul li {
        margin-bottom: 10px;
    }

    .full-profile ul li img {
        margin-right: 0px;
        margin-bottom: 8px;
        display: block;
    }

    .img-check .text .head h5 {
        font-size: 35px;
        line-height: 38px;
    }

    .img-check .text span {
        font-size: 20px;
        line-height: 24px;
    }

    .full-profile .private-img .s-message {
        padding: 15px 10px 10px;
    }

    .img-check .text p {
        font-size: 16px;
        line-height: 18px;
    }

    .shy-type .title h4 {
        font-size: 19px;
        line-height: 24px;
        margin-bottom: 15px;
    }

    .full-profile .private-img .s-message p {
        line-height: 26px;
    }

    .shy-type .conversation {
        padding: 20px 15px;
    }

    .send-btn {
        font-size: 16px;
        line-height: 21px;
    }

    .shy-type .thing {
        padding: 15px 15px;
    }

    .shy-type .thing span {
        font-size: 16px;
        line-height: 18px;
        margin-bottom: 10px;
    }

    .full-profile .shy-type {
        margin-bottom: 25px;
    }

    .heading {
        font-size: 25px;
        line-height: 30px;
    }

    .step-form {
        bottom: 110px;
    }

    .near-you .text {
        padding: 8px 12px 15px;
    }

    .near-you .tag span {
        padding: 4px 14px;
    }

    .near-you .name {
        font-size: 18px;
        line-height: 22px;
    }

    .near-you {
        margin-bottom: 40px;
    }

    .delete-profile .accordion-button::after {
        top: -4px;
    }

    .all-password .form-label {
        font-size: 16px;
        line-height: 20px;
    }

    .setting {
        margin-bottom: 80px;
    }

    .all-password {
        padding: 35px 20px;
    }

    .info .info-select {
        width: 22%;
    }

    .all-password .info ul li span.text {
        width: 78%;
    }

    .setting .nav-tabs .nav-item .nav-link {
        padding: 10px 18px;
    }

    .setting .nav-tabs {
        margin-bottom: 25px;
    }

    .profile-pic .name-online .p-name {
        font-size: 20px;
        line-height: 24px;
    }

    .profile-pic .name-online .online {
        font-size: 14px;
        line-height: 16px;
    }

    .profile-pic .name-online .c-archive {
        font-size: 14px;
        line-height: 16px;
    }

    .pic-editpic .group-img img {
        width: 60px;
        height: 60px;
    }

    .uploade-text .photo-btn {
        padding: 8px 12px;
        font-size: 16px;
        line-height: 18px;
        margin-bottom: 5px;
    }

    .uploade-text h4 {
        margin-bottom: 10px;
    }

    .general-profile .pic-editpic {
        padding-bottom: 25px;
        margin-bottom: 25px;
    }

    span.label-name {
        font-size: 16px;
        line-height: 18px;
    }

    .yourself .save-btn,
    .idea-match .save-btn,
    .my-interests .save-btn {
        margin-top: 15px;
    }

    .idea-match,
    .my-interests {
        padding: 20px 20px;
    }

    .location-date .name h5 {
        font-size: 22px;
        line-height: 25px;
    }

    .detail-date .p-image {
        width: 24%;
    }

    .contribu-box .location-date {
        width: 76%;
    }

    .contribu-box .button .btn {
        margin-bottom: 10px;
    }

    .contribu-box .button {
        text-align: left;
    }

    .comment-detail .name-view .head {
        font-size: 28px;
        line-height: 33px;
    }

    .comment-detail .detail-date .p-image,
    .comment-text .image {
        width: 10%;
    }

    .comment-detail .location-date,
    .comment-text .name-context {
        width: 90%;
    }

    .comment-text .name-context .text p {
        font-size: 16px;
        line-height: 18px;
    }

    .comment-detail .text p {
        font-size: 16px;
        line-height: 20px;
    }

    .pay-method {
        padding: 30px 25px;
    }

    .membercomment .dummy-image img {
        height: 350px;
    }

    .prefferred ul li a {
        width: 80px;
        height: 45px;
    }

    .prefferred ul li {
        margin-bottom: 8px;
    }

    .prefferred ul {
        margin-bottom: 15px;
    }


    .choose-bundle .token .m-tag {
        padding: 6px 12px;
    }

    .choose-bundle .token span {
        font-size: 18px;
        line-height: 20px;
    }

    .choose-bundle .token img {
        width: 12%;
    }

    .card-detail .form-group .form-control {
        padding: 12px 16px;
        font-size: 16px;
        line-height: 18px;
    }

    .card-detail .btn-pay {
        padding: 12px 12px;
    }

    .card-detail p br {
        display: none;
    }

    .card-detail p {
        font-size: 16px;
        line-height: 18px;
    }

    .failed-message .head {
        font-size: 28px;
        line-height: 32px;
    }

    .chat-groups .image .link a {
        font-size: 14px;
        line-height: 16px;
        padding: 6px 10px;
    }

    .my-tribe {
        padding: 30px 25px;
    }

    .play-box .image {
        width: 25%;
    }

    .play-box .title-text .title {
        font-size: 16px;
        line-height: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .important .play-box .title-text {
        width: 48%;
    }

    .play-box .title-text .small p {
        width: 90px;
    }

    .play-box .p-icon {
        width: 14%;
    }

    .chating-box .play-box .image img {
        width: 45px;
        height: 45px;
    }

    .favourites .image-text .star {
        padding-left: 12px;
        padding-top: 8px;
        font-size: 22px;
    }
}

@media (min-width: 992px) {
    .page-chat footer {
        display: none;
    }
}

@media (max-width: 992px) {
    .container-chat {
        height: 100%; 
    }
    .page-chat .sub-header {
        display: none;
    }

    /* .header {
        margin-bottom: 5px;
    } */

    .content-tabs {
        margin-bottom: 0;
        justify-content: center;
    }

    .footer-chat,
    .page-messages .sub-header,
    .page-member .sub-header,
    .page-chat header,
    .page-credits .sub-header,
    .page-credits footer
    {
        display: none;
    }

    .page-credits.page-cancel header, .page-credits.page-success header{
        display: block;
    }

    .page-success .sub-header,
    .page-success .main-header,
    .page-cancel .main-header,
    .page-cancel .sub-header {
        display: block;
    }

    .page-success .container,
    .page-cancel .container {
        padding-top: 0;
    }

    .page-success .back-button,
    .page-cancel .back-button {
        display: block!important;
    }

    .single-group .sub-header h1.main-title,
    .single-post .sub-header .btn
    {
        display: none;
    }

    .btn {
        padding: 6px 12px!important;
    }

    #conversation-nav .nav-left .btn,
    #conversation-nav .nav-right .btn {
        padding: 6px 2px!important;
    }

    .token-bundle .head h4 {
        display: inline-block;
    }

    .filters {
        margin-bottom: 15px;
    }

    h1.main-title {
        font-size: 1.2rem;
        line-height: 2.5rem;
    }

    .content-tabs .nav-item .nav-link {
        font-size: 15px;
        line-height: 20px;
        padding: 8px 12px;
    }

    .like-list .btn-secondary-highlight {
        font-size: 20px;
        margin-top: 6px;
    }

    .page-chat .container {
        max-width: 100%;
    }

    .chat-section .chating-box {
        padding: 5px;
    }

    .page-chat .message-box .btn.send-emoji,
    .page-chat .message-box .btn.select-upload {
        height: 38px;
    }

    .page-chat .photo-preview img {
        max-height: 90px;
        max-width: 135px;
    }

    .showlatest-box {
        bottom: 100px;
    }

    .profile-information img {
        width: 50px;
        height: 50px;
    }

    .location-date .locati,
    .location-date .date {
        line-height: 42px;
    }

    .location-date h3 {
        line-height: 40px;
        font-size: 1.2em;
        margin-right: 15px;
        margin-bottom: 5px;
    }

    .profile-information .c-archive {
        display: inline-block;
        margin-top: 8px;
    }
}

@media (min-width: 768px) and (max-width: 992px) {
    .top-bar {
        padding: 10px 10px;
    }

    .navbar {
        padding: 8px 0px;
    }

    .navbar-brand span {
        font-size: 32px;
        line-height: 50px;
    }

    .all-filed .btn {
        padding: 10px 8px;
        font-size: 22px;
        line-height: 32px;
    }

    .login .btn {
        padding: 3px 32px 4px;
    }

    .signup-form .nav-tabs .nav-item .nav-link {
        padding: 4px 16px;
        line-height: 30px;
    }

    .signup-form .all-btn {
        padding: 16px 0px;
    }

    .signup-form .all-filed {
        padding: 16px 20px;
    }

    .signup-form .nav-tabs span {
        margin-bottom: 6px;
    }

    .signup-form .interested {
        padding: 16px 20px;
    }

    .signup-form .age-intrest {
        padding: 4px 10px;
    }

    .signup-form .all-filed .form-control {
        padding: 8px 10px;
    }

    .copyright-link .all-menu li {
        margin: 0 5px;
    }

    .copyright-link .all-menu {
        justify-content: flex-start;
    }

    .footer {
        padding: 20px 0px;
    }

    .confirm-account {
        padding: 25px 25px;
        margin-bottom: 20px;
    }

    .confirm-account .account-btn {
        padding: 10px 30px;
        font-size: 18px;
        line-height: 28px;
    }

    .verification {
        padding: 25px 20px;
    }

    .head {
        margin-bottom: 15px;
        font-size: 20px;
        line-height: 26px;
    }

    .verification p {
        font-size: 18px;
        line-height: 24px;
    }

    .form-signup .signup-form .btn-recover {
        padding: 8px 25px;
    }

    .phone-number .form-control {
        padding: 8px 8px;
        line-height: 24px;
        font-size: 18px;
    }

    .phone-number .send-sms {
        font-size: 18px;
        line-height: 23px;
        padding: 10px 14px;
    }

    .confirm-account p {
        margin-bottom: 20px;
        font-size: 20px;
        line-height: 26px;
    }

    .girls-image::after {
        width: 245px;
        height: 245px;
    }

    .form-signup.form-top {
        top: 40px;
    }

    .form-signup {
        bottom: 145px;
    }

    .verification .simple-box img {
        width: 180px;
    }

    .all-step .heading {
        padding-top: 20px;
        padding-bottom: 30px;
        font-size: 28px;
        line-height: 32px;
    }

    #multistepsform fieldset p span {
        font-size: 20px;
        line-height: 26px;
        padding: 6px 12px;
    }

    #multistepsform fieldset p {
        font-size: 18px;
        line-height: 22px;
    }

    #multistepsform .step-title {
        font-size: 18px;
        line-height: 22px;
    }

    #multistepsform fieldset .all-img .image {
        margin-bottom: 25px;
    }

    #multistepsform fieldset .all-img .image span {
        font-size: 20px;
        line-height: 26px;
        margin-top: 10px;
    }

    #multistepsform .action-button {
        font-size: 20px;
        line-height: 26px;
        margin: 30px 0px;
    }

    .step-form {
        bottom: 130px;
        top: 65px;
    }

    .background {
        background-size: cover;
    }

    .interest-find .select2 {
        width: 180px !important;
        margin: 0 8px;
    }

    .chat-section .right-btntext {
        flex-wrap: wrap;
        margin-top: 10px;
        font-size: 16px;
        line-height: 18px;
        margin-left: 0;
    }

    .chating-box .all-file {
        margin-bottom: 12px;
    }

    .chating-box .all-file .file-icon svg {
        font-size: 40px;
    }

    .chating-box .all-file span {
        font-size: 16px;
        line-height: 18px;
        margin-left: 10px;
    }

    .chating-box .all-file .more-icon {
        font-size: 20px;
    }

    .chating-box.archived-box {
        height: auto;
    }

    .chat-section .right-btntext .add-credits {
        padding: 8px 18px;
        margin: 0px 8px;
    }

    .chat-section .right-btntext .add-token {
        padding: 8px 18px;
        margin: 0px 8px;
    }

    .all-dropdown .find-btn {
        width: 150px;
        padding: 10px 25px;
        margin-top: 20px;
    }

    .all-dropdown .all-field {
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    .category-box {
        padding: 15px 10px;
    }

    .interest-find span {
        line-height: 22px;
    }

    .category-box ul li a {
        font-size: 16px;
        line-height: 20px;
        padding: 6px 8px;
    }

    .select2-container .selection .select2-selection {
        padding: 6px 8px;
    }

    .chat-starter
        .select2-container--default
        .select2-selection--single
        .select2-selection__arrow {
        top: 10px !important;
        right: 4px !important;
    }

    .chat-starter
        .select2-container--default
        .select2-selection--single[aria-expanded="true"]
        .select2-selection__arrow {
        right: 10px !important;
    }

    .button .btn {
        padding: 7px 12px;
        margin-top: 8px;
    }

    .contributions .add-new,
    .f-message .add-new {
        font-size: 16px;
        line-height: 19px;
        padding: 8px 10px;
    }

    .f-message .uploade-btn .link {
        width: 40px;
        height: 40px;
        font-size: 18px;
        margin-right: 8px;
    }

    .header .buynow {
        padding: 6px 6px;
    }

    .buynow .icon {
        width: 26px;
    }

    .buynow .buy-btn {
        padding: 6px 8px;
    }

    .buynow .text-wallet {
        width: 106px;
        margin: 0px 8px;
    }

    .favorite-sendmess .image-text .name {
        font-size: 18px;
        line-height: 22px;
    }

    .favorite-sendmess .image-text .text {
        padding: 10px 15px 15px;
    }

    .favorite-sendmess .image-text .tag span {
        padding: 4px 16px;
    }

    .pagination .page-item .page-link {
        width: 35px;
        height: 34px;
        font-size: 16px;
        line-height: 18px;
    }

    .pagination .page-item.previ {
        margin-right: 15px;
    }

    .pagination .page-item.next {
        margin-left: 15px;
    }

    .age ul li {
        width: 28px;
        height: 28px;
        font-size: 14px;
        line-height: 22px;
        margin: 0 10px;
    }

    .chat-section .nav-tabs .nav-item .nav-link {
        padding: 8px 16px;
    }

    .chating-box .comment-box {
        padding: 10px 6px;
        flex-wrap: wrap;
    }

    .all-text .name-time {
        flex-direction: column;
        align-items: baseline;
    }

    .chating-box .comment-box .image {
        margin-bottom: 10px;
        margin-right: 0;
    }

    .all-text .name-time .name {
        margin-bottom: 5px;
    }

    .chating-box .comment-box br {
        display: none;
    }

    .chat-detail .message-box {
        padding: 12px 12px 12px;
    }

    .chating-box .chat-detail .all-message {
        height: 345px;
    }

    .chating-box .chat-detail {
        padding: 12px 12px 12px;
        height: 580px;
    }

    .chat-detail .right-message .right-icon {
        left: 0px;
        font-size: 14px;
    }

    .chat-detail .message-box .text-box textarea {
        padding: 10px;
    }

    .img-send .image {
        font-size: 16px;
        line-height: 20px;
    }

    .chat-section .profile-pic {
        flex-wrap: wrap;
    }

    .profile-pic .location-date {
        margin-left: 0;
    }

    .chating-box .all-commentbox {
        height: 705px;
    }

    .img-send .emoji .btn-drop {
        padding: 2px 4px;
    }

    .img-send .emoji .btn-drop img {
        width: 22px;
    }

    .membercomment .dummy-image img {
        height: 350px;
    }

    .emoji .dropdown-toggle::after {
        right: -8px;
    }

    .img-send .send-message {
        padding: 8px 14px;
    }

    .message-box .img-send,
    .dont-say .img-send {
        flex-direction: column;
    }

    .s-message .message-box .img-send {
        flex-direction: row;
    }

    .message-box .img-send .photo-emoji {
        margin-bottom: 10px;
    }

    .full-profile .profile {
        margin-bottom: 15px;
    }

    .profile .h-title h5 {
        font-size: 22px;
        line-height: 26px;
        margin-bottom: 8px;
    }

    .profile .h-title h5 span {
        padding: 4px 8px;
        font-size: 14px;
        line-height: 16px;
    }

    .profile .right-text .year-wilts {
        margin-bottom: 8px;
    }

    .profile .button {
        margin-top: 15px;
    }

    .full-profile ul li {
        margin-bottom: 10px;
    }

    .full-profile ul li img {
        margin-right: 10px;
    }

    .img-check .text .head h5 {
        font-size: 35px;
        line-height: 38px;
    }

    .img-check .text span {
        font-size: 22px;
        line-height: 24px;
    }

    .full-profile .private-img .s-message {
        padding: 15px 15px 10px;
        margin-bottom: 15px;
        height: auto;
    }

    .full-profile .private-img .s-message p br {
        display: none;
    }

    .img-check .text p {
        font-size: 18px;
        line-height: 20px;
    }

    .shy-type .title h4 {
        font-size: 20px;
        line-height: 24px;
        margin-bottom: 15px;
    }

    .shy-type .conversation {
        margin-bottom: 15px;
        height: auto;
    }

    .send-btn {
        font-size: 16px;
        line-height: 21px;
        padding: 8px 20px;
    }

    .shy-type .thing span {
        font-size: 16px;
        line-height: 18px;
        margin-bottom: 10px;
    }

    .full-profile .shy-type {
        margin-bottom: 20px;
    }

    .heading {
        font-size: 25px;
        line-height: 30px;
    }

    .near-you .heading {
        margin-bottom: 10px;
    }

    .near-you .image-text {
        margin-bottom: 15px;
    }

    .near-you .text {
        padding: 8px 12px 15px;
    }

    .full-profile .private-img {
        margin: 15px 0px;
    }

    .photo-more .check-out {
        padding: 10px;
    }

    .near-you .tag span {
        padding: 4px 14px;
    }

    .chat-starter .select2 {
        font-size: 16px;
        line-height: 18px;
    }

    .near-you .name {
        font-size: 18px;
        line-height: 22px;
    }

    .near-you {
        margin-bottom: 25px;
    }

    .delete-profile .accordion-button::after {
        top: -4px;
    }

    .select2-container--default
        .select2-selection--single[aria-expanded="true"]
        .select2-selection__arrow {
        top: 6px !important;
    }

    .all-password .form-label {
        font-size: 16px;
        line-height: 20px;
    }

    .all-password .form-group .form-control {
        font-size: 16px;
        line-height: 20px;
        padding: 8px 12px;
    }

    .all-password .form-group {
        margin-bottom: 15px;
    }

    .all-password {
        padding: 30px 20px;
    }

    .info .info-select {
        width: 35%;
    }

    .all-password .info ul li span.text {
        width: 65%;
    }

    .all-password .info ul li {
        font-size: 16px;
        line-height: 18px;
    }

    .info-select .checkmark {
        height: 20px;
        width: 20px;
    }

    .info-select .custom-click {
        font-size: 16px;
        line-height: 20px;
        margin-right: 8px;
    }

    .info-select .custom-click .checkmark:after {
        width: 8px;
        height: 8px;
    }

    .setting .nav-tabs .nav-item .nav-link {
        font-size: 16px;
        line-height: 18px;
        padding: 10px 18px;
    }

    .data-profile .button {
        margin-top: 15px;
    }

    .data-profile .request-deta {
        margin-bottom: 25px;
    }

    .setting {
        margin-bottom: 70px;
    }

    .profile-pic .name-online .p-name {
        font-size: 18px;
        line-height: 22px;
    }

    .profile-pic .name-online .online {
        font-size: 14px;
        line-height: 16px;
    }

    .profile-pic .name-online .c-archive {
        font-size: 14px;
        line-height: 16px;
    }

    .profile-pic .p-image img {
        width: 60px;
        height: 60px;
    }

    .profile-pic .name-online .p-name {
        font-size: 20px;
        line-height: 24px;
    }

    .uploade-text .photo-btn {
        padding: 8px 12px;
        font-size: 16px;
        line-height: 18px;
        margin-bottom: 5px;
    }

    .uploade-text h4 {
        margin-bottom: 10px;
        font-size: 24px;
        line-height: 28px;
    }

    .general-profile .pic-editpic {
        padding-bottom: 25px;
        margin-bottom: 25px;
    }

    span.label-name {
        font-size: 16px;
        line-height: 18px;
    }

    .yourself .save-btn,
    .idea-match .save-btn,
    .my-interests .save-btn {
        margin-top: 15px;
        padding: 8px 18px;
    }

    .idea-match,
    .my-interests {
        padding: 20px 20px;
    }

    .yourself .text-box p {
        font-size: 16px;
        line-height: 22px;
    }

    .general-profile .yourself {
        margin-top: 20px;
    }

    .yourself .text-box {
        padding: 15px;
    }

    .tribe-member .pic-text p {
        font-size: 16px;
        line-height: 22px;
    }

    .tribe-member,
    .contributions {
        padding: 20px 15px;
    }

    .contributions .head-addnew {
        margin-bottom: 15px;
    }

    .contributions .contribu-box {
        padding: 20px 15px;
    }

    .location-date .name h5 {
        font-size: 22px;
        line-height: 25px;
    }

    .contribu-box .detail-date {
        margin-bottom: 10px;
    }

    .contribu-box .text p {
        font-size: 16px;
        line-height: 18px;
    }

    .detail-date .p-image {
        width: 16%;
    }

    .contribu-box .location-date {
        width: 84%;
    }

    .comment-detail .detail-date .p-image {
        width: 15%;
    }

    .comment-detail .location-date {
        width: 85%;
    }

    .comment-detail .name-view .head {
        font-size: 26px;
        line-height: 32px;
    }

    .comment-detail,
    .latest-comment {
        padding: 20px 15px;
    }

    .comment-text .name-context .text p {
        font-size: 15px;
        line-height: 18px;
    }

    .comment-detail .text p {
        font-size: 16px;
        line-height: 20px;
    }

    .comment-detail .detail-date {
        margin-bottom: 12px;
    }

    .comment-detail .name-view {
        margin-bottom: 12px;
    }

    .comment-text .image {
        width: 15%;
    }

    .comment-text .name-context {
        width: 85%;
    }

    .comment-text .comment-time .time span {
        font-size: 16px;
        line-height: 18px;
    }

    .pay-method {
        padding: 25px 20px;
        margin-bottom: 40px;
    }

    .choose-bundle .token img {
        width: 10%;
    }

    .prefferred ul li a {
        width: 85px;
        height: 50px;
    }


    .choose-bundle .token .m-tag {
        padding: 8px 18px;
    }

    .prefferred ul {
        margin-bottom: 15px;
    }

    .choose-bundle .token span {
        font-size: 18px;
        line-height: 20px;
    }

    .card-detail .form-group .form-control {
        font-size: 16px;
        line-height: 18px;
        padding: 12px 18px;
    }

    .card-detail .form-group {
        margin-bottom: 12px;
    }

    .card-detail .btn-pay {
        padding: 10px 12px;
    }

    .card-detail p {
        font-size: 16px;
        line-height: 18px;
    }

    .failed-message .fpay-icon {
        width: 140px;
        height: 140px;
        margin: 0 auto 10px;
    }

    .failed-message .head {
        font-size: 26px;
        line-height: 30px;
        margin-bottom: 10px;
    }

    .my-tribe .link-block {
        margin-bottom: 20px;
    }

    .all-tribes .chat-groups .link-block {
        margin-bottom: 20px;
    }

    .chat-groups .link-block span {
        margin-top: 12px;
    }

    .my-tribe {
        padding: 25px 20px;
    }

    .contact-us {
        padding: 25px 25px;
        margin-bottom: 40px;
    }

    .contact-us .text-detail {
        margin-bottom: 30px;
    }

    .text-detail .btn-send {
        padding: 10px 20px;
    }

    .play-box .image {
        width: 25%;
    }

    .play-box .title-text .title {
        font-size: 16px;
        line-height: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .important .play-box .title-text {
        width: 48%;
    }

    .play-box .title-text .small p {
        width: 90px;
    }

    .play-box .p-icon {
        width: 14%;
    }

    .play-box .title-text {
        width: 60%;
    }

    .chating-box .play-box .image img {
        width: 45px;
        height: 45px;
    }

    .favourites .image-text .star {
        padding-left: 10px;
        padding-top: 8px;
        font-size: 24px;
    }
}

@media (max-width: 767px) {
    .background {
        background-size: cover;
    }

    .form-signup {
        top: 60px;
        bottom: 155px;
    }

    .container-chat {
        width: 100%;
        max-width: 100%;
    }

    .chat-section .chating-box {
        border-radius: 0;
    }
}

@media (min-width: 767px) {
    .modal-dialog {
        max-width: 600px;
    }
}

/*---------- Small Mobile , IPhone Start ----------*/

/*=== Screen Size = 240, 320, 360, 480, 568 ===*/
@media (min-width: 576px) and (max-width: 767px) {
    .top-bar {
        padding: 10px 10px;
    }

    .navbar {
        padding: 8px 0px;
    }

    .navbar-brand span {
        font-size: 32px;
        line-height: 50px;
    }

    .all-filed .btn {
        padding: 8px 6px;
        font-size: 20px;
        line-height: 30px;
    }

    .login .btn {
        padding: 3px 32px 4px;
    }

    .heading {
        margin-bottom: 15px;
    }

    .small-head {
        font-size: 28px;
        line-height: 32px;
    }

    .footer .copyright-link p {
        margin-bottom: 10px;
    }

    .signup-form .nav-tabs .nav-item .nav-link {
        padding: 4px 16px;
        line-height: 29px;
    }

    .signup-form .all-btn {
        padding: 16px 0px;
    }

    .signup-form .nav-tabs span {
        margin-bottom: 6px;
    }

    .signup-form .interested {
        padding: 16px 20px;
    }

    .signup-form .all-filed {
        padding: 16px 20px;
    }

    .signup-form .age-intrest {
        padding: 4px 10px;
    }

    .signup-form .all-filed .form-control {
        padding: 8px 10px;
    }

    .copyright-link .all-menu li {
        margin: 0 5px;
    }

    .copyright-link .all-menu {
        justify-content: flex-start;
    }

    .footer {
        padding: 20px 0px;
    }

    .confirm-account {
        padding: 20px 20px;
        margin-bottom: 25px;
    }

    .confirm-account .account-btn {
        padding: 10px 30px;
        font-size: 18px;
        line-height: 25px;
    }

    .verification {
        padding: 25px 20px;
    }

    .head {
        margin-bottom: 15px;
        font-size: 20px;
        line-height: 26px;
    }

    .verification p {
        font-size: 18px;
        line-height: 24px;
    }

    .phone-number .form-control {
        padding: 8px 8px;
        line-height: 22px;
        font-size: 16px;
    }

    .phone-number .send-sms {
        font-size: 16px;
        line-height: 20px;
        padding: 8px 12px;
    }

    .confirm-account p {
        margin-bottom: 25px;
        font-size: 18px;
        line-height: 25px;
    }

    .girls-image::after {
        width: 245px;
        height: 245px;
    }

    .verification .simple-box img {
        width: 150px;
    }

    .all-step .heading {
        padding-top: 15px;
        padding-bottom: 20px;
        font-size: 24px;
        line-height: 28px;
    }

    #multistepsform fieldset p span {
        font-size: 20px;
        line-height: 26px;
        padding: 6px 12px;
    }

    #multistepsform fieldset p {
        font-size: 18px;
        line-height: 22px;
    }

    #multistepsform .step-title {
        font-size: 18px;
        line-height: 22px;
    }

    #multistepsform fieldset .all-img .image {
        margin-bottom: 20px;
    }

    #multistepsform fieldset .all-img .image span {
        font-size: 20px;
        line-height: 26px;
        margin-top: 8px;
    }

    #multistepsform .action-button {
        font-size: 20px;
        line-height: 26px;
        margin: 20px 0px;
        width: 300px;
        padding: 10px 12px;
    }

    .interest-find .select2 {
        width: 137px !important;
        margin: 0 2px;
    }

    .step-form {
        bottom: 140px;
        top: 65px;
    }

    .all-dropdown .find-btn {
        width: 120px;
        padding: 10px 20px;
        margin-top: 20px;
    }

    .cemera-icon svg {
        font-size: 35px;
    }

    .chat-section .right-btntext {
        font-size: 16px;
        line-height: 18px;
        margin-top: 15px;
        margin-left: unset;
    }

    .chat-section .right-btntext .add-credits {
        padding: 8px 18px;
    }

    .chat-section .right-btntext .add-token {
        padding: 8px 18px;
    }

    .chating-box .all-file .more-icon {
        font-size: 20px;
    }

    .chating-box .all-file {
        margin-bottom: 10px;
    }

    .chating-box .all-file .file-icon svg {
        font-size: 30px;
    }

    .chating-box .all-file span {
        font-size: 16px;
        line-height: 18px;
        margin-left: 10px;
    }

    .chat-section .profile-pic {
        flex-wrap: wrap;
    }

    .profile-pic .location-date {
        margin-left: 0;
    }

    .full-bg .add-photos {
        padding: 15px 15px;
    }

    .membercomment .dummy-image img {
        height: 300px;
    }

    .all-dropdown .all-field {
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    .category-box {
        padding: 15px 10px;
        margin-bottom: 15px;
    }

    .interest-find span {
        line-height: 20px;
        margin-bottom: 6px;
    }

    .category-box ul li a {
        font-size: 16px;
        line-height: 20px;
    }

    .select2-container .selection .select2-selection {
        padding: 5px 8px;
        font-size: 16px;
        line-height: 18px;
    }

    .button .btn {
        padding: 6px 10px;
        margin-top: 6px;
    }

    .header .buynow {
        padding: 6px 6px;
    }

    .buynow .icon {
        width: 26px;
    }

    .buynow .buy-btn {
        padding: 6px 8px;
    }

    .pagination .page-item .world.page-link .fa-angles-left {
        margin-right: 8px;
    }

    .pagination .page-item .world.page-link .fa-angles-right {
        margin-left: 8px;
    }

    .buynow .text-wallet {
        width: 106px;
        margin: 0px 8px;
    }

    .favorite-sendmess .image-text .name {
        font-size: 18px;
        line-height: 22px;
    }

    .favorite-sendmess .image-text .text {
        padding: 8px 10px 12px;
    }

    .favorite-sendmess .image-text .tag span {
        padding: 2px 14px;
    }

    .pagination .page-item .page-link {
        width: 32px;
        height: 31px;
        font-size: 16px;
        line-height: 18px;
    }

    .pagination .page-item.previ {
        margin-right: 15px;
    }

    .pagination .page-item.next {
        margin-left: 15px;
    }

    .age ul li {
        width: 28px;
        height: 28px;
        font-size: 14px;
        line-height: 22px;
        margin: 0 10px;
    }

    .chat-section .nav-tabs .nav-item .nav-link {
        padding: 6px 14px;
    }

    .chating-box .comment-box {
        padding: 10px 6px;
        flex-wrap: wrap;
    }

    .all-text .name-time {
        flex-direction: column;
        align-items: baseline;
    }

    .chating-box .comment-box .image {
        margin-bottom: 10px;
        margin-right: 0;
    }

    .all-text .name-time .name {
        margin-bottom: 5px;
    }

    .chating-box .comment-box br {
        display: none;
    }

    .chating-box .chat-detail {
        padding: 12px 12px 12px;
        height: 580px;
    }

    .chating-box.archived-box {
        height: auto;
    }

    .chat-detail .right-message .right-icon {
        left: 0px;
        font-size: 14px;
    }

    .chat-detail .message-box .text-box textarea {
        padding: 10px;
    }

    .img-send .image {
        font-size: 16px;
        line-height: 20px;
    }

    .chating-box .all-commentbox {
        height: 705px;
    }

    .chat-section .nav-tabs {
        margin-bottom: 15px;
    }

    .img-send .emoji .btn-drop img {
        width: 20px;
    }

    .emoji .dropdown-toggle::after {
        right: -8px;
    }

    .img-send .emoji .btn-drop {
        padding: 2px 4px;
    }

    .img-send .send-message {
        padding: 8px 12px;
    }

    .message-box .img-send,
    .dont-say .img-send {
        flex-direction: column;
    }

    .s-message .message-box .img-send {
        flex-direction: row;
    }

    .chat-detail .message-box {
        padding: 12px 12px 10px;
    }

    .chating-box .chat-detail .all-message {
        height: 345px;
    }

    .message-box .img-send .photo-emoji {
        margin-bottom: 10px;
    }

    .full-profile .profile {
        margin-bottom: 15px;
    }

    .profile .h-title h5 {
        font-size: 22px;
        line-height: 26px;
        margin-bottom: 8px;
    }

    .profile .h-title h5 span {
        padding: 4px 8px;
        font-size: 14px;
        line-height: 16px;
    }

    .profile .right-text .year-wilts {
        margin-bottom: 8px;
    }

    .profile .button {
        margin-top: 15px;
    }

    .full-profile ul li {
        margin-bottom: 10px;
    }

    .full-profile ul li img {
        margin-right: 10px;
    }

    .img-check .text .head h5 {
        font-size: 30px;
        line-height: 32px;
    }

    .img-check .text span {
        font-size: 20px;
        line-height: 22px;
    }

    .full-profile .private-img .s-message {
        margin-bottom: 15px;
        height: auto;
    }

    .full-profile .private-img .s-message p br {
        display: none;
    }

    .img-check .text p {
        font-size: 16px;
        line-height: 18px;
    }

    .shy-type .title h4 {
        font-size: 20px;
        line-height: 24px;
        margin-bottom: 15px;
    }

    .shy-type .conversation {
        margin-bottom: 15px;
        height: auto;
    }

    .send-btn {
        font-size: 16px;
        line-height: 21px;
        padding: 8px 20px;
    }

    .shy-type .thing span {
        font-size: 16px;
        line-height: 18px;
        margin-bottom: 10px;
    }

    .full-profile .shy-type {
        margin-bottom: 20px;
    }

    .heading {
        font-size: 25px;
        line-height: 30px;
    }

    .near-you .heading {
        margin-bottom: 10px;
    }

    .near-you .image-text {
        margin-bottom: 15px;
    }

    .near-you .text {
        padding: 8px 12px 15px;
    }

    .full-profile .private-img {
        margin: 15px 0px;
    }

    .photo-more .check-out {
        padding: 10px;
    }

    .near-you .tag span {
        padding: 4px 14px;
    }

    .chat-starter .select2 {
        font-size: 16px;
        line-height: 18px;
    }

    .near-you .name {
        font-size: 18px;
        line-height: 22px;
    }

    .near-you {
        margin-bottom: 25px;
    }

    .delete-profile .accordion-button::after {
        width: 30px;
        height: 30px;
    }

    .delete-profile .accordion-button.head {
        padding-left: 35px;
        margin-bottom: 15px;
    }

    .all-password .form-label {
        font-size: 16px;
        line-height: 20px;
    }

    .text-pro {
        padding-left: 30px;
    }

    .all-password .form-group .form-control {
        font-size: 16px;
        line-height: 20px;
        padding: 10px 12px;
    }

    .all-password .form-group {
        margin-bottom: 15px;
    }

    .all-password {
        padding: 25px 20px;
    }

    .setting .nav-tabs .nav-item .nav-link {
        font-size: 16px;
        line-height: 18px;
        padding: 10px 16px;
    }

    .setting .nav-tabs {
        margin-bottom: 25px;
    }

    .data-profile .button {
        margin-top: 15px;
    }

    .data-profile .request-deta {
        margin-bottom: 20px;
    }

    .setting {
        margin-bottom: 60px;
    }

    .profile-pic .name-online .p-name {
        font-size: 18px;
        line-height: 22px;
    }

    .profile-pic .name-online .online {
        font-size: 14px;
        line-height: 16px;
    }

    .profile-pic .name-online .c-archive {
        font-size: 14px;
        line-height: 16px;
    }

    .profile-pic .p-image img {
        width: 60px;
        height: 60px;
    }

    .uploade-text h4 {
        font-size: 22px;
        line-height: 24px;
        margin-bottom: 8px;
    }

    .uploade-text .photo-btn {
        font-size: 16px;
        line-height: 18px;
        padding: 8px 10px;
        margin-bottom: 5px;
    }

    .uploade-text span {
        margin-bottom: 10px;
    }

    .general-profile .pic-editpic {
        padding-bottom: 10px;
        margin-bottom: 15px;
    }

    span.label-name {
        font-size: 16px;
        line-height: 18px;
    }

    .info-field .selection .select2-selection .select2-selection__rendered,
    .select2-results .select2-results__options .select2-results__option {
        font-size: 16px;
        line-height: 23px;
    }

    .info-field .form-control {
        font-size: 16px;
        line-height: 23px;
        padding: 8px 15px;
    }

    .chat-starter
        .select2-container--default
        .select2-selection--single
        .select2-selection__arrow {
        right: 4px !important;
        top: 8px !important;
    }

    .select2-container--default
        .select2-selection--single[aria-expanded="true"]
        .select2-selection__arrow {
        top: 4px !important;
    }

    .general-profile .yourself {
        margin-top: 10px;
    }

    .yourself .text-box {
        padding: 15px;
    }

    .yourself .save-btn,
    .idea-match .save-btn,
    .my-interests .save-btn {
        font-size: 16px;
        line-height: 19px;
        padding: 8px 18px;
        margin-top: 10px;
    }

    .general-profile {
        padding: 15px 15px;
    }

    .interest-checkbox {
        margin-bottom: 15px;
    }

    .my-interests {
        margin-bottom: 40px;
    }

    .all-password .btn-save {
        padding: 6px 26px 9px;
    }

    .all-password .info ul li {
        font-size: 16px;
        line-height: 18px;
        padding: 10px 10px;
    }

    .info .info-select {
        width: 30%;
    }

    .all-password .info ul li span.text {
        width: 70%;
    }

    .info-select .custom-click {
        font-size: 16px;
        line-height: 20px;
    }

    .info-select .checkmark {
        height: 20px;
        width: 20px;
    }

    .info-select .custom-click .checkmark:after {
        width: 8px;
        height: 8px;
    }

    .pass-page .background {
        padding-top: 114px;
    }

    .tribe-member .pic-text p {
        font-size: 16px;
        line-height: 22px;
    }

    .contributions .add-new,
    .f-message .add-new {
        font-size: 16px;
        line-height: 19px;
        padding: 8px 10px;
    }

    .tribe-member,
    .contributions {
        padding: 20px 15px;
    }

    .add-contraib .profile-name .image img {
        width: 50px;
        height: 50px;
    }

    .contribu-box .detail-date {
        flex-direction: column;
        margin-bottom: 10px;
        align-items: baseline;
    }

    .location-date .name h5 {
        font-size: 20px;
        line-height: 25px;
    }

    .contribu-box .text p {
        font-size: 16px;
        line-height: 18px;
    }

    .contribu-box .button {
        margin-top: 15px;
        text-align: left;
    }

    .contribu-box .button .btn {
        margin-bottom: 10px;
    }

    .contributions .contribu-box {
        padding: 20px 15px;
    }

    .detail-date .p-image {
        width: 30%;
    }

    .memberphoto .dummy-image img {
        height: 195px;
    }

    .contribu-box .location-date {
        width: 100%;
    }

    .f-message .uploade-btn .link {
        margin-bottom: 5px;
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .f-message .uploade-btn {
        display: block;
    }

    .comment-detail .detail-date .p-image {
        width: 16%;
    }

    .comment-detail .location-date {
        width: 87%;
    }

    .comment-detail .name-view .head {
        font-size: 22px;
        line-height: 28px;
    }

    .comment-detail .name-view {
        margin-bottom: 12px;
    }

    .comment-detail .detail-date {
        margin-bottom: 12px;
    }

    .comment-detail .text p {
        font-size: 16px;
        line-height: 20px;
    }

    .comment-detail .location-date .name h5 {
        margin-bottom: 4px;
    }

    .comment-detail,
    .latest-comment {
        padding: 20px 15px;
    }

    .latest-comment {
        margin-bottom: 40px;
    }

    .comment-text .comment-time {
        padding: 10px;
        margin-bottom: 15px;
    }

    .comment-text .name h6 {
        font-size: 18px;
        line-height: 20px;
    }

    .comment-text .comment-time .time span {
        font-size: 16px;
        line-height: 18px;
    }

    .comment-text .image {
        width: 13%;
    }

    .comment-text .name-context {
        width: 87%;
    }

    .comment-text .name-context .text p {
        font-size: 15px;
        line-height: 18px;
    }

    .comment-model .modal-dialog .modal-body .message {
        padding: 12px;
        height: 190px;
    }

    .modal-content .modal-header .btn-close {
        font-size: 16px;
        padding: 4px;
    }

    .modal .modal-content {
        padding: 15px 12px;
    }

    .pay-method {
        padding: 25px 20px;
        margin-bottom: 40px;
    }

    .choose-bundle .token span {
        font-size: 18px;
        line-height: 20px;
    }

    .choose-bundle .token img {
        width: 8%;
    }


    .prefferred ul li a {
        width: 85px;
        height: 48px;
    }

    .choose-bundle .token .m-tag {
        padding: 8px 15px;
    }

    .prefferred ul {
        margin-bottom: 15px;
    }

    .card-detail .form-group .form-control {
        font-size: 16px;
        line-height: 18px;
        padding: 12px 15px;
    }

    .card-detail .btn-pay {
        padding: 10px 12px;
    }

    .card-detail p {
        font-size: 16px;
        line-height: 18px;
    }

    .failed-message .fpay-icon {
        width: 135px;
        height: 135px;
        padding: 10px;
    }

    .failed-message .head {
        font-size: 24px;
        line-height: 28px;
        margin-bottom: 10px;
    }

    .my-tribe .link-block {
        margin-bottom: 15px;
    }

    .all-tribes .chat-groups .link-block {
        margin-bottom: 15px;
    }

    .chat-groups .link-block span {
        margin-top: 8px;
    }

    .my-tribe {
        padding: 25px 20px;
    }

    .contact-us {
        padding: 25px 25px;
        margin-bottom: 30px;
    }

    .contact-us .text-detail {
        margin-bottom: 25px;
    }

    .text-detail .btn-send {
        padding: 10px 20px;
    }

    .play-box .image {
        width: 22%;
    }

    .play-box .title-text .title {
        font-size: 16px;
        line-height: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .important .play-box .title-text {
        width: 48%;
    }

    .play-box .title-text .small p {
        width: 100px;
    }

    .play-box .p-icon {
        width: 14%;
    }

    .play-box .title-text {
        width: 60%;
    }

    .chating-box .play-box .image img {
        width: 45px;
        height: 45px;
    }

    .favourites .image-text .star {
        padding-left: 10px;
        padding-top: 6px;
        font-size: 22px;
    }
}

@media (min-width: 320px) and (max-width: 575px) {
    .top-bar .main-close {
        right: 8px;
        font-size: 20px;
    }

    .top-bar {
        padding: 8px 8px;
    }

    .navbar {
        padding: 8px 0px;
    }

    .navbar-brand span {
        font-size: 26px;
        line-height: 40px;
    }

    .all-filed .btn {
        padding: 8px 6px;
        font-size: 18px;
        line-height: 23px;
    }

    .login .btn {
        padding: 3px 24px 4px;
        font-size: 16px;
        line-height: 25px;
    }

    .heading {
        font-size: 25px;
        line-height: 30px;
        margin-bottom: 12px;
    }

    .footer .copyright-link p {
        margin-bottom: 10px;
    }

    .signup-form .nav-tabs .nav-item .nav-link {
        padding: 4px 14px;
        line-height: 20px;
        font-size: 14px;
    }

    .signup-form .all-btn {
        padding: 12px 0px;
    }

    .signup-form .nav-tabs span {
        margin-bottom: 6px;
        font-size: 16px;
        line-height: 22px;
    }

    .signup-form .nav-tabs {
        margin-bottom: 10px;
    }

    .signup-form .interested {
        padding: 12px 10px;
    }

    .age-intrest label {
        line-height: 22px;
    }

    .all-filed label {
        font-size: 16px;
        line-height: 22px;
    }

    .signup-form .age-intrest {
        padding: 4px 10px;
        width: 118px;
    }

    .signup-form .all-filed .age-intrest {
        padding: 4px 5px;
    }

    .signup-form .all-filed {
        padding: 12px 10px;
    }

    .signup-form .all-filed .form-control {
        padding: 6px 8px;
    }

    .copyright-link .all-menu li {
        margin: 0 12px;
    }

    .copyright-link .all-menu {
        justify-content: center;
    }

    .form-signup .signup-form .btn-recover {
        font-size: 18px;
        line-height: 22px;
        padding: 8px 22px;
        margin-top: 10px;
    }

    .form-signup .signup-form .mail .form-control {
        padding: 6px 8px;
    }

    .small-head {
        font-size: 22px;
        line-height: 30px;
        margin-bottom: 10px;
    }

    .sign-up .full-form .signup-form {
        padding: 15px 15px;
    }

    .footer {
        padding: 15px 0px;
    }

    .checkmark {
        width: 22px;
        height: 22px;
    }

    .custom-check .checkmark:after {
        top: 0px;
        left: 6px;
    }

    .form-signup {
        bottom: 235px;
    }

    .confirm-account {
        padding: 20px 15px;
        margin-bottom: 20px;
    }

    .confirm-account .account-btn {
        padding: 10px 24px;
        font-size: 16px;
        line-height: 26px;
    }

    .verification {
        padding: 20px 15px;
        margin-bottom: 15px;
    }

    .head {
        margin-bottom: 15px;
        font-size: 20px;
        line-height: 26px;
    }

    .verification p {
        font-size: 16px;
        line-height: 20px;
    }

    .phone-number .form-control {
        padding: 8px 8px;
        line-height: 22px;
        font-size: 16px;
    }

    .phone-number .send-sms {
        font-size: 16px;
        line-height: 20px;
        padding: 8px 12px;
    }

    .confirm-account p {
        margin-bottom: 25px;
        font-size: 18px;
        line-height: 22px;
    }

    .girls-image::after {
        width: 245px;
        height: 245px;
        top: 40px;
    }

    .all-step .heading {
        padding-top: 12px;
        padding-bottom: 18px;
        font-size: 22px;
        line-height: 24px;
    }

    #multistepsform fieldset p span {
        font-size: 18px;
        line-height: 24px;
        padding: 6px 12px;
    }

    #multistepsform fieldset p {
        font-size: 18px;
        line-height: 22px;
    }

    #multistepsform .step-title {
        font-size: 18px;
        line-height: 22px;
    }

    #multistepsform fieldset .all-img .image span {
        font-size: 18px;
        line-height: 24px;
        margin-top: 8px;
    }

    #multistepsform .action-button {
        font-size: 20px;
        line-height: 26px;
        margin: 20px 0px;
        width: 250px;
        padding: 10px 12px;
    }

    .interest-find .select2 {
        width: 137px !important;
        margin: 0 2px;
    }

    .step-form {
        bottom: 190px;
        top: 55px;
    }

    .chat-section .right-btntext {
        font-size: 16px;
        line-height: 18px;
        flex-wrap: wrap;
        margin-left: 0;
    }

    .chat-section .right-btntext .add-credits {
        padding: 6px 14px;
        margin: 0px 5px;
    }

    .chat-section .right-btntext .add-token {
        padding: 6px 14px;
        margin: 0px 5px;
    }

    .chating-box.archived-box {
        height: auto;
    }

    .chating-box .all-file .file-icon svg {
        font-size: 30px;
    }

    .chating-box .all-file span {
        font-size: 16px;
        line-height: 18px;
        margin-left: 10px;
    }

    .chating-box .all-file {
        margin-bottom: 10px;
    }

    .chating-box .all-file .more-icon {
        font-size: 18px;
        right: 15px;
    }

    .add-contraib .f-message {
        display: block;
    }

    .cemera-icon svg {
        font-size: 35px;
        margin-bottom: 6px;
    }

    .full-bg .add-photos {
        padding: 10px 10px;
    }

    .f-message .uploade-btn .link {
        width: 35px;
        height: 35px;
    }

    .cemera-icon .button .btn-favorite {
        width: auto;
    }

    .all-dropdown .find-btn {
        width: 125px;
        padding: 8px 16px;
        margin-top: 20px;
    }

    .all-dropdown .all-field {
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    .category-box {
        padding: 15px 10px;
        margin-bottom: 15px;
    }

    .interest-find span {
        font-size: 16px;
        line-height: 18px;
        margin-bottom: 5px;
    }

    .category-box ul li a {
        font-size: 16px;
        line-height: 20px;
        padding: 6px 8px;
    }

    .select2-container .selection .select2-selection {
        padding: 3px 6px;
        font-size: 16px;
        line-height: 18px;
    }

    .title span {
        font-size: 16px;
        line-height: 18px;
    }

    .all-dropdown .title {
        margin-bottom: 6px;
    }

    .button .btn {
        padding: 6px 9px;
        margin-top: 5px;
        font-size: 14px;
        line-height: 16px;
    }

    .header .buynow {
        padding: 6px 8px;
        margin-bottom: 10px;
        margin-left: 0;
    }

    .buynow .icon {
        width: 26px;
    }

    .all-dropdown {
        padding: 8px 0px;
    }

    .navbar .buy-nowall {
        flex-wrap: wrap;
        justify-content: center;
        margin-top: 10px;
    }

    .buynow .buy-btn {
        padding: 6px 8px;
        font-size: 14px;
        line-height: 16px;
    }

    .pagination .page-item .world.page-link .fa-angles-left {
        margin-right: 5px;
    }

    .pagination .page-item .world.page-link .fa-angles-right {
        margin-left: 5px;
    }

    .buynow .text-wallet {
        width: 106px;
        margin: 0px 8px;
    }

    .favorite-sendmess .image-text .name {
        font-size: 16px;
        line-height: 20px;
    }

    .favorite-sendmess .image-text .text {
        padding: 6px 6px 10px;
    }

    .favorite-sendmess .image-text .tag span {
        padding: 2px 14px;
    }

    .pagination .page-item .page-link {
        width: 28px;
        height: 29px;
        font-size: 16px;
        line-height: 18px;
    }

    .pagination .page-item.previ {
        margin-right: 5px;
    }

    .pagination .page-item.next {
        margin-left: 5px;
    }

    .age ul li {
        width: 28px;
        height: 28px;
        font-size: 14px;
        line-height: 22px;
        margin: 0 10px;
    }

    .pagination .page-item .world.page-link {
        padding: 2px;
    }

    /* .chat-section .nav-tabs .nav-item {
        margin-bottom: 8px;
    } */

    .chat-section .nav-tabs {
        margin-bottom: 10px;
    }

    .chating-box .comment-box {
        padding: 10px 6px;
        flex-wrap: wrap;
    }

    .all-text .name-time {
        flex-direction: column;
        align-items: baseline;
    }

    .chating-box .comment-box .image {
        margin-bottom: 10px;
        margin-right: 0;
    }

    .all-text .name-time .name {
        margin-bottom: 5px;
    }

    .chating-box .comment-box br {
        display: none;
    }

    .chat-section .chating-box {
        border-radius: 0;
    }

    .chatmessages {
        border-radius: 15px;
    }

    .chat-footer {
        border: none;
        padding: 5px!important;
        padding-top: 5px!important;
    }

    .chating-box .chat-detail {
        padding: 12px 12px 12px;
        height: auto;
    }

    .chat-section .profile-pic {
        flex-wrap: wrap;
        margin-bottom: 10px;
    }

    .profile-pic .location-date {
        margin-left: 0;
    }

    .profile-pic .location-date span {
        font-size: 16px;
        line-height: 22px;
    }

    .chat-detail .message-box .text-box textarea {
        padding: 10px;
    }

    .img-send .image {
        font-size: 16px;
        line-height: 20px;
    }

    .chating-box .all-commentbox {
        height: 520px;
        margin-bottom: 25px;
    }

    .img-send .emoji .btn-drop {
        padding: 2px 4px;
    }

    .membercomment .dummy-image img {
        height: 200px;
    }

    .emoji .dropdown-toggle::after {
        right: -8px;
    }

    .img-send .emoji .btn-drop img {
        width: 20px;
    }

    .img-send .send-message {
        padding: 6px 12px;
        font-size: 14px;
        line-height: 16px;
    }

    .message-box .img-send,
    .dont-say .img-send {
        flex-direction: column;
    }

    .chat-detail .message-box {
        padding: 10px 0px 10px;
        position: relative;
    }

    .chating-box .chat-detail .all-message {
        height: 435px;
    }

    .message-box .img-send .photo-emoji {
        margin-bottom: 10px;
    }

    .full-profile .profile {
        margin-bottom: 15px;
        padding: 20px 15px;
    }

    .profile .h-title h5 {
        font-size: 20px;
        line-height: 22px;
        margin-bottom: 8px;
    }

    .profile .h-title h5 span {
        padding: 4px 8px;
        font-size: 14px;
        line-height: 16px;
    }

    .profile .right-text .year-wilts {
        margin-bottom: 8px;
        font-size: 14px;
        line-height: 16px;
    }

    .profile .button {
        margin-top: 10px;
    }

    .full-profile ul li {
        margin-bottom: 6px;
        font-size: 14px;
        line-height: 16px;
    }

    .profile .right-text p {
        line-height: 22px;
    }

    .full-profile .interest {
        padding: 20px 15px;
        height: auto;
    }

    .full-profile ul li img {
        margin-right: 6px;
    }

    .img-check .text .head h5 {
        font-size: 30px;
        line-height: 32px;
    }

    .full-profile .private-img .s-message p {
        font-size: 18px;
        line-height: 26px;
    }

    .private-img .img-check {
        height: auto;
        margin-bottom: 15px;
    }

    .img-check .text span {
        font-size: 20px;
        line-height: 22px;
    }

    .full-profile .private-img .s-message {
        padding: 15px 10px 10px;
        margin-bottom: 15px;
        height: auto;
    }

    .img-check .text p {
        font-size: 16px;
        line-height: 18px;
    }

    .shy-type .title h4 {
        font-size: 18px;
        line-height: 22px;
        margin-bottom: 10px;
    }

    .shy-type .conversation {
        margin-bottom: 15px;
        height: auto;
        padding: 20px 15px;
    }

    .send-btn {
        font-size: 16px;
        line-height: 21px;
        padding: 6px 18px;
    }

    .shy-type .thing {
        padding: 20px 15px;
    }

    .shy-type .thing span {
        font-size: 16px;
        line-height: 18px;
        margin-bottom: 10px;
    }

    .full-profile .shy-type {
        margin-bottom: 15px;
    }

    .heading {
        font-size: 24px;
        line-height: 28px;
    }

    .near-you .heading {
        margin-bottom: 8px;
    }

    .near-you .image-text {
        margin-bottom: 15px;
    }

    .near-you .text {
        padding: 6px 8px 10px;
    }

    .full-profile .private-img {
        margin: 15px 0px;
    }

    .photo-more .check-out {
        flex-direction: column;
        align-items: self-start;
        padding: 10px;
    }

    .photo-more span {
        font-size: 18px;
        line-height: 20px;
        width: 35px;
        height: 35px;
    }

    .near-you .tag span {
        padding: 2px 12px;
    }

    .chat-starter .select2 {
        font-size: 16px;
        line-height: 18px;
    }

    .all-password .form-group {
        margin-bottom: 12px;
    }

    .near-you .name {
        font-size: 18px;
        line-height: 20px;
    }

    .near-you {
        margin-bottom: 10px;
    }

    .delete-profile .accordion-button::after {
        width: 30px;
        height: 30px;
    }

    .delete-profile .accordion-button.head {
        padding-left: 35px;
        margin-bottom: 15px;
        font-size: 18px;
        line-height: 24px;
    }

    .all-password {
        padding: 20px 15px;
    }

    .all-password .form-label {
        font-size: 16px;
        line-height: 20px;
        margin-bottom: 10px;
    }

    .all-password .form-group .form-control {
        font-size: 16px;
        line-height: 20px;
        padding: 8px 12px;
    }

    .all-password .btn-save {
        font-size: 16px;
        line-height: 19px;
        padding: 7px 28px 9px;
    }

    .setting .nav-tabs .nav-item .nav-link {
        font-size: 16px;
        line-height: 18px;
        padding: 8px 14px;
    }

    .setting .nav-tabs {
        margin-bottom: 15px;
    }

    .data-profile .button {
        margin-top: 15px;
    }

    .data-profile {
        padding: 20px 15px;
    }

    .request-deta p {
        font-size: 16px;
        line-height: 18px;
    }

    .data-profile .request-deta {
        margin-bottom: 20px;
    }

    .setting {
        margin-bottom: 45px;
    }

    .text-pro {
        font-size: 16px;
        line-height: 18px;
        padding-left: 30px;
        margin-bottom: 10px;
    }

    .text-pro .checkmark {
        height: 20px;
        width: 20px;
    }

    .text-pro .checkmark:after {
        width: 8px;
        height: 8px;
    }

    .data-profile .button .btn-deta {
        padding: 8px 18px;
    }

    .profile-pic .p-image img {
        width: 60px;
        height: 60px;
    }

    .profile-pic .name-online .p-name {
        font-size: 18px;
        line-height: 22px;
    }

    .profile-pic .name-online .online {
        padding: 4px 8px;
        font-size: 14px;
        line-height: 16px;
    }

    .profile-pic .name-online .c-archive {
        padding: 4px 8px;
        font-size: 14px;
        line-height: 16px;
    }

    .uploade-text h4 {
        font-size: 22px;
        line-height: 24px;
        margin-bottom: 8px;
        margin-top: 10px;
    }

    .uploade-text .photo-btn {
        font-size: 16px;
        line-height: 18px;
        padding: 8px 10px;
        margin-bottom: 5px;
    }

    .uploade-text span {
        margin-bottom: 10px;
        font-size: 16px;
        line-height: 18px;
    }

    .pic-editpic .group-img img {
        width: 77px;
        height: 77px;
    }

    .general-profile .pic-editpic {
        padding-bottom: 5px;
        margin-bottom: 10px;
    }

    span.label-name {
        font-size: 16px;
        line-height: 18px;
        margin-bottom: 10px;
    }

    .info-field .selection .select2-selection .select2-selection__rendered,
    .select2-results .select2-results__options .select2-results__option {
        font-size: 16px;
        line-height: 30px;
    }

    .info-field .form-control {
        font-size: 16px;
        line-height: 25px;
        padding: 8px 15px;
    }

    .chat-starter
        .select2-container--default
        .select2-selection--single
        .select2-selection__arrow {
        right: 0px !important;
        top: 6px !important;
    }

    .chat-starter
        .select2-container--default
        .select2-selection--single[aria-expanded="true"]
        .select2-selection__arrow {
        top: 4px !important;
        right: 6px !important;
    }

    .select2-container--default
        .select2-selection--single[aria-expanded="true"]
        .select2-selection__arrow {
        top: 1px !important;
    }

    .general-profile .yourself {
        margin-top: 10px;
    }

    .yourself .text-box {
        padding: 15px;
    }

    .yourself .save-btn,
    .idea-match .save-btn,
    .my-interests .save-btn {
        font-size: 16px;
        line-height: 19px;
        padding: 6px 16px;
        margin-top: 10px;
    }

    .general-profile {
        padding: 15px 15px;
    }

    .idea-match,
    .my-interests {
        padding: 15px 15px;
    }

    .interest-checkbox {
        margin-bottom: 15px;
        font-size: 16px;
        line-height: 23px;
    }

    .my-interests {
        margin-bottom: 30px;
    }

    .all-password .info ul li {
        flex-direction: column;
        align-items: baseline;
        font-size: 16px;
        line-height: 18px;
    }

    .all-password .info ul li span.text,
    .info .info-select {
        width: auto;
    }

    .all-password .info ul li span.text {
        margin-bottom: 10px;
    }

    .info-select .custom-click {
        font-size: 16px;
        line-height: 20px;
        padding-left: 26px;
    }

    .info-select .checkmark {
        height: 20px;
        width: 20px;
    }

    .info-select .custom-click .checkmark:after {
        width: 8px;
        height: 8px;
    }

    .pass-page .background {
        padding-top: 180px;
    }

    .tribe-member .text-count {
        flex-direction: column;
        align-items: baseline;
    }

    .tribe-member,
    .contributions {
        padding: 20px 15px;
    }

    .tribe-member .pic-text .image {
        margin-bottom: 15px;
    }

    .tribe-member .pic-text p {
        font-size: 16px;
        line-height: 22px;
    }

    .contributions .head {
        margin-bottom: 10px;
    }

    .contribu-box .detail-date {
        flex-direction: column;
        margin-bottom: 10px;
        align-items: baseline;
    }

    .location-date .name h5 {
        font-size: 20px;
        line-height: 23px;
    }

    .contributions .contribu-box {
        padding: 20px 15px;
    }

    .contribu-box .text p {
        font-size: 16px;
        line-height: 18px;
    }

    .contribu-box .button {
        margin-top: 15px;
        text-align: left;
    }

    .contribu-box .button .btn {
        margin-bottom: 10px;
    }

    .detail-date .p-image {
        width: 30%;
    }

    .contribu-box .location-date {
        width: 100%;
    }

    .contributions .add-new,
    .f-message .add-new {
        font-size: 14px;
        line-height: 16px;
        padding: 6px 10px;
    }

    .contributions .button {
        text-align: left;
    }

    .add-contraib .profile-name .image img {
        width: 50px;
        height: 50px;
    }

    .add-contraib .location-date {
        text-align: left;
        margin-top: 10px;
    }

    .message-add textarea {
        font-size: 16px;
        line-height: 22px;
    }

    .comment-detail {
        padding: 20px 15px;
    }

    .comment-detail .name-view .head {
        font-size: 20px;
        line-height: 26px;
    }

    .view-comment .view,
    .view-comment .comment {
        font-size: 14px;
        line-height: 16px;
    }

    .view-comment .view {
        margin-right: 5px;
    }

    .comment-detail .name-view {
        margin-bottom: 10px;
    }

    .comment-detail .detail-date .p-image {
        width: 20%;
        margin-right: 8px;
    }

    .detail-date .p-image img {
        width: 50px;
        height: 50px;
    }

    .comment-detail .location-date {
        width: 75%;
    }

    .comment-detail .detail-date {
        margin-bottom: 10px;
    }

    .chating-box .chat-detail {
        height: auto;
    }

    .comment-detail .location-date .name h5 {
        margin-bottom: 2px;
    }

    .comment-detail .text p {
        font-size: 16px;
    }

    .latest-comment {
        padding: 20px 15px;
        margin-bottom: 30px;
    }

    .comment-text .comment-time {
        padding: 15px;
        margin-bottom: 15px;
    }

    .comment-text .image {
        width: 22%;
        margin-right: 0;
        margin-bottom: 5px;
    }

    .comment-text .image img {
        width: 50px;
        height: 50px;
    }

    .comment-text .comment-time {
        flex-direction: column;
    }

    .comment-text .name-time {
        flex-direction: column;
        align-items: baseline;
    }

    .comment-text .name-context {
        width: 100%;
    }

    .comment-text .name h6 {
        font-size: 18px;
        line-height: 20px;
    }

    .comment-text .comment-time .time span {
        font-size: 16px;
        line-height: 18px;
    }

    .comment-text .name-context .text p {
        font-size: 16px;
        line-height: 18px;
    }

    .modal .modal-content {
        padding: 15px 10px;
    }

    .modal-content .modal-header .btn-close {
        padding: 4px;
        font-size: 16px;
    }

    .modal-dialog .modal-content .modal-header {
        top: 12px;
    }

    .comment-model .modal-dialog .modal-body .message {
        padding: 10px;
        height: 170px;
    }

    .comment-model .modal-body .message textarea {
        font-size: 16px;
        line-height: 18px;
    }

    .comment-model .button span {
        font-size: 16px;
        line-height: 18px;
        margin-top: 10px;
    }

    .comment-model .button {
        flex-direction: column;
    }

    .pay-method {
        padding: 20px 15px;
        margin-bottom: 30px;
    }

    .choose-bundle .token .m-tag {
        font-size: 14px;
        line-height: 16px;
        padding: 5px 10px;
    }


    .choose-bundle .token img {
        width: 12%;
    }

    .choose-bundle .token span {
        font-size: 16px;
        line-height: 18px;
    }

    .choose-bundle .token svg {
        font-size: 22px;
    }

    .prefferred ul li a {
        width: 80px;
        height: 45px;
    }

    .prefferred ul li {
        margin-bottom: 5px;
    }

    .prefferred ul {
        margin-bottom: 15px;
    }

    .card-detail .form-group {
        margin-bottom: 10px;
    }

    .card-detail .form-group .form-control {
        font-size: 16px;
        line-height: 18px;
        padding: 12px 15px;
    }

    .card-detail .btn-pay {
        font-size: 16px;
        line-height: 19px;
        padding: 10px 12px;
    }

    .card-detail p br {
        display: none;
    }

    .card-detail p {
        font-size: 16px;
        line-height: 18px;
    }

    .failed-message .fpay-icon {
        width: 110px;
        height: 110px;
        margin: 0 auto 10px;
        padding: 10px;
    }

    .failed-message .head {
        font-size: 25px;
        line-height: 28px;
        margin-bottom: 10px;
    }

    .failed-message p {
        font-size: 16px;
        line-height: 18px;
    }

    .my-tribe .link-block {
        margin-bottom: 12px;
    }

    .all-tribes .chat-groups .link-block {
        margin-bottom: 12px;
    }

    .chat-groups .link-block span {
        margin-top: 6px;
    }

    .my-tribe {
        padding: 20px 20px;
    }

    .contact-us {
        padding: 20px 20px;
        margin-bottom: 25px;
    }

    .contact-us .text-detail {
        margin-bottom: 15px;
    }

    .text-detail .btn-send {
        padding: 8px 18px;
    }

    .text-detail .form-group .form-control {
        font-size: 16px;
        line-height: 22px;
        padding: 8px 16px;
    }

    .contact-us .text-detail .text p {
        font-size: 16px;
        line-height: 20px;
    }

    .text-detail .form-group {
        margin-bottom: 10px;
    }

    .play-box .image {
        width: 18%;
    }

    .play-box .title-text .title {
        font-size: 16px;
        line-height: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .important .play-box .title-text {
        width: 62%;
    }

    .play-box .title-text .small p {
        width: 175px;
    }

    .play-box .p-icon {
        width: 9%;
    }

    .play-box .title-text {
        width: 70%;
    }

    .chating-box .play-box .image img {
        width: 45px;
        height: 45px;
    }

    .favourites .image-text .star {
        padding-left: 10px;
        padding-top: 6px;
        font-size: 20px;
    }
}

/*---------- Large Desktop , Large Screen End ----------*/

@media (max-width: 992px) {
    .chating-box .left-message {
        max-width: 85%;
    }
    .chating-box .right-message {
        max-width: 85%;
    }

    .message-list {
        margin-bottom: 120px;
    }

    body.page-activation .content
    {
        padding-top: 68px;
    }
}

#mobile-menu .modal-content .btn-close {
    background: var(--color-2);
    padding: 5px;
    opacity: 1;
    position: absolute;
    right: 10px;
    top: 10px;
    color: var(--color-4);
    font-size: 20px;
    border-radius: 25px;
    -webkit-border-radius: 25px;
    -moz-border-radius: 25px;
    -ms-border-radius: 25px;
    -o-border-radius: 25px;
}

#mobile-menu .modal-content .btn-close::before {
    content: "\f00d";
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    font-weight: 900;
    font-family: "Font Awesome 6 free";
}

@media (min-width: 320px) and (max-width: 575px) {
    #mobile-menu .modal-content {
        padding: 10px 15px;
    }
    .page-chat footer {
        display: none;
    }


    .choose-bundle .token {
        padding: 10px 10px;
    }

    #store .product-description .credits {
        font-size: 2.2em;
    }
    #store .product-description .credit-text {
        font-size: 0.8em;
    }

    #store .product-discount .discount {
        font-size: 1.5em;
    }
    #store .ppm-text {
        font-size: 0.7em;
    }

    .product-change {
        font-size: 0.8em;
    }

    .btn-pay {
        font-size: 18px;
        line-height: 20px;
        padding: 12px!important;
    }
}

@media (min-width: 320px) and (max-width: 420px) {
    #store .product-description .credits {
        font-size: 1.6em;
    }
    #store .product-description .credit-text {
        font-size: 0.8em;
    }

    #store .product-discount .discount {
        font-size: 1.4em;
    }
    #store .ppm-text {
        font-size: 0.7em;
    }

    .product-change .final-price {
        line-height: 60px;
    }

    .btn-buynow {
        font-size: 0.8em;
    }

    .payment-text {
        font-size: 0.8em;
    }
}

@media (max-width: 1585px) {
    .edge-left,
    .edge-right {
        display: none!important;
    }

}

.page-messages .form-check-input:checked{
    background-color: var(--color-2);
    border-color: var(--color-2);
}

.page-messages .form-check-input:focus{
    box-shadow: none;
    border-color: rgba(0,0,0,.25);
}