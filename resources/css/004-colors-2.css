:root {
    --border-1: 1px solid #dee2e6;
    --border-2: 1px solid #c82a57;
    --border-3: 1px solid #9d9d9d;
    --border-important: 2px rgb(248, 108, 0) solid;
    --bg-main: #f1f2f6;
    --bg-main: #e0e0e0;
    --bg-primary: #000000;
    --bg-secondary: #ffffff;
    --bg-tertiary: #ffffff;
    --bg-interests-selected: #c82a5793;
    --bg-read: white;
    --bg-unread: #f1f2f6;
    --bg-tertiary-transparent: #00000013;
    --bg-soft: #f1f2f6;
    --slider-gradient: linear-gradient(166deg, #321691 0%, #321691 100%);
    --slider-bg: #3316914c;
    --accent-primary-gradient: linear-gradient(166deg,
            #ec366a 0%,
            #a22649 100%);
    --accent-primary-gradient-ios: -o-linear-gradient(166deg,
            rgb(242, 40, 118) 0%,
            rgb(148, 45, 217) 100%);
    --accent-action-gradient: linear-gradient(166deg,
            rgb(65, 216, 0) 0%,
            #4e9b01 100%);
    --vip-gradient: linear-gradient(-51deg,
            rgb(246, 100, 188) 0%,
            rgb(251, 123, 188) 35%,
            rgb(255, 146, 187) 100%);
    --accent-primary: #c82a57;
    --accent-secondary: #c82a57;
    --vip-gradient: linear-gradient(-51deg,
            rgb(246, 100, 188) 0%,
            rgb(251, 123, 188) 35%,
            rgb(255, 146, 187) 100%);
    --buy-gradient: linear-gradient(-51deg,
            rgb(246, 100, 188) 0%,
            rgb(251, 123, 188) 35%,
            rgb(255, 146, 187) 100%);
    --important-gradient: linear-gradient(-51deg,
            rgb(255, 106, 0) 0%,
            rgb(255, 111, 0) 35%,
            rgb(234, 88, 3) 100%);
    --important-gradient-2: linear-gradient(166deg,
            rgb(121, 187, 40) 0%,
            rgb(55, 139, 19) 100%);
    --text-light: white;
    --text-lighter: rgb(108, 108, 108);
    --text-dark: #000000;
    --color-logo: white;
    --color-male: rgba(173, 216, 230, 0.425);
    --border-color-male: rgba(35, 105, 128, 0.42);
    --color-female: #f6e9f99a;
    --border-color-female: #c871a699;
    --color-online: rgb(114, 179, 83);
    --color-important: rgb(248, 108, 0);
    --color-icon-1: #c22551;
    --bs-accordion-bg: rgba(0, 0, 0, 0);
    --bs-link-color: var(--accent-secondary);
    --bs-accordion-active-color: black !important;
    --bg-profile-page-menu: white;
    --color-profile-page-menu: black;
    --blink-color-1a: rgba(255, 255, 255, 0.6);
    --blink-color-1b: rgba(255, 255, 255, 1);
    --bg-message-member: white;
    --bg-filter: white;
    --bg-special-offer: #fdfdfd;
    --bg-popup-header: #e9e8ff;
    --bg-popup-header: #ffffff;
    --color-male: #5ac1f2;
    --color-female: #f576ab;
    --color-male-2: #2a5569;
    --color-female-2: #944465;
    --accent-primary-2: #c82a57;
    --accent-secondary-2: #c82a57;
    --text-darker: #6c757d;
    --color-time: #bbbbbb;
    --color-succes: #6ac52e;
    --background-chatsection: #D2D5E6;
    --bg-pagination-selected: #D2D5E6;
    --btn-neutral: #ffffff;
    --bg-external: linear-gradient(166deg,
    #424242 0%,
    #424242 100%);
    --border-radius-1: 8px;
    --border-radius-2: 8px;
    --border-radius-3: 8px;
}

.darkmode-button .night {
    display: block;
}

.darkmode-button .day {
    display: none;
}

/* ===== additional changes ===== */
.card-profile .btn svg{
    display:none;
}

.rounded.rounded-pill,
.btn-close{
    border-radius: var(--border-radius-1)!important;
}

.preference-icon{
    filter: grayscale(100%);
}

.form-check-input:checked + .btn-interests span{
    color:white;
}

.form-check-input:checked + .btn-interests .preference-icon{
    filter: brightness(0) invert(1);
}

.preferences .btn img{
    filter: grayscale(100%);
}

.message-inbox .username{
    color:black;
}

.card-profile .card-text a{
    color:black;
}

.page-member h2, .page-profile h2{
    color:black;
}

.mobile-menu .btn{
    color: rgb(134, 134, 134);
}

.mobile-menu .btn.active{
    color: var(--accent-primary-2);
}

.mobile-menu .indicator{
    display: none;
}

.main-title{
    color:black
}

.card-profile .btn{
    background-image: var(--accent-primary-gradient);
}

.credits-title{
    color:black;
}



/*===================================== DARK MODE ========================================*/

.darkmode .darkmode-button .night {
    display: none;
}

.darkmode .darkmode-button .day {
    display: block;
}

.darkmode {
    --border-1: 1px solid #9b96ff;
    --border-2: 1px solid rgb(255, 255, 255);
    --border-3: 1px solid #ffffff;
    --border-important: 2px rgb(248, 108, 0) solid;
    --bg-main: #343078;
    --bg-primary: #5650ce;
    --bg-secondary: #5650ce;
    --bg-tertiary: #7c76ff;
    --bg-tertiary-transparent: #9b96ff;
    --slider-gradient: linear-gradient(166deg, #321691 0%, #321691 100%);
    --slider-bg: var(--bg-tertiary);
    --accent-primary-gradient: linear-gradient(166deg,
            rgb(242, 40, 118) 0%,
            rgb(148, 45, 217) 100%);
    --accent-primary-gradient-ios: -o-linear-gradient(166deg,
            rgb(242, 40, 118) 0%,
            rgb(148, 45, 217) 100%);
    --vip-gradient: linear-gradient(-51deg,
            rgb(246, 100, 188) 0%,
            rgb(251, 123, 188) 35%,
            rgb(255, 146, 187) 100%);
    --accent-primary: #ffe0f0;
    --accent-secondary: #fef1ff;
    --vip-gradient: linear-gradient(-51deg,
            rgb(246, 100, 188) 0%,
            rgb(251, 123, 188) 35%,
            rgb(255, 146, 187) 100%);
    --buy-gradient: linear-gradient(-51deg,
            rgb(246, 100, 188) 0%,
            rgb(251, 123, 188) 35%,
            rgb(255, 146, 187) 100%);
    --important-gradient: linear-gradient(-51deg,
            rgb(255, 106, 0) 0%,
            rgb(255, 111, 0) 35%,
            rgb(234, 88, 3) 100%);
    --important-gradient-2: linear-gradient(166deg,
            rgb(121, 187, 40) 0%,
            rgb(55, 139, 19) 100%);
    --text-light: white;
    --text-lighter: rgb(244, 244, 244);
    --text-dark: #ffffff;
    --color-logo: white;
    --color-male: rgba(139, 209, 255, 0.425);
    --color-female: #ff62949a;
    --color-online: rgb(114, 179, 83);
    --color-important: rgb(248, 108, 0);
    --color-icon-1: #fecaee;
    --bs-accordion-bg: rgba(0, 0, 0, 0);
    --bs-link-color: var(--accent-secondary);
    --bs-accordion-active-color: black !important;
    --bg-profile-page-menu: rgba(255, 255, 255, 0);
    --color-profile-page-menu: rgb(255, 255, 255);
    --blink-color-1a: rgba(255, 255, 255, 0.6);
    --blink-color-1b: rgba(255, 255, 255, 1);
    --bg-message-member: white;
    --bg-filter: white;
    --bg-special-offer: #fdfdfd;
    --bg-popup-header: var(--bg-primary);
}

.darkmode .footer-section,
.darkmode .accordion-footer .accordion-body,
.darkmode .accordion-header,
.darkmode .accordion-button,
.darkmode .accordion-button:not(.collapsed) {
    background-color: #3f3b9a;
}

.darkmode .accordion-footer a {
    color: white;
}

.darkmode .footer-section,
.darkmode .accordion-footer .accordion-body,
.darkmode .accordion-header,
.darkmode .accordion-button,
.darkmode .accordion-button:not(.collapsed) {
    color: white;
}

body.darkmode {
    color: white;
}

.darkmode .btn-neutral {
    background-color: var(--bg-tertiary);
}


.darkmode .accordion-item {
    color: white;
}

.darkmode .list-group-item {
    color: white;
}

.darkmode .profile-page-menu {
    border: var(--border-1);
}

.darkmode #filter-form .accordion-body {
    border: var(--border-1);
    background-color: var(--bg-secondary);
}

.darkmode .btn-neutral:hover {
    color: white;
}

.darkmode .image-popup {
    background-color: var(--bg-secondary);
}

.darkmode .card-profile {
    border: var(--border-1);
}

.darkmode .content-box {
    border: var(--border-1);
    border-color: var(--bg-tertiary);
}

.darkmode .membership-card {
    border: var(--border-1);
}

.darkmode .table {
    color: white !important;
}

.darkmode .form-control,
.darkmode .form-select,
.darkmode .select2-container .select2-selection--single .select2-selection__rendered {
    background-color: var(--bg-tertiary);
    color: white;
}

.darkmode .form-control:focus,
.darkmode .form-select:focus {
    background-color: var(--bg-tertiary);
}

.darkmode .select2-dropdown {
    background-color: var(--bg-tertiary);
}

.darkmode .border-start,
.darkmode .border-bottom {
    border-color: var(--bg-tertiary) !important;
}


.darkmode .gender-seek i {
    color: white;
}


.darkmode .accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.darkmode .accordion-button:not(.collapsed)::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.darkmode textarea::placeholder {
    color: white !important;
}

.darkmode textarea,
.darkmode input {
    color: white;
}

.darkmode .form-control:focus {
    color: white;
}

.darkmode .search-profiles input::placeholder {
    color: white;
}

.darkmode ::selection {
    background-color: var(--bg-main);
}


.darkmode .dropdown-select-search option:hover {
    background-color: black !important;
}


.darkmode .form-check-input:checked+.btn-interests {
    font-weight: bold;
}

.darkmode .form-control:disabled,
.darkmode .form-select:disabled {
    background-color: var(--bg-main);
}

.darkmode .modal-content {
    border: var(--border-1);
    background-color: var(--bg-main);
}

.darkmode .modal-footer {
    background-color: var(--bg-primary);
}

.darkmode .btn-close {
    background-color: white;
    border-radius: 50%;
}

.darkmode .form-select option:disabled {
    color: white;
    font-style: italic;
    font-size: 14px;
    font-weight: bold;
}

.darkmode .form-control,
.darkmode .form-select,
.darkmode .select2-container .select2-selection--single .select2-selection__rendered {
    background-color: var(--bg-tertiary);
    /* background-image:none; */
}

.darkmode .btn-neutral.is-favorite {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0);
}

.darkmode .btn-neutral.add-favorite {
    border: var(--border-2);
    color: white;
    background-color: rgba(0, 0, 0, 0);
}

.darkmode .message-content {
    border: var(--border-1);

}

.darkmode .left-message .message-content {
    background-color: var(--bg-tertiary);
}

.darkmode .right-message .message-content {
    background-color: rgba(0, 0, 0, 0);
}


.darkmode .card-purchase {
    background-color: rgba(0, 0, 0, 0) !important;
}

.darkmode .footer-icons img {
    background-color: white;
    border-radius: 5px;
}

.darkmode .card-profile .btn-neutral {
    background-color: rgba(0, 0, 0, 0);
}

.darkmode .btn {
    color: white;
    border: var(--border-1);
}

.darkmode .btn:hover {
    border: var(--border-2);
}

.darkmode .preferences img,
.darkmode .btn-interests img {
    filter: brightness(0) invert(1);
}

.darkmode.page-members .card-profile .btn-neutral {
    border-width: 0px;
}

.darkmode .text-muted {
    color: rgba(255, 255, 255, 0.84) !important;
}

.darkmode .table.table-striped td {
    color: white !important;
}
