/*---------- Colors ----------*/
:root {
    --accent-primary: #389aba;
    --accent-primary-light: #d1e5ec;
    --accent-secondary: #ca63b0;
    --bg-body: #ffffff;
    --bg-primary: #f5f4ef;
    --bg-primary-highlight: #ebe8d4;
    --bg-primary-alt: var(--bg-primary);
    --bg-green: #149909;
    --bg-orange: #f48802;
    --bg-red: #e91818;
    --bg-bundle:#ffffff;
    --bg-toggle: var(--accent-primary);
    --bg-footer: #f5f4ef;
    --bg-menu: var(--accent-primary);
    --bg-menu-hover: #ffffff;
    --bg-menu-badge: rgba(202, 99, 176, 0.6);
    --bg-fade-up: linear-gradient(0deg, var(--bg-primary-alt) 0%, var(--bg-primary-alt) 78%, rgba(0,0,0,0) 100%);
    --bg-btn-pay: var(--bg-orange);
    --bg-chat-box: #f5f4ef;
    --bg-modal-overlay: #000000af;
    --text-light: #ffffff;
    --text-light-muted: #cccccc;
    --text-dark: #000000;
    --text-dark-muted: #777777;
    --text-footer: var(--text-dark);
    --text-menu: var(--text-light);
    --text-menu-hover: var(--accent-primary);
    --text-menu-external: #fffb2c;
    --border-chat-box: var(--bg-primary-highlight);
}

.accordion-button:not(.collapsed)::after,
.accordion-button::after {
    --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='black'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='black'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
