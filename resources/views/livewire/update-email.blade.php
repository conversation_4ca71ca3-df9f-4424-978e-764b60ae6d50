{{--used in change email on settings page--}}
<form class="update-email" wire:submit="updateEmail" method="POST" class="mb-5">
    @csrf
    <h2>{{ __('Change your email') }}</h2>
    <p class="mb-3">
        {{ __('You can change your email only once and you will need to activate your account again.') }}
    </p>
    <div class="row form-group mb-3">
        <label for="email" class="col col-form-label">{{ __('Current e-mail') }}</label>
        <div class="col-sm-8 col-md-7 col-lg-9 col-xl-9">
            <input wire:model="email" name="email" type="email" class="form-control"
                   placeholder="" value="" required>
        </div>
    </div>
    <div class="row form-group mb-3">
        <label for="email_new" class="col col-form-label">{{ __('New e-mail') }}</label>
        <div class="col-sm-8 col-md-7 col-lg-9 col-xl-9">
            <input wire:model="email_new" name="email_new" type="email" class="form-control"
                   placeholder="" value="" required>
        </div>
    </div>
    <div class="row form-group mb-3">
        <label for="email_new2" class="col col-form-label">{{ __('Confirm e-mail') }}</label>
        <div class="col-sm-8 col-md-7 col-lg-9 col-xl-9">
            <input wire:model="email_new2" name="email_new2" type="email" class="form-control"
                   placeholder="" value="" required>
        </div>
    </div>
    <div class="row my-3 btn-wrapper">
        <div class="col-auto">
            <button type="submit" class="btn btn-primary">{{ __('Save') }}
        </div>
    </div>
</form>
