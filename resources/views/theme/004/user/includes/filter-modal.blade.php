<form id="filter-form" action="{{ route('home') }}" method="GET" class="filter">
    <input type="hidden" name="gender" value="female">
    <input type="hidden" name="has_filters" value="1">
    <div class="accordion accordion-flush accordion-filter" id="accordionFilter">
        <div class="accordion-item py-0">
            <div class="row g-2 mb-2 ">
                <div class="col-12 mb-2 text-center">
                    {{ __('Age between:') }}
                    <span class="selected-age d-inline">{{ $filter['age_min'] }}-{{ $filter['age_max'] }}</span>
                    <div class="px-2">
                        <input type="hidden" id="age-min" name="age_min" value="{{ $filter['age_min'] }}">
                        <input type="hidden" id="age-max" name="age_max" value="{{ $filter['age_max'] }}">
                        <input id="age-slider" class="d-none" type="text">
                    </div>
                </div>
                <div class="col-12">
                    @if ($domain->use_distance)
                        <select class="dropdown-select-search" name="distance">
                            <option value="all">{{ __('Every distance') }}</option>
                            @foreach (trans('lists.distance') as $distance)
                                <option value="{{ $distance }}"
                                        @if ($filter['distance'] == $distance) selected @endif>
                                    {{ $distance }}
                                </option>
                            @endforeach
                        </select>
                    @else
                        <select class="dropdown-select-search" name="region_id">
                            <option value="all">{{ __('All states') }}</option>
                            @foreach ($regions as $region)
                                <option value="{{ $region->id }}"
                                        @if ($filter['region_id'] == $region->id) selected @endif>
                                    {{ $region->name }}
                                </option>
                            @endforeach
                        </select>
                    @endif
                </div>
                <div class="col-12">
                    <select class="form-select" name="ethnicity">
                        <option value="">{{ __('All ethnicities') }}</option>
                        @foreach (trans('lists.ethnicity') as $key => $value)
                            <option value="{{ $key }}"
                                    @if ($filter['ethnicity'] == $key) selected @endif>
                                {{ __($value) }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-12 mb-2">
                    <select class="form-select" name="hair_colors">
                        <option value="">{{ __('All hair colors') }}</option>
                        @foreach (trans('lists.hair_colors') as $key => $value)
                            <option value="{{ $key }}"
                                    @if ($filter['hair_colors'] == $key) selected @endif>
                                {{ __($value) }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-12">
                    @if ($filtersChanged)
                        <a class="btn btn-secondary mx-auto" href="{{ route('home') }}">
                            {{ __('Reset filter') }}
                        </a>
                    @endif
                </div>
                <div class="col-12">
                    <button class="btn btn-primary mx-auto" type="submit">
                        {{ __('Search') }}
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>
