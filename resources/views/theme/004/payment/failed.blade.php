@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <div class="flex-grow-1 overflow-auto">
        <div class="container text-center d-flex align-items-center justify-content-center">
            <div class="row row-cols-1 row-cols-md-1 mt-5" style="max-width:500px;">
                <div class="col d-flex align-items-center justify-content-center">
                    <div>
                        <h2><i class="bi bi-x-circle text-danger fs-3"></i> {{ __('Payment Denied') }}</h2>
                        <p class="mb-4">
                            {!! __(
                                'Oops! It looks like your payment didn\'t go through. <br>Please check your payment details and try again. If you need assistance, contact our support team—we\'re happy to help!',
                            ) !!}
                        </p>
                        <div class="row row-cols-1 row-cols-md-2 g-2">
                            <div class="col">
                                <a class="btn btn-neutral w-100" href="{{ route('cmsFaq') }}">
                                    {{ __('FAQ page') }}
                                </a>
                            </div>
                            <div class="col">
                                <a class="btn btn-neutral w-100" href="{{ route('cmsContact') }}">
                                    {{ __('Contact us') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col h-100">
                    <img class="w-100 mb-4" src="{{ cdn($domain) }}/images/payment-cancel.png">
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    @include('global.payment.scripts.payment-scripts')
    @if (isset($order) && $order !== null && $paymentMethod !== null)
        @if (View::exists('global.payment.scripts.' . $paymentMethod->type->name))
            @include('global.payment.scripts.' . $paymentMethod->type->name)
        @endif
    @endif
@endpush
