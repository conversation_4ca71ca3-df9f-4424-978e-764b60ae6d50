@if(!isset($platinumPage))
<div class="row row-cols-1 g-0 gy-3 choose-bundle ms-auto me-auto" style="max-width: 600px;">
@endif
    @foreach ($products as $key => $product)
        <input data-credits="{{ $product->credits }}" data-price="{{ $product->toPay() }}" autocomplete="off"
            class="product-radio d-none" type="radio" id="product-{{ $product->id }}" name="product_id"
            value="{{ $product->id }}" @if (request()->query('amount') == $product->credits ||
                    $is_offer ||
                    (!request()->query('amount') && $product->id == $defaultSelected)) checked @endif>
        @if ($productType == 'credits')
            <div class="col token">
            <label class="card card-purchase btn btn-secondary product-label  @if ($bestseller && $product->best_seller) popular-product @endif @if ($product->amount > $product->toPay()) discounted-product @endif show-next"
                role="button" for="product-{{ $product->id }}">
                <div class="card-body product-labels row">
                    <div class="card-info col-auto">
                        <p class="credits-number">{{ $product->credits }}</p>
                        <p class="small">{{ __('credits') }}</p>
                    </div>
                    @if ($domain->hasModule('premium') && $user->isPremium())
                    <div class="col-auto d-flex px-2 align-items-center justify-content-center free-credit-box">
                        <div>
                            <p class="credits-number">+{{ ceil($product->credits * 0.1) }}</p>
                            <p class="small">{{ __('Bonus Credits') }}</p>
                        </div>
                    </div>
                    @endif
                    <div class="col px-0">
                        <div class="position-relative">
                            <div class="card-title">{{ currency($domain, $product->toPay()) }}</div>
                        </div>
                    </div>
                    <div class="card-btn btn btn-secondary col-auto">{{ __('Select package') }}</div>
                </div>
            </label>
        </div>
        @endif
        @if ($productType == 'premium')
                <div class="swiper-slide card {{ $product->best_seller ? 'active' : '' }}" data-index="{{ $key }}">
                    <div class="card-body mt-3 pe-none">
                        <p class="card-text">{{ $product->months }} {{ $product->months > 1 ? __('months') : __('month') }}</p>
                        <p class="card-amount">{{ currency($domain, $product->toPay()) }}</p>
                        <h5 class="card-title">{{ currency($domain, $product->price_per_month) }} / {{ __('month') }}</h5>
                    </div>
                    <ul class="list-group list-group-flush pe-none text-start">
                        <li class="list-group-item"><i class="bi bi-check-circle-fill me-2"></i> {{ $product->credits }} {{ __('credits per month') }}</li>
                        <li class="list-group-item"><i class="bi bi-check-circle-fill me-2"></i> {{ __('10% bonus credits on every purchase') }}</li>
                        <li class="list-group-item"><i class="bi bi-check-circle-fill me-2"></i> {{ __('Unlock private pictures of all members') }}</li>
                        <li class="list-group-item"><i class="bi bi-check-circle-fill me-2"></i> {{ __('Free Match Game') }}</li>
                        <li class="list-group-item"><i class="bi bi-check-circle-fill me-2"></i> {{ __('See who liked your profile') }}</li>
                        <li class="list-group-item"><i class="bi bi-check-circle-fill me-2"></i> {{ __('Access to your matches') }}</li>
                        <li class="list-group-item"><i class="bi bi-check-circle-fill me-2"></i> {{ __('Access to your likes') }}</li>
                    </ul>
                    @if ($product->best_seller)
                        <div class="most-popular pe-none">{{ __('Most popular') }}</div>
                    @endif
                    <label for="product-{{ $product->id }}" class="product-label btn select-btn" data-bs-toggle="modal" data-bs-target="#paymentMethodsModal">{{ __('Select') }}</label>
                </div>
        @endif
    @endforeach
@if(!isset($platinumPage))
</div>
@endif
