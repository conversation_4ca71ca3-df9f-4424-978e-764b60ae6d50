<div class="modal fade" data-bs-keyboard="false" id="a-match" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('It\'s a match') }}!</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center px-4 py-2">
                    <div class="row row-cols-1">
                        <div class="col">
                            <div class="row g-0 align-items-center justify-content-center">
                                <div class="col-auto">
                                    <div class="position-relative">
                                        <div class="match-thumb invisible">
                                            <img src="{{ thumb($user->profile_image, $user->gender) }}">
                                        </div>
                                        <div class="match-thumb match-thumb-1">
                                            <img src="{{ thumb($user->profile_image, $user->gender) }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="position-relative">
                                        <img class="match-thumb invisible match-image" src="{{ cdn($domain) }}/images/image-home-2.jpg">
                                        <div class="match-thumb match-thumb-2">
                                            <img class="match-image" src="{{ cdn($domain) }}/images/image-home-2.jpg">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col mt-3">
                            <div class="h-100 d-flex justify-content-center align-items-center">
                                <div>
                                    <p>
                                        {!! __(
                                            'You and <span class="match-username"></span> liked each other, you can now send her a message.',
                                        ) !!}
                                    </p>
                                    <p class="d-none">
                                        {!! __(
                                            'You matched with someone! Find out who by becoming a platinum member',
                                            [
                                                // 'member' => $profile->username,
                                            ],
                                        ) !!}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
            <div class="modal-footer justify-content-center ">
                <a class="btn btn-primary match-chat-now" href="#">
                    {{ __('Chat now!') }}
                </a>
                @if($domain->hasModule('premium'))
                    <a href="{{ $user->isPremium() ? route('premiumOverview') : route('premium') }}" class="btn btn-primary d-none">{{ __('Upgrade now!') }}</a>
                @endif
            </div>
        </div>
    </div>
</div>
