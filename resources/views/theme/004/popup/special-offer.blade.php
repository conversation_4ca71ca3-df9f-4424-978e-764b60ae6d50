<!-- Special Offer -->
<div class="modal fade special-offer-modal" data-bs-backdrop="static" tabindex="-1" data-bs-keyboard="false" aria-hidden="true">
	<div class="modal-dialog modal-sm modal-dialog-centered modal-dialog-scrollable">
		<form method="POST" action="{{ route('pay') }}" class="modal-content text-center">
			<div class="modal-header">
				<h5 class="modal-title">{{ __('Discount alert!') }}!</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
            <div class="modal-body text-center py-3 px-3">
                @if ($userHasAffiliate)
                    @if ($domain->stylesheet == '004b')
                        <img class="w-100 animated-gif my-2" src="{{ cdn($domain) }}/images/gif-4.webp">
                    @else
                        <img class="w-100 animated-gif my-2" src="{{ cdn($domain) }}/images/gif-3.webp">
                    @endif
                @endif
                <div class="px-5 pt-3 pb-0">
                    {!! __('Save 43%, buy :credits credits for :final!', [
                        'credits' => $offer->credits,
                        'initial' => currency($domain, $offer->amount),
                        'final' => currency($domain, $offer->toPay()),
                    ]) !!}
                </div>
            </div>
           <div class="modal-footer justify-content-center">
                <a href="{{ route('creditsOffer', ['offer_id' => $offer ? $offer->id : null]) }}" class="btn btn-primary modal-click">
                    {{ __('Claim now!') }}
                </a>
			</div>
		</form>
	</div>
</div>
@push('scripts')
    <script>
        $( document ).ready(function() {
            let hasCredits = sessionStorage.getItem('lastKnownCredits') > 0;
            let recentlyShown = false;
            let lastShownAt = sessionStorage.getItem('specialOfferModalShownAt');
            if (lastShownAt) {
                recentlyShown = lastShownAt > Date.now() - specialOfferTimeout;
            }
            if (sessionStorage.getItem('visitedBuyCredits') && !window.location.href.includes('/credits') && !hasCredits && !recentlyShown && !hasPaid) {
                $('.special-offer-modal').modal('show');
                sessionStorage.removeItem('visitedBuyCredits');
                sessionStorage.setItem('specialOfferModalShownAt', Date.now());
            }

            $('.special-offer-modal').on('hide.bs.modal', function (e) {
                $.post("{{ route('saveNotification',['type' => 'popup', 'name' => 'special-offer', 'action' => 'dismiss']) }}");
            })
            $('.special-offer-modal .modal-click').on('click', function (e) {
                $.post("{{ route('saveNotification',['type' => 'popup', 'name' => 'special-offer', 'action' => 'click']) }}");
            })
		});
    </script>
@endpush
