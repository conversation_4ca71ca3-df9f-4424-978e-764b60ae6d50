<!-- popup - signup -->
<div class="modal fade forgot-password-modal" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered modal-dialog-scrollable">
        <form method="POST" action="{{ route('password') }}" class="modal-content @if($domain->recaptcha_site_key) recaptcha-form @endif">
            @csrf
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Forgot your password? Don\'t worry!') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body login-form all-filled text-center">
                @error('reset_error')
                    <div class="alert alert-danger" role="alert">{!! $message !!}</div>
                @enderror
                <div>
                    {{ __('Fill in your email address below and we\'ll send you the instructions to reset your password.') }}
                </div>
                <div class="form-floating mt-3">
                    <input type="email" name="email" class="form-control" id="email-password-reset"
                        placeholder="{{ __('Email') }}" value="{{ old('email') }}" dusk="password-forgot-email" required>
                    <label for="email-password-reset">{{ __('Email') }}</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary w-100 ms-auto me-auto">
                    {{ __('Reset password') }}
                </button>
            </div>
        </form>
    </div>
    @push('scripts')
        @error('reset_error')
            <script>
                $(document).ready(function() {
                    $('.forgot-password-modal').modal('show');
                });
            </script>
        @enderror
    @endpush
</div>
