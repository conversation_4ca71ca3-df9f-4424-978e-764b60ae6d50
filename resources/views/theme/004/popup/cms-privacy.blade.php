<div class="modal fade cms-privacy" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    {{ __('Privacy Policy') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-3">
                {!! trans_cms('privacy.intro',[
                    'agreement' => route('cmsAgreement'),
                    'privacy' => route('cmsPrivacy'),
                    'pricing' => route('cmsPricing'),
                    'company_name' => $domain->company->name,
                    'name_url' => $domain->name_url,
                    'url' => $domain->url,
                    'app_url' => $domain->name_url,
                ]) !!}
                {!! trans_cms('privacy.block',[
                    'agreement' => route('cmsAgreement'),
                    'contact' => route('cmsContact'),
                    'cookies' => route('cmsCookies'),
                    'company_name' => $domain->company->name,
                    'contact_person' => $domain->company->contact_person,
                    'contact_phone' => $domain->company->contact_phone,
                    'address' => $domain->company->address,
                    'zip' => $domain->company->zip,
                    'city' => $domain->company->city,
                    'country' => $domain->company->country,
                    'dpo_phone' => $domain->company->dpo_phone,
                    'chamber_of_commerce' => $domain->company->chamber_of_commerce,
                    'dpo_email' => 'dpo@' . $domain->name_url,
                    'url' => $domain->url,
                    'app_url' => $domain->name_url,
                ]) !!}
            </div>
            <div class="modal-footer justify-content-center">
                <button role="button" type="button" class="btn btn-primary" data-bs-dismiss="modal"> {{ __('Close') }}</button>
            </div>
        </div>
    </div>
</div>
