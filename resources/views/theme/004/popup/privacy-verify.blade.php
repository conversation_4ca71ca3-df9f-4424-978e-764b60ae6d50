    <div id="popup-privacy-verify" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade sure-notification-request-user-data-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="w-100 mb-0">
                        {{ __('Welcome :username',['username' => $user->username]) }}
                    </h2>
                </div>
                <div class="modal-body">
                    <!-- Step 1 -->
                    <div class="step" data-step="1">
                        <div class="mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div class="fw-bold mb-4">
                            {{ trans_cms('verify.step1') }}
                        </div>
                        <div>
                            {{ __('Click, "Yes, I agree" to proceed or "No, I reject" if you want to leave this Website now!') }}
                        </div>
                    </div>
                    <!-- Step 2 -->
                    <div class="step d-none" data-step="2">
                        <div class="mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div>
                            {!! trans_cms('verify.step2', ['cookies' => route('cmsCookies')]) !!}
                        </div>
                    </div>
                    <!-- Step 3 -->
                    <div class="step d-none" data-step="3">
                        <div class="mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div>
                            {!! trans_cms('verify.step3', ['privacy' => route('cmsPrivacy')]) !!}
                        </div>
                    </div>
                    <!-- Step 4 -->
                    <div class="step d-none" data-step="4">
                        <div class="mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div>
                            {!! trans_cms('verify.step4', ['terms' => route('cmsAgreement')]) !!}
                        </div>
                    </div>
                    <!-- Step 5 -->
                    <div class="step d-none" data-step="5">
                        <div class="mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div>
                            {!! trans_cms('verify.step5') !!}
                        </div>
                    </div>
                    <!--  Step 6 -->
                    <div class="step d-none" data-step="6">
                        <div class="mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div>
                            {!! trans_cms('verify.step6',
                                [
                                    'terms' => route('cmsAgreement'),
                                    'privacy' => route('cmsPrivacy'),
                                    'cookies' => route('cmsCookies')
                                ]
                            ) !!}
                        </div>
                    </div>
                    <!-- Step Reject -->
                    <div class="step-reject d-none">
                        <div class="popup_text_special">
                            {{ __('Are you sure you want to reject the Terms & Conditions and the Privacy Statement?') }}<br /><br />
                            {{ __('Your account will be blocked immediately and you won\'t be able to use this service.') }}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="steps-agree-decline w-100 d-flex justify-content-between">
                        <a class="btn btn-secondary step-decline" href="#">
                            {{ __('No, I reject') }}
                        </a>
                        <button class="btn btn-primary step-agree">
                            {{ __('Yes, I agree!') }}
                        </button>
                    </div>
                    <div class="steps-confirm-agree-decline d-none w-100 d-flex justify-content-between">
                        <button type="button" class="btn btn-secondary step5-decline">{{ __('No, I reject') }}</button>
                        <form action="{{ route('verifyPrivacy') }}" method="POST">
                            @csrf
                            <button class="btn btn-primary" type="submit">{{ __('Yes, I agree!') }}</button>
                        </form>
                    </div>
                    <div class="step-reject d-none w-100 d-flex justify-content-between">
                        <form action="{{ route('verifyPrivacyReject') }}" method="POST">
                            @csrf
                            <button class="btn btn-secondary" type="submit">{{ __('Yes, I am sure') }}</button>
                        </form>
                        <button type="button" class="btn btn-primary step-reject-agree">{{ __('No, proceed') }}</button>
                    </div>
                    <div class="redirect-message d-none text-center">
                        {!! __('You will be redirected in <strong>5</strong> seconds.') !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
@push('scripts')
    <script>
        $( document ).ready(function() {
                var popupStep = 1;
                $('#popup-privacy-verify').modal('show');
                // Go to next step
                $('.step-agree').on('click', function(){
                    if(popupStep === 5) {
                        $('.steps-agree-decline').addClass('d-none');
                        $('.redirect-message').removeClass('d-none');
                        $.ajax({
                            type: "POST",
                            url: "{{ route('verifyPrivacyJsonResponse') }}",
                            success: setTimeout(hidePopups, 5000)
                        });
                    }
                    $('.step[data-step="'+popupStep+'"]').addClass('d-none');
                    popupStep++;
                    $('.step[data-step="'+popupStep+'"]').removeClass('d-none');
                });
                // Show confirmation screen if user really wants to decline
                $('.step-decline').on('click', function(){
                    $('.step').addClass('d-none');
                    $('.steps-agree-decline').addClass('d-none');
                    $('.step-reject').removeClass('d-none');
                });
                // Cancel the decline - go back to step 1
                $('.step-reject-agree').on('click', function(){
                    popupStep = 1;
                    $('.step').addClass('d-none');
                    $('.step[data-step="'+popupStep+'"]').removeClass('d-none');
                    $('.step-reject').addClass('d-none');
                    $('.steps-agree-decline').removeClass('d-none');
                });
                function hidePopups() {
                    $('#popup-privacy-verify').modal('hide');
                }
        });
    </script>
@endpush
