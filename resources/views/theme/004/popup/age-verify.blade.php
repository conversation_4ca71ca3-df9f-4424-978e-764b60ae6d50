<!-- age verify -->
<div id="popup-age-verify" data-bs-backdrop="static" class="modal fade" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
	<div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
		<form method="POST" action="#" class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">{!! trans_cms('age.title') !!}</h5>
			</div>
			<div class="modal-body">
				<div class="small mb-3">
					{!! trans_cms('age.text',['app_name' => $domain->name]) !!}
				</div>
				<div>
					{!! trans_cms('age.content',[
						'agreement' => route('cmsAgreement'),
						'privacy' => route('cmsPrivacy'),
						'disclaimer' => route('cmsPricing'),
						'cookies' => route('cmsCookies'),
						'children' => route('cmsChild'),
						'country' => ($domain->company->country === 'United Kingdom' ? 'England' : $domain->company->country),
					]) !!}
				</div>
			</div>
			<div class="modal-footer justify-content-center">
				<label class="mb-2">
					<input class="me-2" type="checkbox" id="checkVerifyAge" required> {!! trans_cms('age.agree', [
						'agreement' => route('cmsAgreement'),
						'privacy' => route('cmsPrivacy'),
					]) !!}
				</label>
                <a class="btn btn-neutral" href="https://www.google.com" class="decline">
                    {{ __('Exit') }}
                </a>
				<button type="submit" class="btn btn-primary agree">
					{{ __('I agree') }}
				</button>
			</div>
		</form>
	</div>
    @push('scripts')
	<script>
		$( document ).ready(function() {
			if(localStorage.getItem('ageVerification') != 'shown') {
				$("#popup-age-verify").modal('show');
			}
			$('.agree').on("click", function(ev) {
				if($('#checkVerifyAge').is(':checked')) {
					ev.preventDefault();
					$("#popup-age-verify").modal('hide');
					localStorage.setItem('ageVerification','shown');
				}
			});
		});
	</script>
    @endpush
</div>
