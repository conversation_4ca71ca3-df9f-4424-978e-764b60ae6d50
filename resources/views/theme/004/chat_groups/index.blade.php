@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <!-- content start -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col sidebar d-none d-lg-block">
                    @include('theme.' . $domain->theme . '.layouts.includes.side-menu')
                </div>
                <div class="col">
                    <ul class="content-tabs nav nav-tabs" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a href="javascript:void(0)" class="nav-link active" id="all-tribes">{{ __('All Tribes') }}</a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a href="javascript:void(0)" class="nav-link" id="my-tribes">{{ __('My Tribes') }}</a>
                        </li>
                    </ul>

                    <div class="my-tribe all-tribes">
                        <div class="chat-groups">
                            <div class="row">
                                @foreach ($chat_groups as $group)
                                    @php $isJoined = in_array($group->id, $joined, true); @endphp
                                    <div
                                        class="col-6 col-lg-3 tribe @if ($isJoined) tribe-joined @else tribe-not-joined @endif">

                                        <div class="link-block">
                                            <div class="image">
                                                <a
                                                    href="{{ route('groupPosts', ['section' => 'contributions', 'id' => $group->id]) }}">
                                                    <img src="{{ $group->imageLink }}" alt="{{ $group->title }}"
                                                        class="img-fluid w-100">
                                                    @if ($isJoined)
                                                        <span class="joined">{{ __('Joined') }}</span>
                                                    @endif
                                                </a>
                                            </div>
                                            <div class="members">
                                                {{ __(':amount members', ['amount' => $group->total_members]) }}</div>
                                            <div class="group-title">{{ $group->title }}</div>
                                        </div>
                                    </div>
                                @endforeach
                                <div id="not-joined" class="col-12 text-center d-none">
                                    {{ __('You are not a member of any tribe yet. Join one, it\'s free!') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- content end -->
    </div>
@endsection
