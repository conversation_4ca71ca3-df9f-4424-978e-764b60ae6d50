<script>
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    });

    $(document).ajaxError(function myErrorHandler(event, xhr, ajaxOptions, thrownError) {
        if (xhr.responseJSON.message !== undefined) {
            toastr.error(xhr.responseJSON.message, '', {
                timeOut: 0,
                extendedTimeOut: 0,
                closeButton: true,
            });
        }
    });

    toastr.options = {
        "closeButton" : true,
        "progressBar" : true,
        "preventDuplicates" : true,
        "positionClass": "toast-bottom-right",
        "timeOut" : 7500,
        "extendedTimeOut " : 15000
    };

    var cityAutocompleteTimeout = 100;
    var favoriteInProgress = false;
    var routeHome = "{{ route('home') }}";
    var saveSearchUrl = {{ $currentRoute == 'members' && !$filter['search'] ? 'true;' : 'false;' }}
    var textShowAllResults = "{{ __('Show all results') }}";
    var momentLang = '{{ strtolower(explode("_", $domain->locale)[0]) }}';
    var recaptchaKey = '{{ $domain->recaptcha_site_key }}';

    var magnificNext = "{{ __('Next (Right arrow key)') }}";
    var magnificPrevious = "{{ __('Previous (Left arrow key)') }}";
    var magnificClose = "{{ __('Close (Esc)') }}";
    var magnificCounter = "%curr% {{ __('of') }} %total%";
    var magnificAjaxError = "{{ __('The request failed.') }}";

    var justNow = "{{ __('Just now') }}";
    var timeAgo = " {{ __('ago') }}";
    var checkMessageRoute = '{{ route("checkMessages") }}';
    var showMoreMessagesRoute = '{{ route("showMoreMessages") }}';
    var appLocale = "{{ app()->getLocale() }}";
</script>
