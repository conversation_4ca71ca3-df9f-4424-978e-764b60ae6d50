<script>
    $(document).ready(function () {
        // Toastr
        @if($errors->any())
            @foreach($errors->all() as $error)
            toastr.error("{!! addslashes($error) !!}");
            @endforeach
        @endif

        @if(Session::get('success'))
            toastr.success("{!! Session::get('success') !!}");
        @endif

        @if(Session::get('info'))
            toastr.info("{!! Session::get('info') !!}");
        @endif

        @if($errors->hasBag('register'))
            @foreach($errors->register->all() as $error)
                toastr.error("{!! addslashes($error) !!}", '', {
                    timeOut: 0,
                    extendedTimeOut: 0,
                    closeButton: true,
                });
            @endforeach
        @endif

        @if($errors->hasBag('login'))
            @foreach($errors->login->all() as $error)
                toastr.error("{!! addslashes($error) !!}");
            @endforeach
        @endif
    });
</script>
