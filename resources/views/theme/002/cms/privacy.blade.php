@extends('theme.'. $domain->theme .'.layouts.master')
@section('content')
	<div class="container-main-space-for-menu container-info">
		<h1>
			{!! trans_cms('privacy.title') !!}
		</h1>
		<span class="intro">
			{!! trans_cms('privacy.intro',[
				'agreement' => route('cmsAgreement'),
				'privacy' => route('cmsPrivacy'),
				'pricing' => route('cmsPricing'),
				'company_name' => $domain->company->name,
				'name_url' => $domain->name_url,
				'url' => $domain->url,
				'app_url' => $domain->name_url,
			]) !!}
		</span>
		<span class="block">
			{!! trans_cms('privacy.block',[
				'agreement' => route('cmsAgreement'),
				'contact' => route('cmsContact'),
				'cookies' => route('cmsCookies'),
				'company_name' => $domain->company->name,
				'contact_person' => $domain->company->contact_person,
				'contact_phone' => $domain->company->contact_phone,
				'address' => $domain->company->address,
				'zip' => $domain->company->zip,
				'city' => $domain->company->city,
				'country' => $domain->company->country,
				'dpo_phone' => $domain->company->dpo_phone,
				'chamber_of_commerce' => $domain->company->chamber_of_commerce,
				'dpo_email' => 'dpo@' . $domain->name_url,
                'billing_email' => $domain->company->email,
				'support_email' => 'support@' . $domain->name_url,
				'url' => $domain->url,
				'app_url' => $domain->name_url,
			]) !!}
		</span>
		<div style="clear:both"></div>
	</div>
@endsection
