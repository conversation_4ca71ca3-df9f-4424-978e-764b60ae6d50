<!-- popup - add city -->
<div class="modal fade modal-basic add-city-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form method="POST" action="{{ route('postCity') }}" class="modal-content">
        @csrf
        <div class="modal-header">
            <h5 class="modal-title">{{ __('Which city do you live in?') }}</h5>
        </div>
        <div class="modal-body">
            <div class="input">
                <input type="text" name="cityAutocomplete" value="" placeholder="{{ __('City of residence') }}" class="ui-autocomplete-input match_location_input" id="cityAutocomplete" autocomplete="new-password">
                <input type="hidden" name="cityId" value="" id="cityInput" data-field="cityId">

                <div id="results-container" class="d-none">
                    <ul class="results">

                    </ul>
                </div>

            </div>
        </div>
        <div class="modal-footer">
            <button type="submit" class="button-single">{{ __('Submit') }}</button>
        </div>
        </form>
    </div>
</div>
@push('scripts')
<script>
$( document ).ready(function() {
    if($('#popup-privacy-verify').length == 0) {
        $('.add-city-modal').modal('show');
    }
});
</script>
@endpush
