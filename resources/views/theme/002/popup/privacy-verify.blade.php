@if(\Illuminate\Support\Str::startsWith($domain->locale, 'en'))
    <div id="popup-privacy-verify" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade modal-basic" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-center">
                        {{ __($domain->name) }}
                    </h5>
                </div>
                {{-- Step 1 --}}
                <div class="modal-body">
                    <div class="step" data-step="1">
                        <div class="popup_text_normal mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div class="popup_text_special mb-4">
                            {{ trans_cms('verify.step1') }}
                        </div>
                        <div class="popup_text_normal">
                            {{ __('Click, "Yes, I agree" to proceed or "No, I reject" if you want to leave this Website now!') }}
                        </div>
                    </div>
                    {{-- Step 2 --}}
                    <div class="step d-none" data-step="2">
                        <div class="popup_text_normal mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div class="popup_text_special">
                            {!! trans_cms('verify.step2', ['cookies' => route('cmsCookies')]) !!}
                        </div>
                    </div>
                    {{-- Step 3 --}}
                    <div class="step d-none" data-step="3">
                        <div class="popup_text_normal mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div class="popup_text_special">
                            {!! trans_cms('verify.step3', ['privacy' => route('cmsPrivacy')]) !!}
                        </div>
                    </div>
                    {{-- Step 4 --}}
                    <div class="step d-none" data-step="4">
                        <div class="popup_text_normal mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div class="popup_text_special">
                            {!! trans_cms('verify.step4', ['terms' => route('cmsAgreement')]) !!}
                        </div>
                    </div>
                    {{-- Step 5 --}}
                    <div class="step d-none" data-step="5">
                        <div class="popup_text_normal mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div class="popup_text_special">
                            {!! trans_cms('verify.step5') !!}
                        </div>
                    </div>
                    {{-- Step 6 --}}
                    <div class="step d-none" data-step="6">
                        <div class="popup_text_normal mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div class="popup_text_special">
                            {!! trans_cms('verify.step6',
                                [
                                    'terms' => route('cmsAgreement'),
                                    'privacy' => route('cmsPrivacy'),
                                    'cookies' => route('cmsCookies')
                                ]
                            ) !!}
                        </div>
                    </div>
                    {{-- Step Reject --}}
                    <div class="step-reject d-none">
                        <div class="popup_text_special">
                            {{ __('Are you sure you want to reject the Terms & Conditions and the Privacy Statement?') }}<br /><br />
                            {{ __('Your account will be blocked immediately and you won\'t be able to use this service.') }}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="steps-agree-decline text-center">
                        <a href="#" class="button-decline step-decline">
                            {{ __('No, I reject') }}
                        </a>
                        <button class="button-agree step-agree">
                            {{ __('Yes, I agree!') }}
                        </button>
                    </div>
                    <div class="steps-confirm-agree-decline d-none text-center">
                        <button type="button" class="button-decline step5-decline">{{ __('No, I reject') }}</button>
                        <form action="{{ route('verifyPrivacy') }}" method="POST" class="button-agree">
                            @csrf
                            <button type="submit">{{ __('Yes, I agree!') }}</button>
                        </form>
                    </div>
                    <div class="step-reject d-none text-center">
                        <form action="{{ route('verifyPrivacyReject') }}" method="POST" class="button-decline">
                            @csrf
                            <button type="submit">{{ __('Yes, I am sure') }}</button>
                        </form>
                        <button type="button" class="button-agree step-reject-agree">{{ __('No, proceed') }}</button>
                    </div>
                    <div class="redirect-message d-none text-center">
                        You will be redirected in <strong>5</strong> seconds.
                    </div>
                </div>
            </div>
        </div>
    </div>
@else
    <div id="popup-privacy-verify" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade modal-basic" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-center">
                        <img src="{{ cdn($domain) }}/images/{{ strtolower($domain->name) }}.png"/>
                    </h5>
                </div>
                {{-- Step 1 --}}
                <div class="modal-body">
                    <div class="step1">
                        <div class="popup_text_normal mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div class="popup_text_special mb-4">
                            {{ __('By signing up I certify that I am at least 18 years old and the age of majority in my jurisdiction to use this adult entertainment website. I don\'t mind receiving sexual orientated text and images that are explicit.') }}
                        </div>
                        <div class="popup_text_normal">
                            {{ __('Click, "Yes, I agree" to proceed or "No, I reject" if you want to leave this Website now!') }}
                        </div>
                    </div>
                    {{-- Step 2 --}}
                    <div class=" step2 d-none">
                        <div class="popup_text_normal mb-4">
                            {{ __('Dear :username,',['username' => $user->username]) }}<br /><br />
                            {{ trans_cms('verify.common', ['url' => $domain->name_url]) }}
                        </div>
                        <div class="popup_text_special">
                            {!! __('I am aware and acknowledge that this is an adult entertainment website and have read and agree with the whole <a href=":terms" target="_blank">Terms & Conditions</a>, especially section 8 about the nature of this fantasy service.', ['terms' => route('cmsAgreement')]) !!}
                            <br /><br />
                            {!! __('I do not object to sharing my personal details with you and that you use these in the manner as stated in the <a href=":url" target="_blank">privacy statement</a>.',['url' => route('cmsPrivacy')]) !!}
                            <br /><br />
                            {{ __('I wish to be kept informed of new exciting products and promotions that are adult related.') }}
                        </div>
                    </div>
                    {{-- Step Reject --}}
                    <div class=" step-reject d-none">
                        <div class="popup_text_special">
                            {{ __('Are you sure you want to reject the Terms & Conditions and the Privacy Statement?') }}<br /><br />
                            {{ __('Your account will be blocked immediately and you won\'t be able to use this service.') }}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="step1 text-center">
                        <a href="#" class="button-decline step1-decline">
                            {{ __('No, I reject') }}
                        </a>
                        <button class="button-agree step1-agree">
                            {{ __('Yes, I agree!') }}
                        </button>
                    </div>
                    <div class="step2 d-none text-center">
                        <button type="button" class="button-decline step2-decline">{{ __('No, I reject') }}</button>
                        <form action="{{ route('verifyPrivacy') }}" method="POST" class="button-agree">
                            @csrf
                            <button type="submit">{{ __('Yes, I agree!') }}</button>
                        </form>
                    </div>
                    <div class="step-reject d-none text-center">
                        <form action="{{ route('verifyPrivacyReject') }}" method="POST" class="button-decline">
                            @csrf
                            <button type="submit">{{ __('Yes, I am sure') }}</button>
                        </form>
                        <button type="button" class="button-agree step3-agree">{{ __('No, proceed') }}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
@push('scripts')
    <script>
    $( document ).ready(function() {
        @if(\Illuminate\Support\Str::startsWith($domain->locale, 'en'))
        var popupStep = 1;

        $('#popup-privacy-verify').modal('show');

        // Go to next step
        $('.step-agree').on('click', function(){
            // If current step is step 5, then confirm in the background while showing step 6 for 5 seconds
            if(popupStep === 5) {
                $('.steps-agree-decline').addClass('d-none');
                $('.redirect-message').removeClass('d-none');
                $.ajax({
                    type: "POST",
                    url: "{{ route('verifyPrivacyJsonResponse') }}",
                    success: setTimeout(hidePopups, 5000)
                });
            }

            $('.step[data-step="'+popupStep+'"]').addClass('d-none');
            popupStep++;
            $('.step[data-step="'+popupStep+'"]').removeClass('d-none');
        });

        // Show confirmation screen if user really wants to decline
        $('.step-decline').on('click', function(){
            $('.step').addClass('d-none');
            $('.steps-agree-decline').addClass('d-none');
            $('.step-reject').removeClass('d-none');
        });

        // Cancel the decline - go back to step 1
        $('.step-reject-agree').on('click', function(){
            popupStep = 1;
            $('.step').addClass('d-none');
            $('.step[data-step="'+popupStep+'"]').removeClass('d-none');
            $('.step-reject').addClass('d-none');
            $('.steps-agree-decline').removeClass('d-none');
        });

        function hidePopups() {
            $('#popup-privacy-verify').modal('hide');
        }
        @else
        $('#popup-privacy-verify').modal('show');

        $('.step1-agree').on('click', function(){
            $('.step1').addClass('d-none');
            $('.step2').removeClass('d-none');
        });

        $('.step1-decline').on('click', function(){
            $('.step1').addClass('d-none');
            $('.step-reject').removeClass('d-none');
        });

        $('.step2-decline').on('click', function(){
            $('.step2').addClass('d-none');
            $('.step-reject').removeClass('d-none');
        });

        $('.step3-agree').on('click', function(){
            $('.step1, .step2').addClass('d-none');
            $('.step1').removeClass('d-none');
            $('.step-reject').addClass('d-none');
        });
        @endif
    });
    </script>
@endpush
