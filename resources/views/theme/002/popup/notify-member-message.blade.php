<!-- modal: disable notification if member sends message -->
<div class="modal fade modal-basic sure-notification-members-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
	<div class="modal-dialog modal-dialog-centered modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">{{ __('Are you sure?') }}</h5>
				<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="title">
					{{ __('When you set this option to none you won\'t receive emails about members sending you a message.') }}
				</div>
				<div class="text">
					{{ __('You can adjust the frequency so you will receive less emails, but you will not miss a new message.') }}
				</div>
			</div>
			<div class="modal-footer yes-no">
				<div>
					<button class="agree receive-messages" data-bs-dismiss="modal">
						{{ __('Keep receiving emails') }}
					</button>
					<button class="decline" data-bs-dismiss="modal">
						{{ __('Set to none') }}
					</button>
				</div>
			</div>
		</div>
	</div>
</div>