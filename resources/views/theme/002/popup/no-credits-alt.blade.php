<!-- modal: no credits (when user enters profile page of woman with no credits) -->
<div class="modal fade modal-basic no-credits-alt-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
	<div class="modal-dialog modal-dialog-centered modal-lg">
		<form method="POST" action="#" class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">{{ __('Watch your credits') }}</h5>
				<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="title">
					{{ __('You have no credits left to send a message to one of the lovely ladies. Make sure to upgrade your credits so you will be able to send messages and experience the full service.') }}
				</div>
			</div>
			<div class="modal-footer">
				<button class="main-button extrawide modal-click">
					{!! __('Buy :credits messages for only :price <span class="discount">:discount% discount!</span>', [
                        'credits' => $offer->credits,
                        'price' => $offer->price,
                        'discount' => $offer->discount,
                        ]) !!}
				</button>
				<div class="text-below-button modal-click">
					{!! __('Or <a href=":credits">select a different bundle</a>', ['credits' => route('pay')]) !!}
				</div>
				<div style="clear: both;"></div>
			</div>
		</form>
	</div>
</div>
@push('scripts')
 <script>
	$( document ).ready(function() {
        $('.no-credits-alt-modal').modal('show');
		$('.no-credits-alt-modal').on('hide.bs.modal', function (e) {
			$.post( "{{ route('saveNotification',['type' => 'popup', 'name' => 'no-credits-profile', 'action' => 'dismiss']) }}");
		})
		$('.no-credits-alt-modal .modal-click').on('click', function (e) {
			$.post( "{{ route('saveNotification',['type' => 'popup', 'name' => 'no-credits-profile', 'action' => 'click']) }}");
		})
    });
 </script>
@endpush
