@extends('theme.' . $domain->theme . '.layouts.simple')
@section('content')
    <div class="no-credit-container">
        <div class="credit-header d-none d-md-block">
            <div class="credit-header-containment row">
                <div class="col">
                    <a href="{{ route('home') }}">
                        <img src="{{ cdn($domain) }}/images/{{ strtolower($domain->name) }}.png" class="logo-header">
                    </a>
                </div>
            </div>
        </div>
        <div class="container">
            @include('global.various.shutdown')
            <form class="products" style="" method="POST"
                action="{{ route($formRoute) }}" accept-charset="UTF-8"
                id="transaction"><input type="hidden" name="_token" value="{{ csrf_token() }}">
                @if (isset($orderId) && $orderId !== false)
                    <input type="hidden" name="order_id" value="{{ $orderId }}">
                @endif
                <div class="row g-1 justify-content-center text-center mb-4">
                    <div class="col-12">
                        <div class="row">
                            <div class="col-2 pt-2">
                                <a class="small-round-button-3 d-flex justify-content-center align-items-center mt-2 back-1"
                                    href="{{ $back }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                                <div
                                    class="small-round-button-3 d-flex justify-content-center align-items-center mt-2 back-2 d-none previous show-previous">
                                    <i class="fas fa-chevron-left"></i>
                                </div>
                            </div>
                            <div class="col">
                                <h1 class="choose-bundle">
                                    @if ($is_offer)
                                        1. {{ __('Take advantage of this one-time offer!') }}
                                    @else
                                        1. {{ __('Choose a bundle') }}
                                    @endif
                                </h1>
                                <h1 class="select-payment d-none">2.{{ __('Select payment method') }}</h1>
                            </div>
                            <div class="col-2">
                            </div>
                        </div>
                        <div class="row g-0 ms-auto me-auto card-steps">
                            <div class="col-auto text-center">
                                <div class="small-round-button-5 d-flex justify-content-center align-items-center mt-2">
                                    <i class="fas fa-gift checkout-active"></i>
                                </div>
                                <span class="text-dark">{{ __('Bundles') }}</span>
                            </div>
                            <div class="col">
                                <div class="card-line"></div>
                            </div>
                            <div class="col-auto">
                                <div class="small-round-button-5 d-flex justify-content-center align-items-center mt-2">
                                    <i class="far fa-credit-card"></i>
                                </div>
                                <span class="text-dark">{{ __('Payment') }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 mt-3">
                        <div class="row row-cols-1 g-2 ">
                            @foreach ($products as $product)
                                <div class="col @if ($products->count() == 1) d-none @endif">
                                    <input
                                        data-credits="{{ $product->credits }}"
                                        data-price="{{ $product->toPay() }}"
                                        autocomplete="off"
                                        class="product-radio d-none"
                                        type="radio"
                                        id="product-radio-{{ $product->id }}"
                                        name="product_id"
                                        value="{{ $product->id }}"
                                        @if ($product->id == $defaultSelected || $is_offer) checked @endif>

                                    <label for="product-radio-{{ $product->id }}"
                                        class="product-label next show-next @if ($bestseller && $product->best_seller) popular-product @endif"
                                        style="line-height: 22px;">
                                        <div class="row g-0 d-flex align-items-center justify-content-center">
                                            <div
                                                class="col-auto h-100 py-2 py-md-3  px-2 px-md-3 section-credits d-flex align-items-center justify-content-center">
                                                <div>
                                                    <h4 class="mb-1">{{ $product->credits }}</h4>
                                                    <div>{{ __('Credits') }}</div>
                                                </div>
                                            </div>
                                            <div class="col h-100">
                                                <div class="h-100 d-flex align-items-center justify-content-center">
                                                    <div class="position-relative w-100">
                                                        @if (!$user->isPremium())
                                                            <div class="coin-icons" style="">
                                                                @if ($product->credits < 11)
                                                                    <img
                                                                        src="{{ cdn($domain) }}/images/gold-icon-1.png" />
                                                                @elseif($product->credits < 26)
                                                                    <img
                                                                        src="{{ cdn($domain) }}/images/gold-icon-2.png" />
                                                                @elseif($product->credits < 51)
                                                                    <img
                                                                        src="{{ cdn($domain) }}/images/gold-icon-3.png" />
                                                                @elseif($product->credits < 101)
                                                                    <img
                                                                        src="{{ cdn($domain) }}/images/gold-icon-4.png" />
                                                                @elseif($product->credits < 201)
                                                                    <img
                                                                        src="{{ cdn($domain) }}/images/gold-icon-5.png" />
                                                                @else
                                                                @endif
                                                            </div>
                                                        @endif
                                                        <div
                                                            class="row g-0 d-flex align-items-center justify-content-center">
                                                            @if ($user->isPremium())
                                                                <div
                                                                    class="col-auto d-flex px-2 align-items-center justify-content-center free-credit-box">
                                                                    <div>
                                                                        <i class="fas fa-gift fs-4"></i><br />
                                                                        + {{ ceil($product->credits * 0.1) }}
                                                                        {{ __('VIP credits') }}
                                                                    </div>
                                                                </div>
                                                            @else
                                                            @endif
                                                            <div class="col fw-bold fs-5">
                                                                @if ($bestseller && $product->best_seller)
                                                                    <div class="badge rounded-pill special-notice">
                                                                        {{ __('BEST SELLER') }}
                                                                    </div>
                                                                @endif
                                                                @if ($product->discount > 0)
                                                                    <div class="badge rounded-pill special-notice">
                                                                        {{ $product->discount }}%
                                                                        {{ __('OFF') }}
                                                                    </div>
                                                                @endif
                                                                @if ($product->credits == 200)
                                                                    <div
                                                                        class="badge rounded-pill special-notice orange-badge">
                                                                        {{ __('BEST VALUE') }}
                                                                    </div>
                                                                @endif
                                                                <div class="position-relative">
                                                                    @if ($product->amount > $product->toPay())
                                                                        <span
                                                                            class="discount-amount">{{ currency($domain, $product->amount) }}</span><br />
                                                                    @endif
                                                                    {{ currency($domain, $product->toPay()) }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-auto h-100">
                                                <button class="btn btn-primary px-3 px-lg-4 fw-bold buy-button-1 pe-none">
                                                    <span class="d-none d-lg-block"> {{ __('Continue') }} <i
                                                            class="far fa-hand-point-right"></i></span>
                                                    <span class="d-lg-none"><i class="far fa-hand-point-right"></i></span>
                                                </button>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="row payment-methods @if ($products->count() > 1) d-none @endif payment-section">
                    <div class="col-12">
                        <div class="row g-3 justify-content-md-center p-0">
                            @include('theme.' . $domain->theme . '.payment.includes.payment-methods')
                        </div>
                    </div>
                    <div class="col-12 mt-3">
                        <div class="row g-1">
                            @if ($meta = $domain->getMeta('scheduled_shutdown'))
                                <div class="col-6 col-md-12">
                                    <div class="alert alert-danger" role="alert">
                                        {{ __('Buying credits is not available') }}
                                    </div>
                                </div>
                            @else
                                <div class="col-12 text-center">
                                    <button type="submit" class="buy-now buy-button">{{ __('Validate payment') }}
                                        <i class="fa fa-arrow-right" aria-hidden="true"></i></button>
                                </div>
                            @endif

                            <div class="col-12 text-center mt-3">
                                <div class="small">{{ __('This is a one time payment. You will not be billed again.') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div class="row">
                <div class="col-md-12">
                    <div class="row justify-content-center mb-4 mt-5">
                        <div class="col-4 col-sm-3 col-md-2 footer-icon">
                            <i class="fas fa-check"></i>
                            {{ __('100% anonymous') }}
                        </div>
                        <div class="col-4 col-sm-3 col-md-2 footer-icon">
                            <i class="fas fa-lock"></i>
                            {{ __('100% secure payment') }}
                        </div>
                        <div class="col-4 col-sm-3 col-md-2 footer-icon">
                            <i class="fas fa-money-check"></i>
                            {{ __('No subscription!') }}
                        </div>
                    </div>
                    <div class="row justify-content-center">
                        <div class="col-md-9">
                            <p class="small text-center">
                                {!! __(
                                    'We respect the privacy of our users. Any charges made on your credit card will appear under: :credit_card_provider. This is no subscription and your credit card will not be re-billed by making a purchase on this site, you agree to our <a href=":terms">Terms & Conditions</a>.',
                                    ['terms' => route('cmsAgreement'), 'credit_card_provider' => $domain->company->payment_provider_title],
                                ) !!}<br />
                                <a href="{{ $domain->company->payment_provider_url }}"
                                    target="_blank">{{ ucfirst($domain->company->payment_provider_title) }}</a> |
                                <a href="{{ route('cmsContact') }}">{{ __('Other Support') }}</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('theme.' . $domain->theme . '.payment.includes.payment-verify')
@endsection
@push('scripts')
    <script>
        $('.buy-now').on('click', function(ev) {
            $(this).html('{{ __('Please wait...') }}');
        });
    </script>
    <script>
        $(document).on("click", ".outOfCreditsHarmonicaSection1 .outOfCreditsBundleRadioButton", function() {
            var id = $(this).val();
            var credits = $(this).data('credits');
            var price = $(this).data('price') * 100;
            var hiddenSelected = false;
            $('.price-analysis').addClass('d-none');
            $('#' + id).removeClass('d-none');

            $('input[name="payment_method_id"]').each(function(idx, elem) {
                if (priceMax == 0 && priceMin == 0) {
                    return
                }
                var priceMin = $(elem).data('amount_min') * 100;
                var priceMax = $(elem).data('amount_max') * 100;
                $(elem).next('label').removeClass('d-none');
                if (priceMax > 0 || priceMin > 0) {
                    if (priceMax < price) {
                        if ($(elem).is(':checked')) {
                            $(elem).prop("checked", false);
                        }
                        $(elem).next('label').addClass('d-none');
                        hiddenSelected = true;
                    } else {
                        if (priceMin > price) {
                            if ($(elem).is(':checked')) {
                                $(elem).prop("checked", false);
                            }
                            $(elem).next('label').addClass('d-none');
                        }
                        hiddenSelected = true;
                    }
                }
            });
            if (hiddenSelected) {
                $('input[name="payment_method_id"]').each(function(idx, elem) {
                    if ($(elem).next('label:visible')) {
                        $(elem).prop('checked', true);
                        return false;
                    }
                });
            }
        });
        $(document).ready(function() {
            /* mobile - initial state */
            $(".no-credit-container .outOfCreditsHarmonicaContent3").addClass("d-none");
            /* open the correct section on mobile */
            $(".no-credit-container #harmonicaMobileStep1-Next").click(function() {
                $(".outOfCreditsHarmonicaContent").addClass('d-none');
                $(".outOfCreditsHarmonicaContent3").removeClass('d-none');
            });
            $(".no-credit-container #harmonicaMobileStep2-Previous").click(function() {
                $(".outOfCreditsHarmonicaContent").addClass('d-none');
                $(".outOfCreditsHarmonicaContent1").removeClass('d-none');
            });
            $(".no-credit-container #harmonicaMobileStep2-Next").click(function() {
                $(".outOfCreditsHarmonicaContent").addClass('d-none');
                $(".outOfCreditsHarmonicaContent3").removeClass('d-none');
            });
            $(".no-credit-container #harmonicaMobileStep3-Previous").click(function() {
                $(".outOfCreditsHarmonicaContent").addClass('d-none');
                $(".outOfCreditsHarmonicaContent1").removeClass('d-none');
            });
            /* open the correct section on mobile - when clicking on the title */
            $(".no-credit-container .outOfCreditsHarmonicaTitle").css("cursor", "pointer");
            $(".no-credit-container .outOfCreditsHarmonicaSection1 .outOfCreditsHarmonicaTitle").css("cursor",
                "default");
            $(".no-credit-container .outOfCreditsHarmonicaSection1 .outOfCreditsHarmonicaTitle").click(function() {
                $(".no-credit-container .outOfCreditsHarmonicaContent").addClass('d-none');
                $(".no-credit-container .outOfCreditsHarmonicaContent1").removeClass('d-none');
                $(".no-credit-container .outOfCreditsHarmonicaTitle").css("cursor", "pointer");
                $(".no-credit-container .outOfCreditsHarmonicaSection1 .outOfCreditsHarmonicaTitle").css(
                    "cursor",
                    "default");
            });
            $(".no-credit-container .outOfCreditsHarmonicaSection2 .outOfCreditsHarmonicaTitle").click(function() {
                $(".no-credit-container .outOfCreditsHarmonicaContent").addClass('d-none');
                $(".no-credit-container .outOfCreditsHarmonicaContent2").removeClass('d-none');
                $(".no-credit-container .outOfCreditsHarmonicaTitle").css("cursor", "pointer");
                $(".no-credit-container .outOfCreditsHarmonicaSection2 .outOfCreditsHarmonicaTitle").css(
                    "cursor",
                    "default");
            });
            $(".no-credit-container .outOfCreditsHarmonicaSection3 .outOfCreditsHarmonicaTitle").click(function() {
                $(".no-credit-container .outOfCreditsHarmonicaContent").addClass('d-none');
                $(".no-credit-container .outOfCreditsHarmonicaContent3").removeClass('d-none');
                $(".no-credit-container .outOfCreditsHarmonicaTitle").css("cursor", "pointer");
                $(".no-credit-container .outOfCreditsHarmonicaSection3 .outOfCreditsHarmonicaTitle").css(
                    "cursor",
                    "default");
            });
        });
        $(".show-next").click(function() {
            $(".fa-gift").removeClass('checkout-active');
            $(".fa-credit-card").addClass('checkout-active');
            $(".choose-bundle").addClass('d-none');
            $(".select-payment").removeClass('d-none');
            $(".back-1").addClass('d-none');
            $(".back-2").removeClass('d-none');
        });
        $(".show-previous").click(function() {
            $(".fa-gift").addClass('checkout-active');
            $(".fa-credit-card").removeClass('checkout-active');
            $(".choose-bundle").removeClass('d-none');
            $(".select-payment").addClass('d-none');
            $(".back-1").removeClass('d-none');
            $(".back-2").addClass('d-none');
        });
    </script>
@endpush
