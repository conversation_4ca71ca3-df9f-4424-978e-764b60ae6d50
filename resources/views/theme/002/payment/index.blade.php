@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <div class="container-main-space-for-menu container-vip">
        @include('global.various.shutdown')
        <h1>{{ __('Premium Membership') }}</h1>
        <div class="container-vip-content row py-2 w-100 mx-auto">
            <div class="vip-banner col-lg-6 col-xl-5 mb-5">
                <div style="position: relative;">
                    <div id="scroll-to-vip-packages" class="vip-button">{{ __('Choose your package!') }} <i
                            class="fas fa-angle-down fs-4 ms-2"></i></div>
                    <div class="reasonsforvip">
                        <div class="row g-2 row-cols-1 text-start fs-5">
                            <div class="col">
                                <div class="row g-2 align-items-center">
                                    <div class="col-auto"><i class="fas fa-unlock-alt fa-fw"></i></div>
                                    <div class="col">{{ __('Unlock the private images of all members') }}</div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="row g-2 align-items-center">
                                    <div class="col-auto"><i class="fas fa-question fa-fw"></i></div>
                                    <div class="col">{{ __('See who visited your profile') }}</div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="row g-2 align-items-center">
                                    <div class="col-auto"><i class="far fa-window-maximize fa-fw"></i></div>
                                    <div class="col">{{ __('View photos of all members in full size') }}</div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="row g-2 align-items-center">
                                    <div class="col-auto"><i class="far fa-heart fa-fw"></i></div>
                                    <div class="col">{{ __('10% extra credits on every purchase') }}</div>
                                </div>
                            </div>
                            <div class="col">
                                <div class="row g-2 align-items-center">
                                    <div class="col-auto"><i class="far fa-smile fa-fw"></i></div>
                                    <div class="col">{{ __('Receive 15 free credits every month') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <img class="mb-5"
                        src="{{ cdn($domain) }}/images/{{ $domain->is_whitelabel ? 'vip-whitelabel' : 'model-vip-alt' }}.png">
                </div>
            </div>
            <div class="vip-membership col ps-xl-5 ms-auto" id="vip-packages">
                @if ($domain->hasMeta('scheduled_shutdown'))
                    <div class="alert alert-danger" role="alert">
                        {{ __('VIP membership is not available') }}
                    </div>
                @else
                    <form action="{{ route('postTransactionPremium') }}" method="POST" class="packages" id="transaction">
                        @csrf
                        @if (isset($orderId) && $orderId !== false)
                            <input type="hidden" name="order_id" value="{{ $orderId }}">
                        @endif
                        <div class="mb-5">
                            @if ($is_offer)
                                <div class="title">{{ __('Special offer!') }}</div>
                            @else
                                <div class="title">{{ __('Select your package') }}</div>
                            @endif
                            @include('theme.' . $domain->theme . '.payment.includes.products')
                            <div class="title">{{ __('Select payment method') }}</div>
                            <div class="row g-1 justify-content-center payment-methods d-md-flex payment-section mb-2">
                                @include('theme.' . $domain->theme . '.payment.includes.payment-methods')
                            </div>
                            <div><button type="submit" class="buy-now buy-button">{{ __('Buy Now!') }}</button></div>
                            <div class="col-12 text-center mt-3">
                                <div class="small">{{ __('This is a one-time payment and not a recurring membership.') }}
                                </div>
                            </div>
                        </div>
            </div>
            <span style="display:block; position:relative; clear:both"></span>
        </div>
        <span style="display:block; position:relative; clear:both"></span>
        </form>
        @endif
    </div>
    </div>
    <div style="clear:both"></div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {
            $('button[name=product_id]').click(function(event) {
                @foreach ($domain->paymentMethods as $method)
                    @if ($method->visible === 1 && $method->provider->name === 'SegPay')
                        if ($('input[name=payment_method_id]:checked').val() === '{{ $method->id }}') {
                            event.preventDefault();
                            let urlTransactionForm = $(".packages").attr('action');
                            let _token = $("input[name=_token]").val();
                            let payment_method_id = $("input[name=payment_method_id]:checked").val();
                            let product_id = $(this).val();
                            $.ajax({
                                type: "POST",
                                url: urlTransactionForm,
                                data: {
                                    _token: _token,
                                    product_id: product_id,
                                    payment_method_id: payment_method_id
                                },
                                success: function(response) {
                                    var obj = JSON.parse(response);
                                    $('body').html(obj.form);
                                    $('#segpayForm').submit();
                                }
                            });
                        }
                    @endif
                @endforeach
            });

            // Scroll to Premium packages
            $('#scroll-to-vip-packages').click(function(e) {
                $('html, body').animate({
                    scrollTop: $('#vip-packages').offset().top - 150
                }, 1000);
            });

            $('input[name=payment_method_id]').change(function(event) {
                let priceMin = $(this).data('amount_min') * 100;
                let priceMax = $(this).data('amount_max') * 100;
                $('button[name=product_id]').each(function(idx, elem) {
                    let price = $(elem).data('price') * 100;
                    $(elem).prop('disabled', false);
                    $(elem).removeClass('btn btn-gray');
                    if (priceMax > 0 || priceMin > 0) {
                        if (priceMax < price) {
                            $(elem).prop('disabled', true);
                            $(elem).addClass('btn btn-gray');
                        } else {
                            if (priceMin > price) {
                                $(elem).prop('disabled', true);
                                $(elem).addClass('btn btn-gray');
                            }
                        }
                    }
                });
            });
        });
    </script>
@endpush
