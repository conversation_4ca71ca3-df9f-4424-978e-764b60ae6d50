@extends('theme.'. $domain->theme .'.layouts.master')

@section('content')
@if($order->isForDefaultProduct())
	<div class="container-main-space-for-menu container-payment-message">
		<h1>
			{{ __('Thank you for your purchase!') }} <i class="fas fa-check"></i>
		</h1>
		<div class="text-1">
			@if($user->isPremium())
			{{ __('Your payment has been successfully processed.') }}
			{!! __('Because you are a Premium member you receive <span>:credits</span> extra credits.', ['credits' => ceil($order->product->credits * 0.10)]) !!}
			{{ __('The amount of your credits is now') }} <span>{{ $user->credits }}</span>.
				@else
			{{ __('Your payment has been successfully processed. The amount of your credits is now') }} <span>{{ $user->credits }}</span>.
			@endif
		</div>
	@if(isset($profile))
	<div class="text-2">
		{{ __('Would you like to continue your conversation with') }} {{ $profile->username }}?
	</div>
	<div class="buttons single-button">
		<div>
			<a href="{{ route('conversation', ['type' => 'conversation', 'username' => $profile->username]) }}">
				<span>{{ __('Go to chat') }} <i class="fas fa-comment"></i></span>
			</a>
		</div>
		<span style="display:block; position:relative; clear:both;"></span>
	</div>
	@else
	<div class="text-2">
		{{ __('Would you like to return to your inbox?') }}
	</div>
	<div class="buttons single-button">
		<div>
			<a href="{{ route('messages', ['type' => 'all']) }}">
					<span>{{ __('Go to inbox') }} <i class="fas fa-comment"></i></span>
				</a>
			</div>
		<span style="display:block; position:relative; clear:both;"></span>
	</div>
	@endif
</div>
@endif
@if($order->isForPremiumProduct())
<div class="container-main-space-for-menu container-payment-message">
	<h1>
		{{ __('Thank you for your purchase!') }} <i class="fas fa-check"></i>
	</h1>
	<div class="text-1">
		{!! __('Your payment has been successfully processed. You can now access the <span>Premium zone</span>.') !!}
	</div>
	<div class="buttons single-button">
		<div>
			<a href="{{ route('premiumOverview') }}">
				<span>{{ __('Go to Premium zone') }} <i class="fas fa-crown"></i></span>
			</a>
		</div>
		<span style="display:block; position:relative; clear:both;"></span>
	</div>
</div>
@endif

<!-- START ExoClick Goal Tag | Paid -->
<script type="application/javascript" src="https://a.magsrv.com/tag_gen.js" data-goal="e8e0250b6222ace5b3803bda8875b66a" ></script>
<!-- END ExoClick Goal Tag | Paid -->
<img src="https://ads.trafficjunky.net/rt?action=list&type=add&id=0&context=alllabels&cookiename=paymentpagevisit&age=90&maxcookiecount=10" />
@endsection
@push('ga-scripts')
<meta http-equiv="Delegate-CH" content="sec-ch-ua https://ads.trafficjunky.net; sec-ch-ua-arch https://ads.trafficjunky.net; sec-ch-ua-full-version-list https://ads.trafficjunky.net; sec-ch-ua-mobile https://ads.trafficjunky.net; sec-ch-ua-model https://ads.trafficjunky.net; sec-ch-ua-platform https://ads.trafficjunky.net; sec-ch-ua-platform-version https://ads.trafficjunky.net;">

<script>
$( document ).ready(function() {
	window.dataLayer = window.dataLayer || [];
	dataLayer.push({ ecommerce: null });
	dataLayer.push({
		'event' : 'purchase',
		'ecommerce': {
			'transaction_id': '{{ $order->id }}',
			'value': '{{ $order->amount }}',
			'currency': '{{ strtoupper($domain->currency) }}',
			'items': [{
				'item_name': '{{ $order->product->name }}',
				'item_id': '{{ $order->product_id }}',
				'item_category': '{{ $order->isForDefaultProduct() ? "Credits" : "Premium" }}',
				'price': '{{ $order->amount }}',
				'currency': '{{ strtoupper($domain->currency) }}'
			}]
		}
	});

	window._vis_opt_queue = window._vis_opt_queue || [];
	window._vis_opt_queue.push(function() {
		_vis_opt_revenue_conversion({{ $order->amount }});
	});
});
</script>
@endpush

