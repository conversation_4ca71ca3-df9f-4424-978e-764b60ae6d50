@php $messageLink = route('conversation', ['type' => 'conversation', 'username' => $message->profile->username]); @endphp
<div class="section row g-0 justify-content-between @if(!$message->read) unread @endif">
    <div class="select ps-1">
        <input data-username="{{ $message->profile->username }}"
                data-favorite="{{ in_array($message->profile->id, $favorites) ? 1 : 0 }}"
                data-conversation="{{ $message->isMutual() ? 1 : 0 }}"
                type="checkbox" class="select-message" name="selected" value="{{ $message->profile_id }}">
    </div>
    <div class="col-7 col-md-3">
        <a href="{!! $messageLink !!}" class="from">
            <div class="profile-photo">
                <div class="position-relative d-block">
                    <img src="{{ thumb($message->profile->profile_image, $message->profile->gender) }}">
                    <i class="fas fa-circle is-online @if ($message->profile->isOnline()) text-success @else text-secondary @endif"></i>
                </div>
            </div>
            <div class="profile-info">
                <span class="profile-name">
                    {{ $message->profile->username }}</span>
                <i class="fas fa-heart heart-fave-messages"></i>
                <span class="message-sent small">{{ humantime($message->last_sent_at) }}</span>
            </div>
        </a>
    </div>
    <div class="message-text col-12 col-md-7 order-4 order-md-3 mt-1 pt-1">
        <a @if($loop->first && !$message->read) id="popover-unread-message"
            data-bs-trigger="manual" data-bs-toggle="popover" data-bs-placement="right"
            data-content="{!! __('When you receive a new message it will be indicated by bold text') !!}" @endif
            href="{!! $messageLink !!}" class="message">

            @if($message->last->upload_id && $attachment = $message->last->upload)
                <img class="upload-attachment" src="{{ thumb($attachment->file, $message->profile->gender) }}" alt="{{ __('Message Attachment') }}">
            @endif
            @if(mb_strlen(strip_tags($message->last->content)) > 200)
            {!! Str::limit(strip_tags($message->last->content), 200) !!}
            @else
            {!! $message->last->content !!}
            @endif
        </a>
    </div>
    <div class="col-4 col-md-1 order-3 order-md-4">
        <a href="{!! $messageLink !!}" class="btn btn-secondary">
            <span class="view-message">{{ __('View') }}</span>
            <span class="arrow-message"><i class="fa fa-arrow-right" aria-hidden="true"></i></span>
        </a>
    </div>
</div>
