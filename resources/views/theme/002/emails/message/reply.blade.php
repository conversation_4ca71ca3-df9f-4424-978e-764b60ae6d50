@extends('theme.'. $domain->theme .'.emails.master')
@section('preheader')
<table>
	<tr>
		<td style="display:none !important; visibility:hidden; mso-hide:all;
            font-size:1px; color:#fcf7f7; line-height:1px; max-height:0px;
            max-width:0px; opacity:0; overflow:hidden;">
            {{ __(':profile has replied to your message!', ['profile' => $profile]) }}. {{ __('Reply now') }}
		</td>
	</tr>
</table>
@endsection
@section('content')
<tr>
    <td valign="top" align="center"><table width="100%" border="0" cellspacing="0" cellpadding="0" align="center" bgcolor="#faf9f9" style="background-color:#faf9f9;">
      <tbody><tr>
        <td valign="top" align="center" style="padding:26px 30px 42px 30px;" class="em_ptblr"><table width="100%" border="0" cellspacing="0" cellpadding="0" align="center">
        <tbody><tr>
          <td valign="top" align="center" class="em_defaultlink" style="font-size:16px; line-height:19px; color:#a7a7a7; font-family:Arial, sans-serif;"><strong>{{ __('Hi') }} {{ $username }},</strong> <br><br> {{ __(':profile has replied to your message!', ['profile' => $profile]) }}</td>
        </tr>
        <tr>
          <td valign="top" align="center" style="padding-top:32px;" class="em_ptop"><table width="295" border="0" cellspacing="0" cellpadding="0" align="center" style="width:295px; max-width:295px;" class="em_wrapper">
          <tbody><tr>
            <td valign="middle" align="center" height="50" bgcolor="{{ $domain->theme('email-color1') }}" style="height:50px; background-color:{{ $domain->theme('email-color1') }}; border-radius:50px; font-size:21px; color:#ffffff; font-family:Arial, sans-serif;"><a href="{{ $message_url }}" target="_blank" style="text-decoration:none; color:#ffffff; line-height:50px; display:block;">{{ __('Reply now') }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="vertical-align:-6px; font-size:0px; line-height:0px;"><img src="{{ cdn($domain) }}/images/massage_icon.png" width="25" alt="Chat" border="0" style="display:inline-block; max-width:25px;"></span></a></td>
          </tr>
        </tbody></table>
        </td>
        </tr>
        <tr>
          <td valign="top" align="center" class="em_full_img em_ptop" style="padding-top:30px;"><img src="{{ cdn($domain) }}/images/image5.jpg" width="400" alt="" border="0" style="display:block; font-size:20px; line-height:24px; color:#000000; font-family:Arial, sans-serif; max-width:400px;"></td>
        </tr>
        <tr>
          <td valign="top" align="center" class="em_defaultlink" style="font-size:16px; line-height:19px; color:#a7a7a7; font-family:Arial, sans-serif; padding-top:26px;">{{ __(':profile has replied to your message!', ['profile' => $profile]) }}: <br><br> <span style="color:{{ $domain->theme('email-color2') }};">{!! Str::limit(strip_tags($messageContent), 110) !!}...</span></td>
        </tr>
        <tr>
          <td valign="top" align="center" style="padding-top:24px;" class="em_ptop"><table width="295" border="0" cellspacing="0" cellpadding="0" align="center" style="width:295px; max-width:295px;" class="em_wrapper">
          <tbody><tr>
            <td valign="middle" align="center" height="50" bgcolor="{{ $domain->theme('email-color2') }}" style="height:50px; background-color:{{ $domain->theme('email-color2') }}; border-radius:50px; font-size:21px; color:#ffffff; font-family:Arial, sans-serif;"><a href="{{ $message_url }}" target="_blank" style="text-decoration:none; color:#ffffff; line-height:50px; display:block;">{{ __('Send a message') }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="vertical-align:-6px; font-size:0px; line-height:0px;"><img src="{{ cdn($domain) }}/images/massage_icon.png" width="25" alt="Chat" border="0" style="display:inline-block; max-width:25px;"></span></a></td>
          </tr>
        </tbody></table>
        </td>
        </tr>
      </tbody></table>
      </td>
      </tr>
    </tbody></table>
    </td>
  </tr>
@endsection
