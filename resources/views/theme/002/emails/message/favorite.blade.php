@extends('theme.'. $domain->theme .'.emails.master')
@section('preheader')
<table>
	<tr>
		<td style="display:none !important; visibility:hidden; mso-hide:all;
            font-size:1px; color:#fcf7f7; line-height:1px; max-height:0px;
            max-width:0px; opacity:0; overflow:hidden;">
            {{ $profile->username }} {{ __('has added you as one of her likes') }}. {{ __('Contact her as soon as possible, before someone else does.') }}
		</td>
	</tr>
</table>
@endsection
@section('content')
<tr>
    <td valign="top" align="center" style="padding:22px 30px 34px 30px;" class="em_ptblr"><table width="100%" border="0" cellspacing="0" cellpadding="0" align="center">
    <tbody><tr>
      <td valign="top" align="center"><table width="540" border="0" cellspacing="0" cellpadding="0" align="center" style="width:540px;" class="em_wrapper">
     <tbody><tr>
       <td valign="top" align="center"><table width="285" border="0" cellspacing="0" cellpadding="0" align="left" style="width:285px;" class="em_wrapper">
        <tbody><tr>
          <td valign="top" align="center"><table width="285" border="0" cellspacing="0" cellpadding="0" align="center" style="width:285px;" class="em_wrapper">
           <tbody><tr>
             <td valign="top" align="center"><table width="100%" border="0" cellspacing="0" cellpadding="0" align="center">
               <tbody><tr>
                 <td valign="top" align="left" class="em_defaultlink" style="font-size:16px; line-height:24px; color:#a7a7a7; font-family:Arial, sans-serif;">
                    <strong>{{ __('Hey') }} {{ $user->username }},</strong> <br><br> {{ $profile->username }} {{ __('has added you as one of her likes') }} <br><br>
                    {{ __('Contact her as soon as possible, before someone else does.') }}
                </td>
               </tr>
             </tbody></table>
             </td>
             <td width="10" style="width:10px;" class="em_hide">&nbsp;</td>
           </tr>
         </tbody></table>
         </td>
        </tr>
      </tbody></table>
      <!--[if gte mso 9]></td><td valign="top"><![endif]-->
        <table width="255" border="0" cellspacing="0" cellpadding="0" align="right" style="width:255px;" class="em_wrapper">
        <tbody><tr>
          <td valign="top" align="center"><table width="255" style="width:255px;" class="em_wrapper" border="0" cellspacing="0" cellpadding="0" align="center">
          <tbody><tr>
            <td valign="top" align="left" class="em_full_img em_ptop" style="padding-top:52px;"><img src="{{ thumb($profile->profile_image, $profile->gender) }}" width="200" alt="" border="0" style="display:block; font-size:20px; line-height:24px; color:#000000; font-family:Arial, sans-serif; max-width:255px; border-radius:15px;"></td>
          </tr>
        </tbody></table>
        </td>
        </tr>
      </tbody></table>

      </td>
     </tr>
   </tbody></table>
   </td>
    </tr>
    <tr>
      <td valign="top" align="center" style="padding-top:32px;" class="em_ptop"><table width="540" border="0" cellspacing="0" cellpadding="0" align="center" style="width:540px;" class="em_wrapper">
      <tbody><tr>
        <td valign="top" align="center"><table width="255" border="0" cellspacing="0" cellpadding="0" align="left" style="width:255px;" class="em_wrapper">
        <tbody><tr>
          <td valign="top" align="center"><table width="255" border="0" cellspacing="0" cellpadding="0" align="left" style="width:255px; max-width:255px;" class="em_wrapper">
      <tbody><tr>
        <td valign="middle" align="center" height="50" bgcolor="{{ $domain->theme('email-color1') }}" style="height:50px; background-color:{{ $domain->theme('email-color1') }}; border-radius:50px; font-size:21px; color:#ffffff; font-family:Arial, sans-serif;">
            <a href="#" target="_blank" style="text-decoration:none; color:#ffffff; line-height:50px; display:block;">
                {{ __('See profile now') }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="vertical-align:-6px; font-size:0px; line-height:0px;"><img src="{{ cdn($domain) }}/images/search_icon.png" width="25" alt="Search" border="0" style="display:inline-block; max-width:25px;"></span>
            </a>
        </td>
      </tr>
    </tbody></table>
    </td>
        </tr>
      </tbody></table>
      <!--[if gte mso 9]></td><td valign="top"><![endif]-->
          <table width="285" border="0" cellspacing="0" cellpadding="0" align="right" style="width:285px;" class="em_wrapper">
        <tbody><tr>
          <td valign="top" align="center" class="em_ptop"><table width="100%" border="0" cellspacing="0" cellpadding="0" align="center">
           <tbody><tr>
             <td width="30" style="width:30px;" class="em_hide">&nbsp;</td>
             <td valign="top" align="center"><table width="255" border="0" cellspacing="0" cellpadding="0" align="left" style="width:255px; max-width:255px;" class="em_wrapper">
      <tbody><tr>
        <td valign="middle" align="center" height="50" bgcolor="{{ $domain->theme('email-color2') }}" style="height:50px; background-color:{{ $domain->theme('email-color2') }}; border-radius:50px; font-size:21px; color:#ffffff; font-family:Arial, sans-serif;">
            <a href="#" target="_blank" style="text-decoration:none; color:#ffffff; line-height:50px; display:block;">
                &nbsp;{{ __('Favorite now') }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="vertical-align:-2px; font-size:0px; line-height:0px;"><img src="{{ cdn($domain) }}/images/sign_icon.png" width="26" alt="✓" border="0" style="display:inline-block; max-width:26px;"></span>
            </a>
        </td>
      </tr>
    </tbody></table>
    </td>
           </tr>
         </tbody></table>
         </td>
        </tr>
      </tbody></table>
      </td>
      </tr>
    </tbody></table>
    </td>
    </tr>
  </tbody></table>
  </td>
  </tr>
@endsection
