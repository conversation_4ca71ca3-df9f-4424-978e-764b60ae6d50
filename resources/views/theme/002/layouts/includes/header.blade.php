@auth
    <div class="logged-header-full">
        <div class="position-relative bg-white w-100 p-3 d-block d-lg-none text-end clearfix">
            <a href="{{ route('home') }}" class="d-block float-start logo-header mt-2">
                <img src="{{ cdn($domain) }}/images/{{ strtolower($domain->name) }}.png" class="margin-right:auto;">
                <!-- mobile -->
            </a>
            <a class="mobile-user-thumb" href="{{ route('userProfile') }}">
                <div class="d-block position-relative">
                    <img src="{{ thumb($user->profile_image, $user->gender) }}" class="rounded-circle w-100">
                    @if ($user->isPremium())
                        <span class="badge vip-badge">
                            {{ __('Premium') }}
                        </span>
                    @endif
                </div>
            </a>
        </div>
    </div>
    <!-- top menu -->
    <div class="header-menu-bar sticky-top">
        <div class="container">
            <div class="row w-100 mx-auto">
                <ul class="col d-flex align-items-center justify-content-between">
                    <li class="logo">
                        <a class="col-auto" class="menu-home" href="{{ route('home') }}"> <!-- desktop -->
                            <img src="{{ cdn($domain) }}/images/{{ strtolower($domain->name) }}.png">
                        </a>
                    </li>
                    <li class="top-menu-item @if ($currentRoute == 'members' || $currentRoute == 'member') top-menu-item-selected @endif">
                        <a class="menu-home" href="{{ route('home') }}">
                            <div class="icons"><i class="fas fa-users"></i></div> <span>{{ __('Members') }}</span>
                        </a>
                    </li>
                    @if ($domain->hasModule('chat_groups'))
                        <li @if ($currentRoute == 'groups') class="mobile-menu-item-selected" @endif>
                            <a href="{{ route('groups') }}"><i class="fas fa-users-crown"></i> {{ __('Tribes') }}</a>
                        </li>
                    @endif

                    <li class="top-menu-item @if ($currentRoute == 'messages' || $currentRoute == 'chat') top-menu-item-selected @endif">
                        <a @if ($messageCount > 0) id="popover-first-message" data-bs-trigger="manual" data-bs-toggle="popover" data-bs-placement="bottom" data-bs-content="{{ __('You received a message! Click here to see who it is.') }}" @endif
                            href="{{ route('messages', ['type' => 'all']) }}">
                            <div class="icons"><i class="fas fa-comments"></i> </div> <span>{{ __('Messages') }}</span>
                            @if ($messageCount > 0)
                                <span
                                    class="badge bg-secondary badge-pink ms-1">{{ $messageCount > 99 ? '99+' : $messageCount }}</span>
                            @endif
                        </a>
                    </li>
                    <li class="top-menu-item @if ($currentRoute == 'likes') top-menu-item-selected @endif">
                        <a href="{{ route('likes') }}">
                            <div class="icons"><i class="fa fa-heart"></i></div><span>{{ __('My Likes') }}</span>
                        </a>
                    </li>

                    <li class="top-menu-item @if ($currentRoute == 'visitors') top-menu-item-selected @endif" >
                        <a href="{{ route('visitors') }}">
                            <div class="icons"><i class="fa fa-eye"></i></div><span>{{ __('My Visitors') }}</span>
                            @if ($visitorCount > 0)
                                <span class="badge bg-secondary badge-pink ms-1">{{ $visitorCount > 99 ? '99+' : $visitorCount }}</span>
                            @endif
                        </a>
                    </li>
                    @if ($domain->hasModule('premium'))
                        <li class="top-menu-item @if ($currentRoute == 'premium') top-menu-item-selected @endif">
                            <a href="{{ $user->isPremium() ? route('premiumOverview') : route('premium') }}"
                                class="d-block">
                                <div class="icons"><i class="fa fa-star"></i></div><span>{{ __('Premium') }}</span>
                            </a>
                        </li>
                    @endif
                    <li class="top-menu-item @if ($currentRoute == 'credits') top-menu-item-selected @endif">
                        <a href="{{ route('pay') }}">
                            <div class="icons"><i class="fas fa-coins"></i></div> <span>{{ __('Credits') }}</span>
                            @if ($user->active)
                                <span class="badge bg-secondary badge-pink ms-1 credit-amount">{{ $user->credits }}</span>
                            @endif
                        </a>
                    </li>
                    <li>
                        <div class="d-block position-relative">
                            <a class="user-profile"
                                @if ($user->active) id="popover-profile" data-bs-trigger="manual" data-bs-toggle="popover" data-bs-placement="bottom" data-bs-content="{{ __('When you finish your profile information you will have a better chance to find the right match.') }}" @endif
                                href="{{ route('userProfile') }}">
                                <div class="icons"><img src="{{ thumb($user->profile_image, $user->gender) }}"
                                        style="border-radius: 50%; width:60px;" alt="{{ $user->username }}"></div>
                            </a>
                            @if ($domain->hasModule('premium') && $user->isPremium())
                                <span class="badge vip-badge">
                                    {{ __('Premium') }}
                                </span>
                            @endif
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <!-- bottom menu-->
    <div class="mobile-menu d-block d-lg-none">
        <ul class="content">
            <li @if ($currentRoute == 'members' || $currentRoute == 'member') class="mobile-menu-item-selected" @endif>
                <a class="menu-home" href="{{ route('home') }}">
                    <i class="fas fa-users"></i>
                    <label>{{ __('Members') }}</label>
                </a>
            </li>
            @if ($domain->hasModule('chat_groups'))
                <li @if ($currentRoute == 'groups') class="mobile-menu-item-selected" @endif>
                    <a href="{{ route('groups') }}"><i class="fas fa-users-crown"></i>
                    </a>
                </li>
            @endif
            <li @if ($currentRoute == 'messages' || $currentRoute == 'chat') class="mobile-menu-item-selected" @endif>
                <a href="{{ route('messages', ['type' => 'all']) }}"><i class="fas fa-comments"></i>
                    @if ($messageCount > 0)
                        <span
                            class="badge bg-secondary badge-pink ms-1">{{ $messageCount > 99 ? '99+' : $messageCount }}</span>
                    @endif
                    <label>{{ __('Inbox') }}</label>
                </a>
            </li>
            <li @if ($currentRoute == 'likes') class="mobile-menu-item-selected" @endif>
                <a href="{{ route('likes') }}">
                    <i class="fa fa-heart"></i>
                    <label>{{ __('Likes') }}</label>
                </a>
            </li>
            <li @if ($currentRoute == 'visitors') class="mobile-menu-item-selected" @endif>
                <a href="{{ route('visitors') }}">
                    <i class="fa fa-eye"></i>
                    @if ($visitorCount > 0)
                        <span class="badge bg-secondary badge-pink ms-1">{{ $visitorCount > 99 ? '99+' : $visitorCount }}</span>
                    @endif
                    <label>{{ __('Visitors') }}</label>
                </a>
            </li>
            {{-- @if ($domain->hasModule('premium'))
                <li @if ($currentRoute == 'premium') class="mobile-menu-item-selected" @endif>
                    <a href="{{ $user->isPremium() ? route('premiumOverview') : route('premium') }}"><i class="fa fa-star"></i></a>
                </li>
            @endif --}}
            {{--
            <li @if ($currentRoute == 'credits') class="mobile-menu-item-selected" @endif>
                <a href="{{ route('pay') }}">
                    <i class="fas fa-coins"></i>
                    @if ($user->active)
                        <span class="badge bg-secondary badge-pink ms-1 credit-amount">{{ $user->credits }}</span>
                    @endif
                    <label>{{ __('Credits') }}</label>
                </a>
            </li>
            --}}
        </ul>
    </div>
@endauth

@guest
    <!-- header - not logged in -->
    <div class="homepage-header-full">
        <form class="homepage-header" action="#">
            <a href="{{ route('guestHome') }}"><img
                    src="{{ cdn($domain) }}/images/{{ strtolower($domain->name) }}.png"></a>
            <span data-bs-toggle="modal" data-bs-target="#login-modal" class="login-button">
                {{ __('Login') }} <i class="fas fa-hand-point-right"></i>
            </span>
            <div style="clear: both;"></div>
        </form>
    </div>
@endguest
