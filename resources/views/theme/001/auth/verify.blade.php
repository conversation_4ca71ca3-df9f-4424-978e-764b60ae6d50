@extends('theme.'. $domain->theme .'.layouts.master')
@section('content')
<div class="container-main container-complete">
    <img src="{{ cdn($domain) }}/images/verification-image.jpg" class="ms-auto me-auto d-block mb-3 verification-image">
	<div class="h3 text-center">
		{{ __('Last step...you\'re almost there') }}
	</div>
    <p class="mb-4 text-center">{{ __('Complete your registration and gain free access to') }} <img src="{{ cdn($domain) }}/images/{{ strtolower($domain->name) }}-dark.png" class="logo-small"/></p>
    <form id="verify-registration" class="recaptcha-form" method="POST" action="{{ route('verify') }}" class="px-2">
        @csrf
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="row mb-2">
                    <div class="col-md-4 text-start">{{ __('E-mail') }}</div>
                    <div class="col-md-8">
                        <input value="{{ old('email', $user->email) }}" name="email" id="email" class="form-control" type="email" required/>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md-4 text-start">{{ __('Your date of birth') }}</div>
                    <div class="col-md-8">
                        <div class="age-selector row m-0">
                            @include('theme.' . $domain->theme . '.partials.age-selector')
                        </div>
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-md-4 text-start">{{ __('City of residence') }}</div>
                    <div class="col-md-8">
                        <div class="input">
                            <input type="text" name="cityAutocomplete" value="" class="ui-autocomplete-input match_location_input m-0" id="cityAutocomplete" autocomplete="off">
                            <input type="hidden" name="city_id" value="" id="cityInput" data-field="city_id">
                            <div id="results-container" class="d-none">
                                <ul class="results"></ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 city-error mt-3 d-none">
                        <div class="alert alert-danger" role="alert">
                            {{ __('Please select a city from the drop down menu.') }}
                        </div>
                    </div>
                </div>
                <div class="row text-center justify-content-end mt-3">
                    <div class="col-md-8">
                        <button type="submit" class="btn btn-success rounded-pill w-100 px-4 py-2 fw-bold">{{ __('Verify Now!') }}</button>
                    </div>
                </div>
                <div class="row text-center mt-4">
                    <div class="col-6">
                        <small class="fw-bold mt-5 mb-2 d-block">{{ __('Voted as the best adult dating contact site in 2023') }}</small>
                        <img src="{{ cdn($domain) }}/images/stars.png" class="logo-stars"/>
                    </div>
                    <div class="col-6">
                        <small class="fw-bold mt-5 mb-2 d-block">{{ __('We proudly work with') }}</small>
                        <div class="row row-cols-3 row-cols-md-4 g-0 px-2 justify-content-center">
                            <div class="col"><img src="{{ asset('global/img/cc/visa.png') }}" alt="Visa"/></div>
                            <div class="col"><img src="{{ asset('global/img/cc/mastercard.png') }}" alt="Mastercard" /></div>
                            @if($domain->country->code == 'US')
                            <div class="col"><img src="{{ asset('global/img/cc/apple_pay.png') }}" alt="Apple Pay" /></div>
                            <div class="col"><img src="{{ asset('global/img/cc/google_pay.png') }}" alt="Google Pay" /></div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
    $('#verify-registration').on('submit', function(ev) {
        if ($('#cityInput').val() == '') {
            ev.preventDefault();
            $('.city-error').removeClass('d-none');
        }
    });
</script>
@endpush
