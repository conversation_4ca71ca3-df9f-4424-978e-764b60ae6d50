@extends('theme.'. $domain->theme .'.layouts.master')
@section('content')
<div class="container-main">
	<span class="homepage-create-account-popup-button">
		{{ __('CREATE AN ACCOUNT') }} <i class="fas fa-chevron-right"></i>
	</span>
    @include('theme.' . $domain->theme . '.login.login_without_google')
	<!-- welcome screen -->
	<div class="homepage-welcome-container">
        @include('theme.' . $domain->theme . '.partials.slideshow'.($domain->is_whitelabel ? '-whitelabel' : ''))
	</div>
	<div style="clear:both"></div>
</div>
@include('theme.' . $domain->theme . '.popup.age-verify')
@endsection
@push('scripts')
<script>
    $( document ).ready(function() {
        $(".slideshow > div:gt(0)").hide();
        setInterval(function() {
            $('.slideshow > div:first')
                .fadeOut(1000)
                .next()
                .fadeIn(1000)
                .end()
                .appendTo('.slideshow');
        }, 4000);
        @if($errors->hasBag('login'))
            $('#login-modal').modal('show');
        @enderror
        @if($errors->hasBag('register'))
            showRegistrationForm();
        @endif

        if(localStorage.getItem('ageVerification') != 'shown') {
            $("#popup-age-verify").modal('show');
        } else {
            @if($loginEmail)
                $('#login-modal').modal('show');
                $('#login-modal input[name="email"]').val('{{ $loginEmail }}')
            @endif
        }

        $('.agree').on("click", function(ev) {
            if($('#checkVerifyAge').is(':checked')) {
                ev.preventDefault();
                $("#popup-age-verify").modal('hide');
                localStorage.setItem('ageVerification','shown');
            }
            @if($loginEmail)
                $('#login-modal').modal('show');
                $('#login-modal input[name="email"]').val('{{ $loginEmail }}')
            @endif
        });
    });

    $(".homepage-form-button-1").on("click", function() {
        if(($("#region").val() == "none")||($("#username").val() == "")){
            toastr.error("{{ __('Please fill in all required fields.') }}");
            return;
        }
    });

    $(".button-continue").on("click", function(ev) {
        if ($('select[name="birth_day"]').val() == '' ||
        $('select[name="birth_month"]').val() == '' ||
        $('select[name="birth_year"]').val() == '' ||
        $('select[name="region_id"]').val() == '') {
            $(this).prop('type', '');
            $('.register-form').validate();
            return false;
        }
        $(this).prop('type', 'button');
        $('.homepage-step-1, .homepage-step-2').toggleClass('d-none');
    });

    $(".homepage-back-button").on("click", function() {
        $('.homepage-step-1, .homepage-step-2').toggleClass('d-none');
    });

    $(".homepage-create-account-popup-button").on('click', function() {
        showRegistrationForm();
    });

    $(".register-form").on('submit', function(ev) {
        var submitButton = $('button[type="submit"]').first();
        if (submitButton.hasClass('disabled')) {
            ev.preventDefault();
            return false;
        }

        var submitIcon = $('button[type="submit"] i').first();
        submitButton.addClass('disabled');
        submitIcon.removeClass('fas fa-chevron-right').addClass('fa fa-spinner fa-spin');
    });

    function showRegistrationForm() {
        $(".register-form").css("display", "block");
        $(".homepage-create-account-popup-button").css("position", "fixed");
        $(".homepage-create-account-popup-button").css("visibility", "hidden");
        $(".homepage-create-account-popup-button").css("z-index", "-100");
    }
</script>
@include('global.layouts.voluum.voluum')
@endpush
