<div id="magnific-popup"  class="white-popup">
    <div class="magnific-photo" data-url="{{ $photo_url }}">
        <img src="{{ fullimage($filename) }}" alt="">
    </div>
    @if($profile)
    <div class="magnific-comment mt-1">
        <form id="photo-message-form" method="POST" action="{{ route('addMessage', ['username' => $profile->username]) }}">
            @csrf
            <input type="hidden" name="photo" value="{{ $filename }}" />
            <input type="hidden" name="type" value="photo"/>
            <input type="hidden" name="go-to-chat" value="1" />
            <div class="row g-1">
                <div class="col">
                    <textarea spellcheck="false" name="message" class="form-control photo-message" placeholder="{!! __('Let :username know what you think of her photo', ['username' => $profile->username]) !!}" rows="1"></textarea>
                </div>
                <div class="col-auto">
                    <button type="submit" class="float-end send-photo-message d-flex justify-content-center align-items-center @if($user->credits == 0) no-credits @endif">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
    @if($comments)
    <div class="photo-comments mt-1">
        @foreach ($comments as $comment)
        <blockquote class="blockquote mb-0">
            <p class="mb-3">{{ $comment->content }}</p>
            <footer class="blockquote-footer"><small>{{ __('Sent') }} {{ humantime($comment->created_at) }}</small></footer>
        </blockquote>
        @endforeach
    </div>
    @endif
    @endif
</div>
