<!-- modal: disable notification if new member sends message -->
<div class="modal fade modal-basic sure-notatification-new-members-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
	<div class="modal-dialog modal-dialog-centered modal-lg">
		<form method="POST" action="#" class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">{{ __('Are you sure?') }}</h5>
				<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="title">
					{{ __('When you set this option to none you won\'t receive emails about new members sending you a message.') }}
				</div>
				<div class="text">
					{{ __('You can adjust the frequency so you will receive less emails, but you will not miss a new message.') }}
				</div>
			</div>
			<div class="modal-footer yes-no">
				<div>
					<button class="agree">
						{{ __('Change frequency') }}
					</button>
					<button class="decline">
						{{ __('Set to none') }}
					</button>
				</div>
			</div>
		</form>
	</div>
</div>