<!-- modal: last 10 visitors -->
@if($user->isPremium())
<div class="modal fade modal-basic last-10-visitors" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
	<div class="modal-dialog modal-dialog-centered modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">{{ __('Who visited my profile?') }}</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="title">
					{{ __('These are the last 10 people who viewed your profile.') }}
				</div>
                <span id="visitors-loading" class="fas fa-spinner fa-spin"></span>
				<div id="visitor-output" class="visitor-overview" style="display: none;"></div>
			</div>
		</div>
	</div>
</div>
@push('scripts')
<script>
$( document ).ready(function() {
	$('.view-visitors, #show-visitors').on('click', function (e) {
		e.preventDefault();
        $.ajax({
            url: "/visitor",
            data: {
                limit: 10
            },
            success: function(data) {
                $('#visitor-output').html(data);
                $('#visitors-loading').remove();
                $('#visitor-output').fadeIn();
            }
        });
		$('.last-10-visitors').modal('show');
	});
    $(document).on('click', '.last-10-visitors .favorite', function(ev){
        ev.preventDefault();
        var elem = $(this);
        $(elem).addClass('cursor-wait');
        var url = $(elem).hasClass('is-favorite') ? $(elem).data('remove') : $(elem).data('add');

        $.get( url, function( data ) {
            if(data.result) {
                $(elem).toggleClass('add-favorite is-favorite');
            }
            $(elem).removeClass('cursor-wait');
        });
    });
});
</script>
@endpush
@endif
