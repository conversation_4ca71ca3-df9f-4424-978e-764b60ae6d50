<!-- Modal no vip -->
<div class="modal fade modal-basic no-vip-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Become a VIP') }}</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="title">
                    {!! array(
                        __('To send :profile a message you must become a VIP member.', ['profile' => $profile ? $profile->username : 'this lovely lady']),
                        __('Chatting is only possible if you become a VIP member.'),
                        __('You are not a VIP (yet) to send a message.'),
                    )[rand(0,2)] !!}
                </div>
                @if($subscription = $user->getSubscriptionProduct())
                <div class="text">
                    {!! array(
                        __('Sign up now for only :price!', ['price' => currency($domain, $subscription->toPay())]),
                        __('Sign up now for only :price and start your naughty adventure!', ['price' => currency($domain, $subscription->toPay())]),
                        __('Please upgrade for :price.', ['price' => currency($domain, $subscription->toPay())]),
                    )[rand(0,2)] !!}
                </div>
                @endif
            </div>
            <div class="modal-footer yes-no">
                <div>
                    <a href="{{ route('premium') }}" class="agree modal-click">
                        {{ __('Become a VIP') }}
                    </a>
                    <span data-bs-dismiss="modal" class="decline">
                        {{ __('Not now') }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>
