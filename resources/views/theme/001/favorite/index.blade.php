@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <div class="container-main-space-for-menu container-members">
        <div class="title">{{ __('My Favorites') }}</div>
        <div class="row row-cols-2 row-cols-md-3 row-cols-lg-5 g-2 g-md-3">
            @foreach ($favorites as $profile)
            <div class="col">
                <div class="card member-card">
                    <div class="card-img-top">
                        <a href="{{ route('profile', ['username' => $profile->username]) }}">
                            <img class="w-100" src="{{ thumb($profile->profile_image, $profile->gender) }}" alt="">
                            <div data-add="{{ route('addFavorite', ['username' => $profile->username]) }}" data-remove="{{ route('removeFavorite', $profile->username) }}" class="d-flex justify-content-center align-items-center favorite @if($profile->isFavorite()) is-favorite @else add-favorite @endif">
                                <i class="far fa-heart add"></i>
                                <i class="fas fa-heart liked"></i>
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="row g-1">
                            <div class="col text-truncate">
                                <a href="{{ route('profile', ['username' => $profile->username]) }}">
                                    <h5 class="card-title text-truncate"> {{ $profile->username }} <i class="fas fa-circle ms-1 @if(in_array($profile->id, $online, true)) text-success @endif"></i></h5>
                                    <p class="card-text"><span>{{ $profile->info->age }}</span> {{ __('years old') }}</p>
                                </a>
                            </div>
                            <div class="col-auto">
                                <a class="btn d-flex justify-content-center align-items-center" href="{{ route('conversation', ['type' => 'conversation', 'username' => $profile->username]) }}">
                                    <div><i class="fas fa-comment-dots"></i></div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
            @if($favorites->count() == 0)
            <div class="no-members-found">
                {{ __('You don\'t have any favorite members yet.') }}<br />
            </div>
            @endif
            {{ $favorites->links('theme.' . $domain->theme . '.pagination') }}
        </div>
    </div>
@endsection
