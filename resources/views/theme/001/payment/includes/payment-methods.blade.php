<div class="row payment-methods">
    @foreach ($paymentMethods as $method)
        <div class="col-md-12">
            <input class="payment-method-radio {{ $method->provider->name }} d-none"
                data-amount_min="{{ $method->amount_min }}"
                data-amount_max="{{ $method->amount_max }}"
                data-payment_type="{{ $method->type->name }}"
                data-payment_provider="{{ $method->provider->name }}"
                type="radio" id="payment-method-{{ $method->id }}"
                name="payment_method_id"
                value="{{ $method->id }}"
                @if ($method->default) checked @endif>
            <label for="payment-method-{{ $method->id }}"
                class="w-100 payment-method-label {{ $method->type->name }} {{ $method->type->name === 'apple_pay' || $method->type->name === 'google_pay' ? 'd-none' : '' }}">
                <div class="row g-0">
                    <div class="col payment-name">
                        {{ __($method->type->title) }}
                    </div>
                    <div class="col-auto payment-logo text-end">
                        <img src="{{ asset('/global/img/cc/' . $method->type->icon . '.png') }}">
                    </div>
                    <div class="col-auto product-select text-end ms-3">
                        <i class="far fa-circle"></i>
                        <i class="far fa-dot-circle"></i>
                    </div>
                </div>
            </label>
        </div>
    @endforeach
</div>

@push('scripts')
    <script>
        $(document).on("click", ".show-next", function(e) {
            e.stopPropagation();
            $(".payment-section").removeClass("d-none");
            $(".show-next").addClass('d-none');
            $(".product-label").each(function() {
                if ($(this).parent().find('input[type="radio"]').prop("checked") == false) {
                    $(this).addClass('d-none');
                }
            });
        });

        $(document).on("click", ".show-previous", function(e) {
            e.stopPropagation();
            $(".product-label, .show-next").removeClass("d-none");
            $(".payment-section").addClass("d-none");
        });
    </script>
    @include('global.payment.scripts.payment-scripts')
    @foreach ($paymentMethods as $method)
        @if (View::exists('global.payment.scripts.' . $method->type->name))
            @include('global.payment.scripts.' . $method->type->name)
        @endif
    @endforeach
@endpush
