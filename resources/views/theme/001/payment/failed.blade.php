@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <div class="container-main-space-for-menu container-payment-message">
        <div class="text-1">
            {!! __(
                'Oops! It looks like your payment didn\'t go through. <br>Please check your payment details and try again. If you need assistance, contact our support team—we\'re happy to help!',
            ) !!}
        </div>
        <div class="text-2">
            {{ __('How can we help?') }}
        </div>
        <div class="p-3">
            <div class="buttons failed row justify-content-md-center">
                @if (isset($order) && $order !== null && $paymentMethod !== null)
                    <div class="col-md-4">
                        @if ($paymentMethod->type->name === 'credit_card_hosted')
                            <button type="button" class="retry"
                                onclick="window.location.href='{{ route($order->isForDefaultProduct() ? 'creditsOffer' : 'premiumOffer', ['offer_id' => $order->product_id]) }}'">
                                @if ($domain->country->code == 'US')
                                    <span>{{ __('Retry payment with :payment_method', ['payment_method' => $paymentMethod->type->title]) }}</span>
                                @else
                                    <span>{{ __('Retry payment') }}</span>
                                @endif
                            </button>
                        @else
                            <form action="{{ $order->getRetryRoute() }}" method="post" id="transaction">
                                @csrf
                                <input type="hidden" name="payment_method_id" value="{{ $paymentMethod->id }}"
                                    id="{{ $paymentMethod->id }}" data-payment_type="{{ $paymentMethod->type->name }}"
                                    data-payment_provider="{{ $paymentMethod->provider->name }}" />
                                <input type="hidden" name="product_id" value="{{ $order->product_id }}" />
                                <button type="button" class="retry buy-button">
                                    @if ($domain->country->code == 'US')
                                        <span>{{ __('Retry payment with :payment_method', ['payment_method' => $paymentMethod->type->title]) }}</span>
                                    @else
                                        <span>{{ __('Retry payment') }}</span>
                                    @endif
                                </button>
                            </form>
                        @endif
                    </div>
                    <div class="col-md-4">
                        <a href="{{ $order->getProductPageRoute() }}">
                            <span>{{ __('Pick another package') }}</span>
                        </a>
                    </div>
                @endif
                <div class="col-md-4">
                    <a href="{{ route('cmsContact') }}">
                        <span>{{ __('Chat with customer service') }}</span>
                    </a>
                </div>
                <span style="display:block; position:relative; clear:both;"></span>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    @include('global.payment.scripts.payment-scripts')
    @if (isset($order) && $order !== null && $paymentMethod !== null)
        @if (View::exists('global.payment.scripts.' . $paymentMethod->type->name))
            @include('global.payment.scripts.' . $paymentMethod->type->name)
        @endif
    @endif
@endpush
