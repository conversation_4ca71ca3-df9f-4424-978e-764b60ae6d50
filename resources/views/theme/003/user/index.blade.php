@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <div class="container mb-4">
        @if (session()->has('activated'))
            <div class="row">
                <div class="col">
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session()->get('activated') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                </div>
            </div>
        @endif
        <div class="row">
            <div class="col sidebar d-none d-lg-block">
                @include('theme.' . $domain->theme . '.layouts.includes.side-menu')
            </div>
            <div class="col">
                <div class="favorite-sendmess">
                    <div class="row">
                        @include('theme.' . $domain->theme . '.banners.native-profile')
                        @foreach ($profiles as $profile)
                            <div
                                class="col-6 col-sm-4 col-xl-3 profile-card @if ($profile->isFavorite()) favorite-profile @endif">
                                <div class="image-text">
                                    <div class="image">
                                        <a href="{{ route('profile', ['username' => $profile->username]) }}">
                                            <img src="{{ thumb($profile->profile_image, $profile->gender) }}"
                                                alt="{{ $profile->username }}" class="img-fluid">
                                            <div class="tag">
                                                @if ($profile->isOnline())
                                                    <span>{{ __('online') }}</span>
                                                @endif
                                                <span class="favorite-heart ml-1"><i class="fas fa-heart"></i></span>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="text">
                                        <div class="name">
                                            {{ $profile->username }}
                                        </div>
                                        <div class="year">
                                            {{ __(':age years old', ['age' => $profile->info->age]) }}
                                        </div>
                                        <div class="button">
                                            @if ($domain->hasModule('favorites_to_likes'))
                                                <a href="#" data-add="{{ route('addLike', $profile->username) }}"
                                                    data-remove="{{ route('removeLike', $profile->username) }}"
                                                    class="btn btn-primary favorite @if ($profile->isFavorite()) is-favorite @else add-favorite @endif">
                                                    <span class="text-is-favorite">{{ __('Remove like') }}</span>
                                                    <span class="text-add-favorite">{{ __('Like profile') }}</span>
                                                </a>
                                            @else
                                                <a href="#" data-add="{{ route('addFavorite', $profile->username) }}"
                                                    data-remove="{{ route('removeFavorite', $profile->username) }}"
                                                    class="btn btn-primary favorite @if ($profile->isFavorite()) is-favorite @else add-favorite @endif">
                                                    <span class="text-is-favorite">{{ __('Remove favorite') }}</span>
                                                    <span class="text-add-favorite">{{ __('Add to favorites') }}</span>
                                                </a>
                                            @endif
                                            <a href="{{ route('conversation', ['type' => 'all', 'username' => $profile->username]) }}"
                                                class="btn btn-action">{{ __('Go to Chat') }}</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @php($midpoint = intval($domain->items_per_page / 2))
                            @php($midpoint = $midpoint - ($enable_banners ? 1 : 0))
                            @if ($loop->index + 1 == $midpoint && $profiles->count() > $midpoint)
                                @include('global.banners.middle')
                            @endif
                        @endforeach
                        @if (!$profiles->count())
                            <div class="col-12 page-content text-center">
                                {!! __('No members match the current criteria') !!}
                                <div>
                                    <a class="btn btn-secondary mt-3" href="{{ route('home') }}">
                                        <span>{{ __('Go back') }}</span>

                                    </a>
                                </div>
                            </div>
                        @endif
                    </div>
                    {{ $profiles->appends($data)->links('theme.' . $domain->theme . '.pagination') }}
                </div>
            </div>
        </div>
    </div>
    <!-- content end -->
@endsection
