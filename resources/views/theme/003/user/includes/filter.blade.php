<div class="filters mb-3">
<div class="container">

        <div class="accordion accordion-flush" id="accordionFlushExample">
            <div class="accordion-item">
                <h2 class="accordion-header d-lg-none" id="flush-headingOne">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                        {{ __('Member filter') }}
                    </button>
                </h2>
                <div id="flush-collapseOne" class="accordion-collapse collapse d-lg-block" aria-labelledby="flush-headingOne" data-bs-parent="#accordionFlushExample">
                    <div class="accordion-body p-0">
                        @include('global.forms.filter', ['theme' => '003', 'useAgeSlider' => true, 'inlineLabel' => false, 'useInputGroup' => false])
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
@push('scripts')
<script>
    $( document ).ready(function() {
        $('#select-region').one('select2:open', function(e) {
            $('input.select2-search__field').prop('placeholder', "{{ __('Search state') }}");
        });
    });
</script>
@endpush
