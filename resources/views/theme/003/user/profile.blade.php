@php use Illuminate\Support\Carbon; @endphp

@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <!-- content start -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col sidebar d-none d-lg-block">
                    @include('theme.' . $domain->theme . '.layouts.includes.side-menu')
                </div>
                <div class="col">
                    <div class="row mb-4 g-4">
                        <div class="col-lg-7">
                            <div class="profile-section">
                                <div class="row">
                                    <div class="col-4">
                                        <div class="pro-pic">
                                            <a href="{{ route('photo') }}?name={{ $user->profile_image }}"
                                                class="profile-image @if ($user->profile_image) magnific-ajax @else pe-none @endif">
                                                <img src="{{ thumb($user->profile_image, $user->gender) }}"
                                                    alt="iconictouches">
                                            </a>
                                        </div>
                                    </div>
                                    <div class="col-8">
                                        <div class="location-date">
                                            <div>
                                                <div class="locati">
                                                    <i class="fa-solid fa-location-dot"></i>
                                                    {{ $user->regionName() }}
                                                </div>
                                                <div class="date">
                                                    <i class="fa-solid fa-calendar"></i> {{ $info['age'] }}
                                                </div>
                                            </div>
                                        </div>
                                        <h3>
                                            {{ auth()->user()->username }}
                                        </h3>
                                        <a class="btn btn-primary mt-2"
                                            href="{{ route('editProfile') }}">{{ __('Edit profile') }}</a>
                                    </div>
                                </div>
                                @if ($domain->with_about_me)
                                    <div class="col-12 mt-2">
                                        <div id="profile-about" class="col-12 mt-1">
                                            <div class="about">
                                                {!! nl2br($user->info->about) !!}
                                            </div>
                                        </div>
                                        <a href="#" class="show-more-button d-none">{{ __('Show more') }}</a>
                                        <a href="#" class="show-less-button d-none">{{ __('Show less') }}</a>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="col-lg-5">
                            <div class="profile-section">
                                <div class="row no-gutters">
                                    <div class="col-12">
                                        <h3>{{ __('My photos') }}</h3>
                                    </div>
                                    @if ($uploads->count() > 0)
                                        <div class="col-12">
                                            <div class="row g-1 justify-content-center">
                                                @foreach ($uploads as $upload)
                                                    <div class="col-4 private-photo">
                                                        <a class="magnific-ajax"
                                                            href="{{ route('photo') }}?name={{ $upload->file }}"
                                                            data-title="">
                                                            <img src="{{ thumb($upload->file, $user->gender) }}"
                                                                alt="image {{ $upload->file }}" class="img-fluid rounded">
                                                        </a>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @else
                                        <small class="m-2 text-center">{{ __('No extra photos available') }}</small>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="profile-section p-4">
                            <div class="row">
                                <div class="col-md-6 mb-4 mb-md-0">
                                    <h3>{{ __('Characteristics') }}</h3>
                                    @if ($user->info->marital_status)
                                        <div class="bullet">
                                            <label>{{ __('Relationship status') }}</label><span>{{ trans('lists.marital_status.' . $user->info->marital_status) }}</span>
                                        </div>
                                    @endif
                                    @if ($user->info->body_type)
                                        <div class="bullet">
                                            <label>{{ __('Body type') }}</label><span>{{ trans('lists.body_types.' . $user->info->body_type) }}</span>
                                        </div>
                                    @endif
                                    @if ($user->info->length)
                                        <div class="bullet">
                                            <label>{{ __('Height') }} </label> <span>
                                                @if ($user->info->length)
                                                    {{ format_height($user->info->length) }}
                                                @endif
                                            </span>
                                        </div>
                                    @endif
                                    @if ($user->info->hair_color)
                                        <div class="bullet">
                                            <label>{{ __('Hair color') }}
                                            </label><span>{{ trans('lists.hair_colors.' . $user->info->hair_color) }}</span></span>
                                        </div>
                                    @endif
                                    @if ($user->info->eye_color)
                                        <div class="bullet">
                                            <label>{{ __('Eye color') }}
                                            </label><span>{{ trans('lists.eye_colors.' . $user->info->eye_color) }}</span>
                                        </div>
                                    @endif
                                    @if (!$user->info->smoking === null)
                                        <div class="bullet">
                                            <label>{{ __('Smoker') }}
                                            </label><span>{{ $profile->info->smoking ? __('Yes') : __('No') }}</span>
                                        </div>
                                    @endif
                                    @if (!$user->info->drink === null)
                                        <div class="bullet">
                                            <label>{{ __('Drinks') }}
                                            </label><span>{{ $user->info->drink ? __('Yes') : __('No') }}</span>
                                        </div>
                                    @endif
                                    @if (!$user->info->pets === null)
                                        <div class="bullet">
                                            <label>{{ __('Pets') }}
                                            </label><span>{{ $user->info->pets ? __('Yes') : __('No') }}</span>
                                        </div>
                                    @endif
                                    @if (!$user->info->diet === null)
                                        <div class="bullet">
                                            <label>{{ __('Diet') }} </label><span>{{ $user->info->diet }}</span>
                                        </div>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <h3>{{ __('Interests') }}</h3>
                                    @foreach ($user->info->activePreferences() as $key => $preference)
                                        @if (in_array($key, array_keys($preferences)))
                                        <div class="bullet">
                                                @if ($userHasAffiliate)
                                                <div class="preference-icon">
                                                    <img src="{{ cdn($domain) }}/images/{{ $key }}.png"
                                                        alt="{{ $key }}" class="img-fluid">
                                                </div>
                                                @endif
                                                {{ $preferences[$key] }}
                                            </div>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
