@if (in_array($domain->stylesheet, ['003b', '003e']))
    <div class="background-full polaroid">
        <div class="row row-cols-3 row-cols-md-4 row-cols-lg-5 row-cols-xl-6">
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
            <div class="col">
                <div class="bg-card">
                    <img src="{{ cdn($domain) }}/images/home/<USER>">
                </div>
            </div>
        </div>
        <div class="cover"></div>
    </div>
@endif
@push('scripts')
    <script>
        $(document).ready(function() {
            function get_random(list) {
                return list[Math.floor((Math.random() * list.length))];
            }

            function get_zindex() {
                return Math.floor((Math.random() * 100));
            }

            function get_degrees() {
                return Math.floor((Math.random() * 90)) - 45;
            }

            function show_slides() {
                var elements = $('.polaroid .bg-card img').not('.shown').toArray();
                if (elements.length == 0) {
                    return;
                }
                var element = get_random(elements);
                var degrees = get_degrees();

                $(element).css({ WebkitTransform: 'rotate(' + degrees + 'deg)'});
                $(element).css({ '-moz-transform': 'rotate(' + degrees + 'deg)'});
                $(element).parent().css('z-index', get_zindex());

                $(element).addClass('shown').fadeTo(200, 1, function(elem) {
                    show_slides();
                });
            }

            show_slides();
        });
    </script>
@endpush
