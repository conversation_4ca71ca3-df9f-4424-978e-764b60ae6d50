@if (
    (string) Config::get('wonderpush.web_key') !== '' &&
    $domain->wonderpush !== null &&
    Auth::check() &&
    Auth::user()->status &&
    Auth::user()->active &&
    Auth::user()->confirmed &&
    Auth::user()->privacyconfirmed &&
    Auth::user()->info->city_id !== null &&
    (
        Auth::user()->welcome_credit_popup ||
        (!Auth::user()->welcome_credit_popup && Auth::user()->click_request_id === null)
    )
)
    <script>
        window.WonderPush = window.WonderPush || [];
        WonderPush.ready = WonderPush.push;

        let wonderPushConfig = {
            webKey: "{{ Config::get('wonderpush.web_key') }}",
            userId: "{{ Auth::user()->id }}",
            customDomain: "{{ $domain->fullUrl }}",
            applicationName: "{{ $domain->name }}"
        };

        wonderPushConfig.subscriptionDialog = {
            triggers: {},
            title: "{{ $domain->wonderpush->title }}",
            message: "{{ $domain->wonderpush->message }}",
            positiveButton: "{{ $domain->wonderpush->positive_button }}",
            negativeButton: "{{ $domain->wonderpush->negative_button }}"
        };

        @if($domain->wonderpush->icon !== null)
            wonderPushConfig.notificationIcon = "{{ $domain->wonderpush->icon  }}";
        @endif

        WonderPush.push(["init", wonderPushConfig]);

        WonderPush.ready(function(WonderPushSDK) {
            if (
                WonderPushSDK.Notification.getSubscriptionState() === WonderPushSDK.SubscriptionState.SUBSCRIBED
                || WonderPushSDK.Notification.getSubscriptionState() === WonderPushSDK.SubscriptionState.UNSUPPORTED
            ) {
                return;
            }

            var listener = function(event) {
                if (event.detail.name !== 'subscription') return;
                if (event.detail.state === WonderPushSDK.SubscriptionState.SUBSCRIBED) {
                    window.removeEventListener('WonderPushEvent', listener);

                    $.ajax({
                        url: "{{ route('wonderpushActivate') }}",
                        type: "put"
                    });
                }
            };

            window.addEventListener('WonderPushEvent', listener);
        });
    </script>
@endif
