@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col sidebar d-none d-lg-block">
                    @include('theme.' . $domain->theme . '.layouts.includes.side-menu')
                </div>
                <div class="col">
                    <div class="comment-detail">
                        <div class="row mb-3 profile-information">
                            <div class="col-auto me-3">
                                @if ($post->is_from_profile)
                                    <a href="{{ route('profile', $post->author()->username) }}">
                                        <img src="{{ thumb($post->author()->profile_image, $post->author()->gender) }}"
                                            alt="{{ $post->author()->username }}">
                                    </a>
                                @else
                                    <img src="{{ thumb($post->author()->profile_image, $post->author()->gender) }}"
                                        alt="{{ $post->author()->username }}">
                                @endif
                            </div>
                            <div class="col location-date">
                                <div class="row g-0">
                                    <div class="col-sm">
                                        <div class="locati mt-1">
                                            <i class="fa-solid fa-location-dot"></i> {{ $post->author()->regionName() }}
                                        </div>
                                        <div class="date mt-1">
                                            <i class="fa-solid fa-calendar"></i> {{ $post->author()->info->age }}
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <h3 class="mb-2">
                                            @if ($post->is_from_profile)
                                                <a href="{{ route('profile', $post->author()->username) }}">
                                                    {{ $post->author()->username }}
                                                </a>
                                            @else
                                                <a href="{{ route('userProfile') }}">
                                                    {{ $post->author()->username }}
                                                </a>
                                            @endif
                                        </h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="name-view">
                            <div class="head text-start">
                                <h1 class="mt-4">
                                    {{ $post->title }}
                                </h1>
                                <h6>
                                    <a
                                        href="{{ route('groupPosts', ['section' => 'contributions', 'id' => $post->chatGroup->id]) }}">
                                        {{ __('Posted in:') }} {{ $post->chatGroup->title }}
                                    </a>
                                </h6>
                            </div>
                        </div>
                        <div class="text">
                            <div class="row">
                                <div class="col order-md-1">
                                    <p>
                                        {!! $post->message !!}
                                    </p>
                                    <p class="contribution-time">
                                        <i class="fa fa-clock"></i> {{ humantime($post->created_at) }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="row justify-content-end mt-2">
                            @if ($post->profile_id && $post->author()->gender == $user->seek)
                                <div class="col">
                                    <a href="{{ route('conversation', ['type' => 'all', $post->author()->username, 'post' => $post->id]) }}"
                                        class="btn btn-action @if (!$user->hasEnoughCredits(1)) no-credits @endif">
                                        {{ __('Send Private Message') }}
                                    </a>
                                </div>
                            @endif
                            @can('delete', $post)
                                <div class="col">
                                    <button class="btn btn-secondary delete-post-link">{{ __('Delete post') }}</button>
                                </div>
                            @endcan
                            <div class="col-auto">
                                <div class="btn btn-secondary btn-like d-inline-block @if ($post->is_from_current_user) disabled @endif @if ($post->userLikesPost()) unlike-post @else like-post @endif"
                                    data-likeurl="{{ route('likePost', $post) }}"
                                    data-unlikeurl="{{ route('unlikePost', $post) }}">
                                    <i class="fas fa-heart"></i> <span
                                        class="like-counter">{{ $post->totalLikes() }}</span>
                                </div>
                                @if($post->totalLikes() > 0)
                                <div data-post="{{ $post->id }}" class="likes btn btn-secondary d-inline-block">
                                    {{ __('View likes') }}
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('theme.' . $domain->theme . '.popup.delete-post')
    @include('theme.' . $domain->theme . '.popup.list-likes')
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {
            $(".delete-post-link").click(function(ev) {
                ev.preventDefault();
                $('.delete-post-modal').modal('show');
            });
        });
    </script>
@endpush
