@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <div class="content">
        <div class="container mb-4">
            <div class="setting">
                <div class="row justify-content-center">
                    <div class="col-lg-7">
                        <div class="all-password">
                            <form action="{{ route('postResetPassword') }}" method="POST">
                                @csrf
                                <div class="title">
                                    <h4>{{ __('Reset your password') }}</h4>
                                </div>
                                <div class="row form-group">
                                    <div class="col-sm-4 col-md-5 col-lg-3 col-xl-4">
                                        <label for="email" class="form-label">{{ __('Current e-mail') }}</label>
                                    </div>
                                    <div class="col-sm-8 col-md-7 col-lg-9 col-xl-8">
                                        <input class="form-control" name="email" type="text" value="{{ old('email') }}" required>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-sm-4 col-md-5 col-lg-3 col-xl-4">
                                        <label for="password" class="form-label">{{ __('New password') }}</label>
                                    </div>
                                    <div class="col-sm-8 col-md-7 col-lg-9 col-xl-8">
                                        <input class="form-control" name="password" type="password" required>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-sm-4 col-md-5 col-lg-3 col-xl-4">
                                        <label for="password_confirmation" class="form-label">{{ __('Confirm password') }}</label>
                                    </div>
                                    <div class="col-sm-8 col-md-7 col-lg-9 col-xl-8">
                                        <input class="form-control" name="password_confirmation" type="password" required>
                                    </div>
                                </div>
                                <div class="text-center mt-4">
                                    <input type="hidden" name="token" value="{{ $token }}">
                                    <button type="submit" class="btn btn-primary">{{ __('Reset password') }}</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
