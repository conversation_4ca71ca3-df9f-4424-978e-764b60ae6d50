@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <!-- content start -->
    <div class="container mb-4">
        <div class="row">
            <div class="col sidebar d-none d-lg-block">
                @include('theme.' . $domain->theme . '.layouts.includes.side-menu')
            </div>
            <div class="col">
                <div class="favorite-sendmess">
                    <div class="row">
                        @foreach ($favorites as $profile)
                            <div class="col-6 col-sm-4 col-xl-3">
                                <div class="image-text">
                                    <div class="image">
                                        <a href="{{ route('profile', ['username' => $profile->username]) }}">
                                            <img src="{{ thumb($profile->profile_image, $profile->gender) }}"
                                                alt="{{ $profile->username }}" class="img-fluid">
                                            @if ($profile->isOnline())
                                                <div class="tag">
                                                    <span>{{ __('online') }}</span>
                                                </div>
                                            @endif
                                        </a>
                                    </div>
                                    <div class="text">
                                        <div class="name">
                                            {{ $profile->username }}
                                        </div>
                                        <div class="year">
                                            {{ __(':age years old', ['age' => $profile->info->age]) }}
                                        </div>
                                        <div class="button">
                                            @if ($domain->hasModule('favorites_to_likes'))
                                                <a href="#" data-add="{{ route('addLike', $profile->username) }}"
                                                    data-remove="{{ route('removeLike', $profile->username) }}"
                                                    class="btn btn-primary favorite @if ($profile->isFavorite()) is-favorite @else add-favorite @endif">
                                                    <span class="text-is-favorite">{{ __('Remove like') }}</span>
                                                    <span class="text-add-favorite">{{ __('Like profile') }}</span>
                                                </a>
                                            @else
                                                <a href="#" data-add="{{ route('addFavorite', $profile->username) }}"
                                                    data-remove="{{ route('removeFavorite', $profile->username) }}"
                                                    class="btn btn-primary favorite @if ($profile->isFavorite()) is-favorite @else add-favorite @endif">
                                                    <span class="text-is-favorite">{{ __('Remove favorite') }}</span>
                                                    <span class="text-add-favorite">{{ __('Add to favorites') }}</span>
                                                </a>
                                            @endif
                                            <a href="{{ route('conversation', ['type' => 'all', 'username' => $profile->username]) }}"
                                                class="btn btn-action">{{ __('Go to Chat') }}</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                        @if ($favorites->isEmpty())
                            <div class="col-12">
                                <div class="page-content text-center">
                                    @if ($domain->hasModule('favorites_to_likes'))
                                        {!! __('You haven\'t liked any profiles yet') !!}
                                    @else
                                        {{ __('You haven\'t saved any profiles to favorites yet.') }}
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                    {{ $favorites->appends($data ?? '')->links('theme.' . $domain->theme . '.pagination') }}
                </div>
            </div>
        </div>
    </div>
    <!-- content end -->
@endsection
