@if ($paginator->hasPages())
    <ul class="pagination">
        <li class="page-item previ"><a class="page-link world" href="{{ $paginator->onFirstPage() ? 'javascript:void(0);' : $paginator->previousPageUrl() }}"><i class="fa-solid fa-angles-left"></i>{{ __('Previous') }}</a></li>

        @foreach ($elements as $element)
            @if (is_string($element))
                <li class="page-item disabled">{{ $element }}</li>
            @endif
            @if (is_array($element))
                @foreach ($element as $page => $url)
                    @if($page < $paginator->currentPage() - 2 || $page > $paginator->currentPage() + 2) @continue @endif
                    @if ($page == $paginator->currentPage())
                        <li class="page-item">
                            <a class="page-link active">{{ $page }}</a>
                        </li>
                    @else
                        <li class="page-item">
                            <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                        </li>
                    @endif
                @endforeach
            @endif
        @endforeach

        <li class="page-item next"><a class="page-link world" href="{{ $paginator->hasMorePages() ? $paginator->nextPageUrl() : 'javascript:void(0);' }}">{{ __('Next') }}<i class="fa-solid fa-angles-right"></i></a></li>
    </ul>
@endif
