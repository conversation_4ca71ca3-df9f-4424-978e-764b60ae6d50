<!-- modal: one free credit -->
<div class="modal fade free-credit-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <form method="POST" action="{{ route('welcomeCredit') }}" class="modal-content">
            @csrf
            <div class="modal-header">
                <h5 class="modal-title">{{ __('We love to see you here!') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-3">
                <div class="text-center mb-2"><span class="highlight">
                    {{ $domain->welcome_credits }} {{ $domain->welcome_credits > 1 ? __('free tokens') : __('free token') }}
                </span></div>
				{{ __('Because we are happy to welcome you to our website, we have given you 1 token as a gift.') }}
				{{ __('With this you can send a message or a flirt to one of the lovely members.') }}
			</div>
            <div class="modal-footer justify-content-center">
				<button class="btn btn-primary" data-dismiss="modal">
					{{ __('Great, let\'s start!') }}
				</button>
            </div>
        </form>
    </div>
</div>
@push('scripts')
 <script>
    $( document ).ready(function() {
        if($('.page-preferences').length == 0 && $('#popup-privacy-verify').length == 0) {
            if($('.add-city-modal').length > 0) {
                $('.add-city-modal').on('hidden.bs.modal', function(){
                    $('.free-credit-modal').modal('show');
                });
            } else {
                $('.free-credit-modal').modal('show');
            }

            $('.free-credit-modal').on('hide.bs.modal', function (e) {
                $.post("{{ route('welcomeCredit') }}");
            });
        }
    });
 </script>
@endpush
