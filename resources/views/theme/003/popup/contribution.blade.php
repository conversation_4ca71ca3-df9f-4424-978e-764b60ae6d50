<!-- modal: contribution -->
<div class="modal fade test" id="newPost" data-bs-keyboard="false" data-bs-backdrop="static" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <form method="post" id="message-form"
        enctype="multipart/form-data"
        action="{{ route("saveGroupPost", $chatGroup) }}" class="modal-content p-2">
            @csrf
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Add contribution') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-2">
                <div class="row w-100 g-1 mb-2">
                    <div class="col-auto flex-grow-1">
                        <textarea name="message" spellcheck="false" id="message" cols="" rows="6" placeholder="{{ __('Write story') }}" class="form-control mb-2" required></textarea>
                    </div>
                </div>
                <div class="row w-100 g-1 mb-2">
                    <div class="col-12 mt-2">
                        <div class="row w-100 g-1">
                            <div class="col-auto text-start">
                                <small class="text-muted">{{ __('A contribution requires 3 tokens.') }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col">{!! __('Before adding a contribution please <a href="#" data-bs-toggle="modal" data-bs-target="#contribution-rules">read our rules</a>') !!}</div>
                </div>
            </div>
            <div class="modal-footer justify-content-center p-1">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                    {{ __('Close') }}
                </button>
                <button type="submit" class="btn btn-action send-group-post">
                    {{ __('Add contribution') }}
                </button>
            </div>
        </form>
    </div>
</div>
