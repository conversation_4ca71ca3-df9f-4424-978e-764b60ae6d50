<!-- Special Offer -->
<div id="store" class="modal fade store-modal" data-bs-backdrop="static" tabindex="-1" data-bs-keyboard="false"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body p-0">
                <form id="transaction" method="POST" action="{{ route('postTransaction') }}" accept-charset="UTF-8">
                    @csrf
                    @if (session()->get('order_id') > 0) <input type="hidden" name="order_id" value="{{ session()->get('order_id') }}"> @endif
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    <div class="head">
                        <h4>{{ __('Wallet') }} <span class="badge badge-primary credit-amount ms-2">{{ $user->credits }}</span></h4>
                    </div>
                    @if (session()->get('offer_id'))
                    <h4>{{ __('Take advantage of this one-time offer!') }}</h4>
                    @endif
                    <div class="has-error mb-2 d-none">
                        <h4 class="payment-failed text-center">{{ __('Payment failed') }}</h4>
                        <div class="alert alert-danger alert-dismissible fade show payment-error text-center pe-5">{!! __('Something went wrong. If you are experiencing payment issues, please try to complete your payment with another payment method. If payment issues continue, please contact <a href=":support">customer support</a>.', ['support' => route('cmsContact')]) !!}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    </div>
                    <div class="no-error mb-2">
                        <h4 class="outofcredits d-none">{{ __('You are out of messages') }}</h4>
                        <div class="alert alert-warning no-subscription text-center">{!! __('This is <strong>no subscription.</strong><br/>Your credit card  will <strong>not be re-billed</strong>') !!}</div>
                    </div>
                    <h5 class="choose-bundle-title">{{ __('Choose bundle') }}</h5>
                    <div class="another-bundle text-center mb-2"><a href="#" class="change-bundle">
                        <i class="fas fa-arrow-left"></i> {{ __('Change bundle') }}</a>
                    </div>
                    <div class="accordion accordion-flush" id="accordion-store">
                        <div class="accordion-item">
                            <div id="accordion-products">
                                <div class="token-bundle">
                                    <ul class="choose-bundle">
                                        @php($base_price = $domain->products()->orderBy('credits')->first()->price_per_message)
                                        @foreach ($products as $product)
                                            <li>
                                                <input
                                                    data-credits="{{ $product->credits }}"
                                                    data-price="{{ $product->toPay() }}"
                                                    data-location="{{ $product->location }}"
                                                    autocomplete="off"
                                                    class="product-radio d-none"
                                                    type="radio"
                                                    id="product-{{ $product->id }}"
                                                    name="product_id"
                                                    value="{{ $product->id }}">
                                                <label role="button" for="product-{{ $product->id }}"
                                                    class="row g-0 product-label token @if ($product->best_seller) popular-product @endif @if ($product->location == 'promo') promo-product @endif">
                                                    @if ($product->best_seller)
                                                        <div class="col-12 product-most-popular w-100 text-center">
                                                            {{ __('Most popular') }}
                                                        </div>
                                                    @endif
                                                    @if ($product->location == 'promo')
                                                        <div class="col-12 product-offer w-100 text-center">
                                                            {{ __('One-time offer') }}
                                                        </div>
                                                    @endif
                                                    <div class="col-12">
                                                        <div class="row g-1 g-sm-2 justify-content-between">
                                                            <div class="col-3 product-description text-start">
                                                                <div class="credits">{{ $product->credits }}</div>
                                                                <div class="credit-text">{{ __('tokens') }}</div>
                                                            </div>
                                                            <div class="col-3 col-sm-5 product-discount">
                                                                <div class="discount @if ($product->location == 'promo') credits-promo @else credits-{{ $product->credits }} @endif text-center">
                                                                    @if ($base_price !== $product->price_per_message)
                                                                        {{ round((($product->price_per_message - $base_price) / $base_price) * 100) }}%
                                                                    @else
                                                                        &nbsp;
                                                                    @endif
                                                                </div>
                                                                <div class="ppm-text text-center">
                                                                    {{ __(':ppm per message', ['ppm' => currency($domain, $product->price_per_message)]) }}
                                                                </div>
                                                            </div>
                                                            <div class="col-5 col-sm-auto product-buy-now">
                                                                <div class="btn btn-buynow @if($product->credits == 200) btn-buynow-red @endif float-end">{{ __('Buy Now!') }}<br>{{ currency($domain, $product->toPay()) }}</div>
                                                            </div>
                                                            <div class="col-4 col-sm-auto product-change text-center d-none">
                                                                <div class="final-price">
                                                                    {{ currency($domain, $product->toPay()) }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </label>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <div id="accordion-payment">
                                <div class="prefferred">
                                    <h5 class="mt-2"> {{ __('Choose payment method') }}</h5>
                                    <div>
                                        <div class="row payment-methods g-2 mb-1">
                                            @foreach ($paymentMethods as $method)
                                                <div
                                                    class="col-12 {{ $method->provider->name }} {{ $method->type->name === 'apple_pay' || $method->type->name === 'google_pay' ? 'd-none' : '' }}">
                                                    <input
                                                        class="payment-method-radio d-none {{ $method->provider->name }}"
                                                        data-amount_min="{{ $method->amount_min }}"
                                                        data-amount_max="{{ $method->amount_max }}"
                                                        data-payment_type="{{ $method->type->name }}"
                                                        data-payment_provider="{{ $method->provider->name }}"
                                                        type="radio" id="payment-method-{{ $method->id }}"
                                                        name="payment_method_id"
                                                        value="{{ $method->id }}"
                                                        @if ($method->default) checked @endif>
                                                    <label for="payment-method-{{ $method->id }}"
                                                        class="row justify-content-between payment-method-label {{ $method->type->name }} {{ $method->type->name === 'apple_pay' || $method->type->name === 'google_pay' ? 'd-none' : '' }}">
                                                        <div class="col-auto payment-text">
                                                            {{ $method->type->title }}
                                                        </div>
                                                        <div class="col-auto payment-logo">
                                                            <img src="{{ asset('/global/img/cc/' . $method->type->icon . '.png') }}">
                                                        </div>
                                                    </label>
                                                </div>
                                            @endforeach
                                        </div>
                                        <button class="btn btn-pay buy-now buy-button my-3" type="submit">
                                            {{ __('Buy Now!') }}
                                        </button>
                                        <p class="credits-terms text-center">{!! __(
                                            'We respect your privacy. Your creditcard statement will be billed under the name :company. When clicking on "Buy Now!" you agree to our <a class="terms-modal" href=":agreement">Terms & Conditions</a>',
                                            ['company' => $domain->company->payment_provider_title, 'agreement' => route('cmsAgreement')],
                                        ) !!} </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@push('scripts')
    <script>
        $(document).ready(function() {

            @if (session()->get('show_store'))
                resetAccordion();
                @if (session()->get('payment_error'))
                $('.no-error, .has-error').toggleClass('d-none');
                @endif
                @if (session()->get('product_id'))
                showProduct("{{ session()->get('product_id') }}")
                @endif
                $('#store').modal('show');
            @endif

            function resetAccordion() {
                $('#accordion-products').css('max-height', '900px');
                $('#accordion-payment').css('max-height', '0');
                $('.product-radio + label').removeClass('d-none');
                $('.product-buy-now, .no-error, .choose-bundle-title').removeClass('d-none');
                $('.product-change, .outofcredits, .has-error, .another-bundle').addClass('d-none');
            }

            function showProduct(productId = null) {
                if (productId === null) {
                    productId = $('.product-radio:checked').val();
                }

                var productChekboxId = '#product-' + productId;
                $(productChekboxId).prop('checked', true);

                $('.product-radio + label').css('opacity', 0.1);

                $('#accordion-payment').css('max-height', '400px');
                $('#accordion-products').css('max-height', '125px');

                setTimeout(() => {
                    $('.product-radio + label').addClass('d-none');
                    $('.product-radio + label').css('opacity', 1);
                    $('.product-radio:checked + label').removeClass('d-none');
                    $('.product-buy-now, .choose-bundle-title').addClass('d-none');
                    $('.product-change, .another-bundle').removeClass('d-none');
                }, 500);
            }

            $(document).on('click', '.btn-buynow, .product-label', function() {
                var productChekboxId = $(this).data('id');
                $(productChekboxId).prop('checked', true);
                showProduct();
            });

            $(document).on('show.bs.modal', '#store', function(event) {
                resetAccordion();
                var source = $(event.relatedTarget).data('source');
                if (source == 'quickbuy') {
                    var credits = $(event.relatedTarget).data('credits');
                    var productId = $('input.product-radio[data-credits="' + credits + '"]').val();
                    showProduct(productId);
                }
                if (source == 'topbar') {
                    var productId = $('input.product-radio[data-location="promo"]').val();
                    showProduct(productId);
                }
            });

            $(document).on('click', '.change-bundle', function() {
                resetAccordion();
            });

            $(document).on("change", ".payment-method-radio", function(e) {
                e.stopPropagation();
                expandPaymentMethod();
            });
        });
    </script>
    @include('global.payment.scripts.payment-scripts')
    @foreach ($paymentMethods as $method)
        @if (View::exists('global.payment.scripts.' . $method->type->name))
            @include('global.payment.scripts.' . $method->type->name)
        @endif
    @endforeach
@endpush
