<!-- modal: emoticon -->
<div class="modal fade test" id="emojiPopup" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content p-2">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Select emoji') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-2">
                <ul class="emojiPopup-content">
                    @include('global.emoji-list-unicode')
                </ul>
            </div>
        </div>
    </div>
</div>
@push('scripts')
	<script>
        $(document).on('click', '.emojiPopup-content li a', function(){
            var message = $('#message').val();
            $('#message').val(message + $(this).html()).trigger('change');
            $('#emojiPopup').modal('hide');
        });
        $(document).on('hidden.bs.modal', '#emojiPopup', function(){
            $('#message').focus();
        });
        $(document).on('click', '.send-emoji', function(){
            $(".modal-backdrop").addClass("transparent");
        });
    </script>
@endpush
