<!-- Special Offer -->
<div class="modal fade special-offer-modal" data-bs-backdrop="static" tabindex="-1" data-bs-keyboard="false" aria-hidden="true">
	<div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
		<form method="POST" action="{{ route('pay') }}" class="modal-content text-center">
			<div class="modal-header">
				<h5 class="modal-title">{{ __('Trial offer') }}</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
                <img class="p-2 rounded-circle w-50" src="{{ cdn($domain) }}/images/special-offer.jpg"/>
                <h4>{{ __('Special one time deal!') }}</h4><h5>
                {!! __('Buy :credits credits for <span class="text-decoration-line-through">:initial</span> :final', [
                    'credits' => $offer->credits,
                    'initial' => currency($domain, $offer->amount),
                    'final' => currency($domain, $offer->toPay()),
                ]) !!}</h5>
			</div>
            <div class="modal-footer justify-content-center">
                <a href="{{ route('creditsOffer', ['offer_id' => $offer ? $offer->id : null]) }}" class="btn btn-primary modal-click">
                    {{ __('Buy Now!') }} <i class="fas fa-chevron-right"></i>
                </a>
			</div>
		</form>
	</div>
</div>
@push('scripts')
    <script>
        $( document ).ready(function() {
            let hasCredits = sessionStorage.getItem('lastKnownCredits') > 0;
            let recentlyShown = false;
            let lastShownAt = sessionStorage.getItem('specialOfferModalShownAt');
            if (lastShownAt) {
                recentlyShown = lastShownAt > Date.now() - specialOfferTimeout;
            }
            if (sessionStorage.getItem('visitedBuyCredits') && !window.location.href.includes('/credits') && !hasCredits && !recentlyShown && !hasPaid) {
                $('.special-offer-modal').modal('show');
                sessionStorage.removeItem('visitedBuyCredits');
                sessionStorage.setItem('specialOfferModalShownAt', Date.now());
            }

            $('.special-offer-modal').on('hide.bs.modal', function (e) {
                $.post("{{ route('saveNotification',['type' => 'popup', 'name' => 'special-offer', 'action' => 'dismiss']) }}");
            })
            $('.special-offer-modal .modal-click').on('click', function (e) {
                $.post("{{ route('saveNotification',['type' => 'popup', 'name' => 'special-offer', 'action' => 'click']) }}");
            })
		});
    </script>
@endpush
