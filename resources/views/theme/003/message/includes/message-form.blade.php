<form class="message-form" method="post" enctype="multipart/form-data" action="{{ route("addMessage", $profile->username) }}">
    @csrf
    <div class="message-box">
        <div class="row g-1">
            <div class="col-auto flex-grow-1">
                <textarea maxlength="800" id="message" class="w-100" name="message" spellcheck="false" cols="" rows="4" placeholder="{{ __('Type your message here') }}" required></textarea>
            </div>
            <div class="col-auto photo-preview d-none">
                <img src="" />
                <a class="btn btn-xs btn-primary remove-attachment" href="#"><i class="fas fa-times"></i></a>
            </div>
            <input class="d-none file-upload-field" type="file" accept=".jpg, .jpeg, .png, .webp" name="image">
        </div>
        <div class="row align-items-center">
            <div class="col-auto">
                <div class="btn btn-primary send-emoji d-none d-lg-inline-block" id="dropdownButtonEmoji" data-bs-toggle="dropdown"><i class="fa-solid fa-face-smile"></i></div>
                <div class="dropdown-menu emojiPopup-content" aria-labelledby="dropdownButtonEmoji">
                    <ul>
                        @include('global.emoji-list-unicode')
                    </ul>
                </div>
                <a href="#" class="btn btn-primary select-upload">
                    <i class="fa-solid fa-camera"></i>
                </a>
            </div>
            <div class="col-auto ms-auto">
                <button type="submit" class="btn btn-action send-message @if(!$user->hasEnoughCredits(1)) no-credits @endif">
                    {{ __('Send Message') }}
                </button>
            </div>
        </div>
    </div>
</form>
