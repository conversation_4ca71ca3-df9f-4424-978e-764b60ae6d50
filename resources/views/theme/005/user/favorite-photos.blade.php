
@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <div class="container mb-4">
        <div class="row">
            <div class="col-auto sidebar d-none d-lg-block">
                @include('theme.' . $domain->theme . '.layouts.includes.side-menu')
            </div>
            <div class="col">
                <div class="mb-3 block p-2">
                    <div class="row align-items-center justify-content-center g-1">
                        <div class="col-auto d-lg-none">
                            <a class="btn-back d-flex align-items-center justify-content-center" href="javascript:history.back()"><i class="fa-solid fa-arrow-left"></i></a>
                        </div>
                        <div class="col">
                            <h3 class="mb-0 ps-2">{{ __('Your saved images') }}</h3>
                        </div>
                    </div>
                </div>
                <div class="block p-2 p-md-4 mb-4">
                        <div class="row row-cols-2 row-cols-md-3 row-cols-xl-4 g-3">
                        @forelse($favoritePhotosGroups as $profileId => $photos)
                            @php
                                $profile = $photos->first()->upload->profile ?? null;
                                $photoCount = $photos->count();
                            @endphp
                            <div class="mb-2 photo-group">
                                <div class="row g-2">
                                    <div class="col">
                                        <h5 class="mb-2 text-truncate">
                                            {{ $profile?->username ?? __('Unknown Member') }}
                                        </h5>
                                    </div>
                                </div>
                                <div class="folder photocount-{{ $photoCount }}">
                                    @foreach ($photos as $photo)
                                        @php
                                            $upload = $photo->upload;
                                        @endphp
                                        @if ($domain->hasModule('premium') && !$user->isPremium())
                                            <a href="#"
                                                class="profile-image premium-liked-picture d-block position-relative">
                                                <img src="{{ thumb($upload->file, $profile?->gender) }}" alt="{{ $profile->username }}">
                                            </a>
                                        @else
                                            <a href="{{ route('photo') }}?name={{ $upload->file }}&profile={{ $profileId }}"
                                                class="profile-image magnific-ajax d-block position-relative">
                                                <img src="{{ thumb($upload->file, $profile?->gender) }}" alt="{{ $profile->username }}">
                                            </a>
                                        @endif
                                    @endforeach
                                    <div class="position-absolute bottom-0 end-0 p-2 pe-none">
                                        <div class="badge px-2"><i class="fa-regular fa-image me-1"></i> {{ $photoCount }}</div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <p>{{ __('You have no favorite photos yet.') }}</p>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- content end -->
@endsection
