@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <div class="content">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <div class="col-12">
                        <img src="{{ cdn($domain) }}/images/{{ strtolower($domain->name) }}.png" class="logo">
                    </div>
                    <div class="col-12 hide-restricted">
                        <img class="m-0 rounded-circle preferences-thumb"
                            src="{{ cdn($domain) }}/images/thumb-preferences.jpg">
                    </div>
                    <div class="col-12">
                        <div class="preferences-text p-3 mt-3">
                            <div class="preferences-text-arrow"></div>
                            {!! __('You\'re almost there... this will take less than 60 seconds') !!} 🔥
                        </div>
                    </div>
                </div>
            </div>
            <div class="step-bystep">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <form method="POST" id="multistepsform" action="{{ route('savePreferences') }}">
                            @csrf
                            @php($step = 1)
                            <fieldset>
                                <div class="block p-3 my-2">
                                    <div class="row text-center">
                                        <div class="col-auto">
                                            {{ __('Step') }} {{ $step }}/4
                                        </div>
                                        <div class="col">
                                            <h4>{{ __('Your interests') }}</h4>
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-secondary mb-1 invisible pe-none">
                                                {{ __('Skip') }} <i class="fa-solid fa-forward"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="row row-cols-4 g-1">
                                        <div class="col">
                                            <div class="progress-block">
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="progress-block blank">
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="progress-block blank">
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="progress-block blank">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="block p-3 my-2">
                                    <div class="row mt-3">
                                        <div class="col step-title text-center">
                                            {!! __('Please select at least 4 interests to proceed.') !!}
                                        </div>
                                    </div>
                                    <div class="row all-img">
                                        <div class="col-md-12 preferences text-center">
                                            @foreach ($preferences as $key => $preference)
                                                <input name="interests[{{ $key }}]" type="checkbox"
                                                    class="btn-check" id="btn-check-{{ $key }}"
                                                    autocomplete="off" value="1"
                                                    @if(old('interests.' . $key, $user->info->{$key})) checked @endif>
                                                <label class="btn" for="btn-check-{{ $key }}">
                                                    <div class="preference-icon">
                                                        <img src="{{ cdn($domain) }}/images/{{ $key }}.png"
                                                            alt="{{ $preferences[$key] }}"
                                                            class="img-fluid">
                                                    </div>
                                                    {{ $preferences[$key] }}
                                                </label>
                                            @endforeach
                                        </div>
                                    </div>
                                    <div class="row justify-content-between">
                                        <div class="row g-1 mt-3">
                                            <div class="col-6 col-md-auto ms-auto">
                                                <button type="button"
                                                    class="btn btn-action w-100 next px-5 preferences-next"
                                                    disabled>{{ __('Continue') }}</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @php($step++)
                            </fieldset>
                            <fieldset>
                                <div class="block p-3 my-2">
                                    <div class="row text-center">
                                        <div class="col-auto">
                                            {{ __('Step') }} {{ $step }}/4
                                        </div>
                                        <div class="col">
                                            <h4>{{ __('Your bio') }}</h4>
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-secondary next preferences-next mb-1">
                                                {{ __('Skip') }} <i class="fa-solid fa-forward"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="row row-cols-4 g-1">
                                        <div class="col">
                                            <div class="progress-block">
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="progress-block">
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="progress-block blank">
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="progress-block blank">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="block p-3 my-2">
                                    <div class="row mt-3">
                                        <div class="col step-title text-center">
                                            {!! __('Tell us a few things about yourself.') !!}
                                        </div>
                                    </div>
                                    <div>
                                        <textarea class="form-control about-me-input"
                                            dusk="about-me-text"
                                            placeholder="{{ __('Describe yourself in a few sentences. Writing something about yourself will make your profile 10x more appealing!') }}"
                                            spellcheck="false" name="about" maxlength="500"></textarea>
                                    </div>
                                    <div class="row g-1 mt-3">
                                        <div class="col-6 col-md-auto">
                                            <button type="button"
                                                class="btn btn-secondary w-100 previous px-5">{{ __('Back') }}</button>
                                        </div>
                                        <div class="col-6 col-md-auto ms-auto">
                                            <button type="button"
                                                class="btn btn-action w-100 next px-5 preferences-next"
                                                disabled>{{ __('Continue') }}</button>
                                        </div>
                                    </div>
                                </div>
                                @php($step++)
                            </fieldset>
                            <fieldset>
                                <div class="block p-3 my-2">
                                    <div class="row text-center">
                                        <div class="col-auto">
                                            {{ __('Step') }} {{ $step }}/4
                                        </div>
                                        <div class="col">
                                            <h4>{{ __('Profile picture') }}</h4>
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-secondary next mb-1">
                                                {{ __('Skip') }} <i class="fa-solid fa-forward"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="row row-cols-4 g-1">
                                        <div class="col">
                                            <div class="progress-block">
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="progress-block">
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="progress-block">
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="progress-block blank">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="block p-3 my-2">
                                    <div class="row mt-3">
                                        <div class="col step-title text-center">
                                            {{ __('Please add your profile picture.') }}
                                        </div>
                                    </div>
                                    <div class="row text-center">
                                        <div class="col add-photos">
                                            <div class="text-center mb-3">
                                                <a id="preferences-upload-photo" href="#"
                                                    class="btn btn-action profile-upload">{{ __('Upload profile picture') }}</a>
                                            </div>
                                            <div id="photo-preview">
                                                <img src="{{ thumb($user->profile_image, $user->gender) }}"
                                                    alt="iconictouches" class="img-fluid">
                                            </div>
                                            <div class="upload-later text-center">
                                                {{ __('Add a profile picture and get more attention.') }}
                                                <br />{{ __('Before uploading, please read our') }}:
                                                <a class="fw-bold fst-italic" data-bs-toggle="modal"
                                                    data-bs-target="#upload-rules">
                                                    {{ __('Upload rules') }}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row g-1 mt-3">
                                        <div class="col-6 col-md-auto">
                                            <button type="button"
                                                class="btn btn-secondary w-100 previous px-5">{{ __('Back') }}</button>
                                        </div>
                                        <div class="col-6 col-md-auto ms-auto">
                                            <button type="button"
                                                class="btn btn-action w-100 next px-5 preferences-next"
                                                disabled>{{ __('Continue') }}</button>
                                        </div>
                                    </div>
                                </div>
                                @php($step++)
                            </fieldset>
                            <fieldset>
                                <div class="block p-3 my-2">
                                    <div class="row text-center">
                                        <div class="col-auto">{{ __('Step') }} {{ $step }}/4</div>
                                        <div class="col">
                                            <h4>{{ __('Public photos') }}</h4>
                                        </div>
                                        <div class="col-auto">
                                            <button type="submit" class="btn btn-secondary mb-1">
                                                {{ __('Skip') }} <i class="fa-solid fa-forward"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="row row-cols-4 g-1">
                                        <div class="col">
                                            <div class="progress-block">
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="progress-block">
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="progress-block">
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="progress-block">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="block p-3 my-2">
                                    <div class="row mt-3">
                                        <div class="col step-title text-center">
                                            {{ __('Please add your public photos.') }}
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="row row-cols-3 row-cols-lg-6 g-2 gallery-wrapper">
                                            <div class="col">
                                                <div class="preference-gallery-image">
                                                    <img src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                                        class="w-100 user-public-image no-image">
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="preference-gallery-image">
                                                    <img src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                                        class="d-block user-public-image no-image">
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="preference-gallery-image">
                                                    <img src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                                        class="d-block user-public-image no-image">
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="preference-gallery-image">
                                                    <img src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                                        class="d-block user-public-image no-image">
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="preference-gallery-image">
                                                    <img src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                                        class="d-block user-public-image no-image">
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="preference-gallery-image">
                                                    <img src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                                                        class="d-block user-public-image no-image">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 text-center mt-2 mb-4">

                                        <button
                                            class="d-block ms-auto me-auto mb-3 btn btn-primary btn-add-photo select-upload profile-public-images-upload">
                                            <i class="bi bi-card-image"></i> {{ __('Upload gallery photos') }}
                                        </button>

                                        {{ __('Before uploading, please read our') }}:
                                        <a class="fw-bold fst-italic" data-bs-toggle="modal"
                                            data-bs-target="#upload-rules">
                                            {{ __('Upload rules') }}
                                        </a>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-6 col-md-auto">
                                            <button type="button"
                                                class="btn btn-secondary w-100 previous px-5">{{ __('Back') }}</button>
                                        </div>
                                        <div class="col-6 col-md-auto ms-auto">
                                            <button type="submit"
                                                class="btn btn-action w-100 px-5">{{ __('Complete') }}</button>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </form>
                        <form method="POST" id="profile-image-form" action="{{ route('profileImage') }}">
                            @csrf
                            <input class="d-none" type="file" accept=".jpg, .jpeg, .png, .webp" name="image"
                                id="profile-photo">
                        </form>
                        <form id="profile-public-images-form" action="{{ route('upload') }}"
                            enctype="multipart/form-data"
                            method="POST">
                            @csrf
                            <input type="hidden" name="user" value="{{ $user->id }}">
                            <input type="file" accept=".jpg, .jpeg, .png, .webp" name="image"
                                class="d-none" id="profile-public-images">
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('theme.' . $domain->theme . '.popup.rules-upload')
@endsection
@push('scripts')
    <script>
        $(document).on("click", ".profile-upload", function(ev) {
            ev.preventDefault();
            ev.stopPropagation();
            if ($(this).hasClass('loading')) {
                return false;
            }
            $("#profile-photo").trigger("click");
        });

        $('#profile-photo').on('change', function() {
            $("#profile-image-form").submit();
        });

        $("#profile-image-form").on('submit', (function(ev) {
            ev.preventDefault();
            var actionUrl = $(this).prop("action");
            var formData = new FormData(this);
            var files = $('#profile-photo')[0].files[0];
            formData.append('file', files);
            $("#preferences-upload-photo").addClass("loading");

            $.ajax({
                url: actionUrl,
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                cache: false,
                success: function(data) {
                    if (data.result == false) {
                        toastr.error(data.error);
                    } else {
                        $("#photo-preview img").attr('src', data.url);
                        $("#profile-image-form")[0].reset();
                        $("#preferences-upload-photo").removeClass("loading");
                        toastr.success(data.success);
                    }
                },
                error: function(e) {}
            });
        }));

        $(document).ready(function() {
            // Check how many checkboxes are checked and update the button state
            $('.preferences-next').prop('disabled', $('.preferences input:checkbox:checked').length < 4);
        });

        $('input:checkbox').on('change', function() {
            $('.chat-group-next').prop('disabled', $('.chat-group input:checkbox:checked').length < 3);
            $('.preferences-next').prop('disabled', $('.preferences input:checkbox:checked').length < 4);
        });

        // public img upload
        $(document).on("click", ".profile-public-images-upload", function(ev) {
            ev.preventDefault();
            ev.stopPropagation();
            $("#profile-public-images").trigger("click");
        });

        $('#profile-public-images').on('change', function() {
            $("#profile-public-images-form").submit();
        });

        $("#profile-public-images-form").on('submit', (function(ev) {
            ev.preventDefault();
            var actionUrl = $(this).prop("action");
            var formData = new FormData(this);
            var files = $('#profile-public-images')[0].files[0];
            formData.append('file', files);

            $.ajax({
                url: actionUrl,
                type: "POST",
                data: formData,
                contentType: false,
                processData: false,
                cache: false,
                success: function(data) {
                    if (data.result == false) {
                        toastr.error(data.error);
                    } else {
                        $(".user-public-image.no-image").eq(0).attr('src', data.url).removeClass(
                            'no-image');
                        $("#profile-public-images-form")[0].reset();
                        toastr.success(data.success);

                        if (!$(".user-public-image.no-image")[0]) {
                            $('.profile-public-images-upload').addClass('disabled');
                        }
                    }
                },
                error: function(e) {
                    toastr.error("Error uploading photo");
                }
            });
        }));
    </script>
@endpush
