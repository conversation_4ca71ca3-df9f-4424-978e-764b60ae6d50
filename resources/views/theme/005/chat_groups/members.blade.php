@php
    /** @var \App\Models\User $user */
@endphp
@php
    /** @var \App\Models\ChatGroupMember $member */
@endphp

@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <!-- content start -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col sidebar d-none d-lg-block">
                    @include('theme.' . $domain->theme . '.layouts.includes.side-menu')
                </div>
                <div class="col">
                    <div class="contributions">
                        <div class="head-addnew">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="head">
                                        <h6>{{ __('Members') }}</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            @foreach ($members as $member)
                                <div class="col-sm-6 col-md-12 col-lg-6">
                                    <div class="contribu-box">
                                        <div class="detail-date">
                                            <div class="p-image">
                                                <a href="#">
                                                    <img src="{{ thumb($member->related->profile_image, $member->related->gender) }}"
                                                        class="img-fluid">
                                                </a>
                                            </div>
                                            <div class="location-date">
                                                <div>
                                                    <div class="locati">
                                                        <i class="fa-solid fa-location-dot"></i>
                                                        {{ $member->related->info->region->name }}
                                                    </div>
                                                    <div class="date">
                                                        <i class="fa-solid fa-calendar"></i>
                                                        {{ $member->related->info->age }}
                                                    </div>
                                                </div>
                                                <div class="name">
                                                    <h5>{{ $member->related->username }}</h5>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text">
                                            <p>
                                                {{ $member->related->username }}
                                            </p>
                                            <p>
                                                {{ __('Type:') }} {{ $member->related->gender }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                            {{ $members->links('theme.' . $domain->theme . '.pagination') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
