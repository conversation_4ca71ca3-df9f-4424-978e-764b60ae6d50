@php
    $company = $domain->company;
@endphp
<div class="modal fade cms-terms" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    {{ __('Terms and Conditions') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-3">

                    <strong>{!! trans_cms('agreement.intro', [
                        'app' => $domain->name_url,
                    'company_name' => $domain->company->name,
                        'company_rebilling' => $company
                    ]) !!}</strong>
                    {!! trans_cms('agreement.block',[
                        'agreement' => route('cmsAgreement'),
                        'privacy' => route('cmsPrivacy'),
                        'pricing' => route('cmsPricing'),
                        'company_name' => $domain->company->name,
                        'company_rebilling' => $company,
                        'support_email' => 'support@' . $domain->name_url,
                        'name_url' => $domain->name_url,
                        'address' => $domain->company->address,
                        'zip' => $domain->company->zip,
                        'city' => $domain->company->city,
                        'country' => $domain->company->country,
                        'app_url' => $domain->url,
                        'contact_phone' => $domain->company->contact_phone,
                        'chamber_of_commerce' => $domain->company->chamber_of_commerce,
                    ]) !!}

            </div>
            <div class="modal-footer justify-content-center">
                <button role="button" type="button" class="btn btn-primary" data-bs-dismiss="modal"> {{ __('Close') }}</button>
            </div>
        </div>
    </div>
</div>
