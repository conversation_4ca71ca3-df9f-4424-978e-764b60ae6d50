    <div class="modal fade chat-menu-modal" data-bs-keyboard="false" id="chat-menu" tabindex="-1" aria-labelledby="mobileMenuLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content py-3 px-2">
                {{-- <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="top:-20px!important;"></button> --}}
                <div class="main-menu-popup">
                    <div class="row row-cols-1 g-3">
                        <div class="col">
                            <a href="#" data-add="{{ route('addLike', $profile->username) }}"
                                data-remove="{{ route('removeLike', $profile->username) }}"
                                class="btn w-100 border-0 favorite @if ($profile->isFavorite()) is-favorite @else add-favorite @endif">
                                <span class="text-is-favorite">
                                    <div class="row g-3 text-start d-flex align-items-center justify-content-center">
                                        <div class="col">
                                            <h5 class="mb-0">{{ __('Remove from likes') }}</h5>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fa-solid fa-heart fa-fw"></i>
                                        </div>
                                    </div>
                                </span>
                                <span class="text-add-favorite">
                                    <div class="row g-3 text-start d-flex align-items-center justify-content-center">
                                        <div class="col">
                                            <h5 class="mb-0">{{ __('Add to likes') }}</h5>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fa-solid fa-heart fa-fw"></i>
                                        </div>
                                    </div>
                                </span>
                            </a>
                       </div>
                       <div class="col">
                            <a href="{{ route('profile', $profile->username) }}" class="btn w-100 border-0">
                                <div class="row g-3 text-start d-flex align-items-center justify-content-center">
                                    <div class="col">
                                        <h5 class="mb-0">{{ __('View profile') }}</h5>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fa-solid fa-user fa-fw"></i>
                                    </div>
                                </div>
                            </a>
                       </div>
                       <div class="col">
                            @if($conversation)
                                @if($conversation->isArchived())
                                    <a href="{{ route('unArchiveConversation', ['id' => $conversation->id]) }}" class="btn w-100 border-0">
                                        <div class="row g-3 text-start d-flex align-items-center justify-content-center">
                                            <div class="col">
                                                <h5 class="mb-0">{{ __('Remove conversation from archive') }}</h5>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fa-solid fa-inbox fa-fw"></i>
                                            </div>
                                        </div>
                                    </a>
                                @else
                                    <a href="{{ route('archiveConversation', ['id' => $conversation->id]) }}" class="btn w-100 border-0 @if(!$user->profile_image) no-archive @endif" >
                                        <div class="row g-3 text-start d-flex align-items-center justify-content-center">
                                            <div class="col">
                                                <h5 class="mb-0">{{ __('Archive conversation') }}</h5>
                                            </div>
                                            <div class="col-auto">
                                                <i class="fa-solid fa-box-archive fa-fw"></i>
                                            </div>
                                        </div>
                                    </a>
                                @endif
                            @endif
                        </div>
                        {{--
                        <div class="col">
                            <a href="#" class="btn w-100 border-0 ">
                                <div class="row g-3 text-start d-flex align-items-center justify-content-center">
                                    <div class="col">
                                        <h5 class="mb-0">{{ __('Delete conversation') }}</h5>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fa-solid fa-trash fa-fw"></i>
                                    </div>
                                </div>
                            </a>
                        </div>
                        --}}
                       <div class="col">
                            <div data-bs-dismiss="modal" class="btn w-100 border-0">
                                <div class="row g-3 text-start d-flex align-items-center justify-content-center">
                                    <div class="col">
                                        <h5 class="mb-0">{{ __('Cancel') }}</h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).on("click", ".chat-menu-btn", function(ev) {
                ev.preventDefault();
                $("#chat-menu").modal("show");
                return false;
            });
        </script>
    @endpush
