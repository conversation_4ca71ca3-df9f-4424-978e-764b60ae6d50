    <div class="modal fade profile-menu-modal" data-bs-keyboard="false" id="profile-menu" tabindex="-1" aria-labelledby="mobileMenuLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content py-3 px-2">
                {{-- <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button> --}}
                <div class="main-menu-popup">
                    <div class="row row-cols-1 g-3">
                       <div class="col">
                            <a href="{{ route('editSettings') }}" class="btn w-100 border-0 @if($currentRoute == 'settings') active @endif">
                                <div class="row g-3 text-start d-flex align-items-center justify-content-center">
                                    <div class="col">
                                        <h5 class="mb-0">{{ __('View profile') }}</h5>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fa-solid fa-user fa-fw"></i>
                                    </div>
                                </div>
                            </a>
                       </div>
                       <div class="col">
                            <a href="{{ route('editSettings') }}" class="btn w-100 border-0 @if($currentRoute == 'settings') active @endif">
                                <div class="row g-3 text-start d-flex align-items-center justify-content-center">
                                    <div class="col">
                                        <h5 class="mb-0">{{ __('Remove from likes') }}</h5>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fa-solid fa-user-minus fa-fw"></i>
                                    </div>
                                </div>
                            </a>
                       </div>
                       <div class="col">
                            <a href="{{ route('editSettings') }}" class="btn w-100 border-0 @if($currentRoute == 'settings') active @endif">
                                <div class="row g-3 text-start d-flex align-items-center justify-content-center">
                                    <div class="col">
                                        <h5 class="mb-0">{{ __('Cancel') }}</h5>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
