@if(!$user->profile_image)
<div class="modal fade enable-archive" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <form method="POST" action="{{ route('profileImage') }}" enctype="multipart/form-data" class="modal-content">
            @csrf
            <input type="file" accept=".jpg, .jpeg, .png, .webp" name="image" class="custom-file-input upload-instantly d-none file-upload-field">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Enable archive?') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-3">
                <div class="failed-message">
                    <div class="fpay-icon">
                        <img src="{{ cdn($domain) }}/images/archive.png" alt="Archive" class="bundal-round">
                    </div>
                </div>
                <p class="mb-4">
                    {!! __('Messages get deleted after 60 days. However, you can store messages permanently by archiving the conversation. <strong>You unlock this feature by uploading a profile picture.</strong>') !!}
                </p>
            </div>
            <div class="modal-footer justify-content-center">
                <button role="button" type="button" class="btn btn-primary" data-bs-dismiss="modal"> {{ __('Close') }}</button>
                <button role="button" type="button" class="btn btn-action select-upload">{{ __('Upload photo') }}</button>
            </div>
        </form>
    </div>
</div>
@endif
