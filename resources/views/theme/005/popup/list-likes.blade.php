<div id="post-likes" class="modal fade post-likes" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <form method="POST" action="#" class="modal-content">
            @csrf
            <div class="modal-header">
                <h5 class="modal-title">{{ __('Who liked this post') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-1">
                <div class="spinner-border text-primary spinner my-5 d-none" role="status">
                    <span class="visually-hidden">{{ __('Loading...') }}</span>
                </div>
                <div class="content-likes">
                </div>
            </div>
            <div class="modal-footer justify-content-center">
                <button role="button" type="button" class="decline btn btn-primary" data-bs-dismiss="modal">
                    {{ __('Close') }}</button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
    <script>
        $(document).ready(function() {
            $('.likes').click(function() {
                $('#post-likes .spinner').removeClass('d-none');
                $('#post-likes .content-likes').addClass('d-none').html('');
                $('#post-likes').modal('show');

                var id = $(this).data('post');

                $.ajax({
                    url: "{{ route('viewPostLikes') }}",
                    type: "POST",
                    data: {
                        post_id: id
                    },
                    success: function (data) {
                        $('#post-likes .content-likes').html(data);
                        $('#post-likes .spinner, #post-likes .content-likes').toggleClass('d-none');
                    },
                });

            });
        });
    </script>
@endpush
