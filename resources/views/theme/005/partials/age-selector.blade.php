@php \Carbon\Carbon::setLocale(app()->getLocale()); @endphp
<div class="row g-2 mb-0">
    <div class="col-4 order-{{ $dateOrder['day'] }}">
        <select class="form-select" name="birth_day" required="">
            <option value="">{{ __('DD') }}</option>
            @for($d = 1; $d < 32; $d++)
            <option value="{{ sprintf("%02d", $d) }}" @if(old('birth_day') == sprintf("%02d", $d)) selected @endif>{{ sprintf("%02d", $d) }}</option>
            @endfor
        </select>
    </div>
    <div class="col-4 order-{{ $dateOrder['month'] }}">
        <select class="form-select" name="birth_month" required="">
            <option value="">{{ __('MM') }}</option>
            @for($m = 1; $m < 13; $m++)
            <option value="{{ sprintf("%02d", $m) }}" @if(old('birth_month') == sprintf("%02d", $m)) selected @endif>{{ ucfirst(\Carbon\Carbon::parse('2010-'.$m.'-01')->translatedFormat('M')) }}</option>
            @endfor
        </select>
    </div>
    <div class="col-4 order-{{ $dateOrder['year'] }}">
        <select class="form-select" name="birth_year" required="">
            <option value="">{{ __('YYYY') }}</option>
            @for($y = date('Y') - 18; $y > date('Y') - 101; $y--)
            <option value="{{ $y }}" @if(old('birth_year') == $y) selected @endif>{{ $y }}</option>
            @endfor
        </select>
    </div>
</div>
