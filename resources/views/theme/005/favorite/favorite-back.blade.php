@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <div class="container mb-4">
        <div class="row">
            <div class="col-auto sidebar d-none d-lg-block">
                @include('theme.' . $domain->theme . '.layouts.includes.side-menu')
            </div>
            <div class="col">
                <div class="sub-menu">
                    <div class="row g-0">
                        <div class="col">
                            <a class="btn" href="{{ route('favorites') }}">
                                {{ __('My likes') }}
                            </a>
                        </div>
                        <div class="col">
                            <a class="btn active" href="{{ route('favoriteBack') }}">
                                {{ __('Liked me') }}
                            </a>
                        </div>
                    </div>
                </div>
                <div class="mt-4 mb-3">
                    @if ($domain->hasModule('premium') && !$user->isPremium())
                    @else
                        @if ($favorites->isEmpty())
                            <h5>
                                {{ __('Members that liked your profile') }}
                            </h5>
                        @endif
                    @endif
                    <div class="favorite-sendmess">
                        <div class="row g-2">
                            @foreach ($favorites as $profile)
                                @php
                                    $isMatch = $profile->user_action && $profile->profile_action;
                                @endphp
                                <div class="col-12 col-lg-6">
                                    <div
                                        class="liked-card premium-liked-you @if ($profile->isFavorite()) favorite-profile @endif">
                                        <div class="row g-1 align-items-center">
                                            <div class="col-auto me-3">
                                                <div class="d-block position-relative">
                                                    @if ($domain->hasModule('premium') && !$user->isPremium())
                                                        <span class="blur-element-container">
                                                            <img class="position-relative d-block overflow-hidden photo blur-element pe-none"
                                                                src="{{ thumb($profile->profile_image, $profile->gender, true) }}"
                                                                alt="">
                                                            @if ($profile->online)
                                                                <div class="online-cirle-container-2">
                                                                    <span class="online-cirle"><i
                                                                            class="fas fa-circle"></i></span>
                                                                </div>
                                                            @endif
                                                        </span>
                                                    @else
                                                        <a
                                                            href="{{ route('profile', ['username' => $profile->username]) }}">
                                                            <img class="photo"
                                                                src="{{ thumb($profile->profile_image, $profile->gender) }}"
                                                                alt="{{ $profile->username }}">
                                                            @if ($profile->online)
                                                                <div class="online-cirle-container-2">
                                                                    <span class="online-cirle"><i
                                                                            class="fas fa-circle"></i></span>
                                                                </div>
                                                            @endif
                                                        </a>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="col">
                                                @if ($domain->hasModule('premium') && !$user->isPremium())
                                                    <span class="h5 text-break">
                                                        {!! str_repeat('&bull;', strlen($profile->username)) !!}
                                                    </span>
                                                @else
                                                    <a href="{{ route('profile', ['username' => $profile->username]) }}"
                                                        class="h5">
                                                        {{ $profile->username }}
                                                    </a>
                                                <div>
                                                    @if ($isMatch)
                                                        <span class="text-matches">{{ __('You matched!') }}</span>
                                                    @endif
                                                </div>

                                                @endif
                                            </div>
                                            @if ($domain->hasModule('premium') && !$user->isPremium())
                                            @else
                                                <div class="col-auto">
                                                    <a href="#"
                                                        data-add="{{ route('addLike', $profile->username) }}"
                                                        data-remove="{{ route('removeLike', $profile->username) }}"
                                                        class="btn btn-small btn-small-fav d-flex align-items-center justify-content-center favorite @if ($profile->isFavorite()) is-favorite @else add-favorite @endif">
                                                        <span class="text-is-favorite"><i
                                                                class="fa-solid fa-heart fa-fw"></i></span>
                                                        <span class="text-add-favorite"><i
                                                                class="fa-solid fa-heart fa-fw"></i></span>
                                                    </a>
                                                </div>
                                                <div class="col-auto">
                                                    <a href="{{ route('conversation', ['type' => 'all', 'username' => $profile->username]) }}"
                                                        class="btn btn-small btn-small-text d-flex align-items-center justify-content-center">
                                                        <i class="fa-solid fa-comment-dots fa-fw"></i>
                                                        @if (in_array($profile->id, $withConversations))
                                                            <div class="message-cirle-container">
                                                                <span class="message-cirle"><i class="fas fa-circle"></i></span>
                                                            </div>
                                                        @endif
                                                    </a>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                            @if ($favorites->isEmpty())
                                <div class="col-12">
                                    <div class="page-content text-center">
                                        @if ($domain->hasModule('favorites_to_likes'))
                                            {!! __('You haven\'t received any likes yet') !!}
                                        @else
                                            {{ __('You haven\'t gotten any admirers yet.') }}
                                        @endif
                                    </div>
                                </div>
                            @endif
                            @if ($domain->hasModule('premium') && !$user->isPremium())
                                <div class="py-4 text-center">
                                    {!! __(
                                        'Profiles that liked you are only shown for a limited time. Don’t miss your chance — <a href=":premium">upgrade to Premium!</a>.',
                                        ['premium' => route('premium')],
                                    ) !!}
                                </div>
                            @endif
                        </div>
                        {{ $favorites->appends($data ?? '')->links('theme.' . $domain->theme . '.pagination') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- content end -->
@endsection
