@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <div class="container">
        <div class="row g-3 mb-3">
            <div class="col-auto">
                <a class="btn-back d-flex align-items-center justify-content-center" href="javascript:history.back()"><i
                        class="fa-solid fa-arrow-left"></i></a>
            </div>
            <div class="col d-flex align-items-center">
                <h3>
                    {!! trans_cms('pricing.title') !!}
                </h3>
            </div>
        </div>
    </div>
    <div class="content mb-4">
        <div class="container">
            {!! trans_cms('pricing.block') !!}
            <div class="w-100 p-3">
                <div class="row border-bottom">
                    <div class="col"><strong>{{ __('Bundle price') }}</strong></div>
                    <div class="col"><strong>{{ __('Credits') }}</strong></div>
                    <div class="col"><strong>{{ __('Price per message') }}</strong></div>
                </div>
                @foreach ($products as $product)
                    <div class="row border-bottom">
                        <div class="col">{{ currency($domain, $product->toPay()) }}
                            {{ !empty($product->location) && $product->location != 'main' ? '(' . $product->location . ')' : '' }}
                        </div>
                        <div class="col">{{ $product->credits }}</div>
                        <div class="col">{{ currency($domain, $product->price_per_message) }}</div>
                    </div>
                @endforeach
            </div>
            @if ($domain->hasModule('premium'))
                {!! trans_cms('pricing.premium', ['agreement' => route('cmsAgreement')]) !!}
                @if ($domain->use_subscriptions)
                    {!! trans_cms('pricing.cancel_membership', ['url' => $domain->url]) !!}
                @endif
                <br>
                @if ($domain->use_subscriptions)
                    <div class="w-100 p-3">
                        <div class="row border-bottom">
                            <div class="col"><strong>{{ __('Premium Package') }}</strong></div>
                            <div class="col"><strong>{{ __('Price') }}</strong></div>
                            <div class="col"><strong>{{ __('Price per month') }}</strong></div>
                        </div>
                        @foreach ($products_subscription as $subscription)
                            <div class="row border-bottom">
                                <div class="col">{{ $subscription->periodInMonths() }}
                                    {{ $subscription->periodInMonths() > 1 ? __('months') : __('month') }}</div>
                                <div class="col">{{ currency($domain, $subscription->toPay()) }}</div>
                                <div class="col">{{ currency($domain, $subscription->price_per_month) }}</div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="w-100 p-3">
                        <div class="row border-bottom">
                            <div class="col"><strong>{{ __('Premium Package') }}</strong></div>
                            <div class="col"><strong>{{ __('Price') }}</strong></div>
                            <div class="col"><strong>{{ __('Price per month') }}</strong></div>
                        </div>
                        @foreach ($products_premium as $premium)
                            <div class="row border-bottom">
                                <div class="col">{{ $premium->months }}
                                    {{ $premium->months > 1 ? __('months') : __('month') }}</div>
                                <div class="col">{{ currency($domain, $premium->toPay()) }}</div>
                                <div class="col">{{ currency($domain, $premium->price_per_month) }}</div>
                            </div>
                        @endforeach
                    </div>
                @endif
            @endif
            <br>
            @if (!$domain->use_subscriptions)
                <strong>{!! trans_cms('pricing.footer') !!}</strong>
                <br>
            @endif

            <small>
                @if (in_array(strtoupper($domain->country_code), ['CA']))
                    {!! trans_cms('pricing.taxes_ca') !!}<br>
                @elseif(!in_array(strtoupper($domain->country_code), ['US']))
                    {!! trans_cms('pricing.vat') !!}<br>
                @endif
            </small>
        </div>
    </div>
@endsection
