@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <div class="container mb-4">
        <div class="row">
            <div class="col-auto sidebar d-none d-lg-block">
                @include('theme.' . $domain->theme . '.layouts.includes.side-menu')
            </div>
            <div class="col">
                <div class="mb-3 block p-2">
                    <div class="row align-items-center justify-content-center g-1">
                        @auth
                        <div class="col-auto menu-btn-container d-lg-none">
                            <button class="menu-btn" data-bs-toggle="modal" data-bs-target="#mobile-menu">
                                <i class="fa-solid fa-bars"></i>
                            </button>
                        </div>
                        @endauth
                        <div class="col">
                            <h3 class="mb-0 ps-2">{{ __('Your viewers') }}</h3>
                        </div>
                        @auth
                        @if ($domain->hasModule('premium') && !$user->isPremium())
                            <div class="col-auto d-flex align-items-center justify-content-center">
                                <a href="{{ route('premium') }}" class="btn btn-action-2">
                                    {{ __('Upgrade') }}
                                </a>
                            </div>
                        @endif
                        @endauth
                    </div>
                </div>
                <div class="mt-4 mb-3">
                    @if ($visitors->isEmpty())
                        <h5>
                            {{ __('Members that viewed your profile') }}
                        </h5>
                    @endif
                    <div class="row g-2">
                        @foreach ($visitors as $visitor)
                            @if (!$visitor->profile)
                                @continue
                            @endif
                            <div class="col col-12 col-lg-6">
                                <div
                                    class="liked-card premium-visitor block @if ($visitor->profile->isFavorite()) favorite-profile @endif">
                                    <div class="row g-1 align-items-center">
                                        <div class="col-auto me-3">
                                            <div class="d-block position-relative">
                                                @if ($domain->hasModule('premium') && !$user->isPremium())
                                                    <span class="blur-element-container">
                                                        <img class="position-relative d-block overflow-hidden photo blur-element pe-none"
                                                            src="{{ thumb($visitor->profile->profile_image, $visitor->profile->gender, true) }}"
                                                            alt="">
                                                        @if ($visitor->profile->online)
                                                            <div class="online-cirle-container-2">
                                                                <span class="online-cirle"><i
                                                                        class="fas fa-circle"></i></span>
                                                            </div>
                                                        @endif
                                                    </span>
                                                @else
                                                    <a
                                                        href="{{ route('profile', ['username' => $visitor->profile->username]) }}">
                                                        <img class="photo"
                                                            src="{{ thumb($visitor->profile->profile_image, $visitor->profile->gender) }}"
                                                            alt="{{ $visitor->profile->username }}">
                                                        @if ($visitor->profile->online)
                                                            <div class="online-cirle-container-2">
                                                                <span class="online-cirle"><i
                                                                        class="fas fa-circle"></i></span>
                                                            </div>
                                                        @endif
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col">
                                            @if ($domain->hasModule('premium') && !$user->isPremium())
                                                <span class="h5 text-break">
                                                    {!! str_repeat('&bull;', strlen($visitor->profile->username)) !!}
                                                </span>
                                            @else
                                                <a href="{{ route('profile', ['username' => $visitor->profile->username]) }}"
                                                    class="h5 text-break">
                                                    {{ $visitor->profile->username }}
                                                </a>
                                            @endif
                                        </div>
                                        @if ($domain->hasModule('premium') && !$user->isPremium())
                                        @else
                                            <div class="col-auto">
                                                <a href="#"
                                                    data-add="{{ route('addLike', $visitor->profile->username) }}"
                                                    data-remove="{{ route('removeLike', $visitor->profile->username) }}"
                                                    class="btn btn-small btn-small-fav d-flex align-items-center justify-content-center favorite @if ($visitor->profile->isFavorite()) is-favorite @else add-favorite @endif">
                                                    <span class="text-is-favorite"><i
                                                            class="fa-solid fa-heart fa-fw"></i></span>
                                                    <span class="text-add-favorite"><i
                                                            class="fa-solid fa-heart fa-fw"></i></span>
                                                </a>
                                            </div>
                                            <div class="col-auto">
                                                <a href="{{ route('conversation', ['type' => 'all', 'username' => $visitor->profile->username]) }}"
                                                    class="btn btn-small btn-small-text d-flex align-items-center justify-content-center">
                                                    <i class="fa-solid fa-comment-dots fa-fw"></i>
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                        @if ($visitors->isEmpty())
                            <div class="col-12">
                                <div class="page-content text-center">
                                    {{ __('You haven\'t received any profile visits yet.') }}
                                </div>
                            </div>
                        @endif
                        @if ($domain->hasModule('premium') && !$user->isPremium())
                            <div class="py-4 text-center">
                                {!! __(
                                    'Your viewers are only shown for a limited time. See who’s interested in you before they disappear — <a href=":premium">upgrade to Premium!</a>.',
                                    ['premium' => route('premium')],
                                ) !!}
                            </div>
                        @endif
                    </div>
                    {{ $visitors->appends($data ?? '')->links('theme.' . $domain->theme . '.pagination') }}
                </div>
            </div>
        </div>
    </div>
    <!-- content end -->
@endsection
