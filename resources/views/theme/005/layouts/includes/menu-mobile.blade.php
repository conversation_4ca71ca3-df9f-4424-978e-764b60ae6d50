<div class="main-menu-bottom clearfix">
    <div class="row gx-1 p-1 @if ($enable_banners) row-cols-5 @else row-cols-3 @endif">
        <div class="col">
            <a href="{{ route('home') }}" class="btn @if (request()->routeIs('home')) active @endif">
                <div class="icon"> <i class="fa-solid fa-magnifying-glass fa-fw"></i></div>
                <div>{{ __('Search') }}</div>
            </a>
        </div>
        <div class="col">
            <a href="{{ route('likes') }}" class="btn @if (request()->routeIs('likes')) active @endif">
                <div class="icon"><i class="fa-solid fa-heart fa-fw"></i></div>
                <div>{{ __('Likes') }}</div>
            </a>
        </div>
        <div class="col">
            <a href="{{ route('messages', ['type' => 'all']) }}"
                class="btn @if (request()->routeIs(['messages', 'conversation'])) active @endif">
                <div class="icon"><i class="fa-solid fa-comment-dots fa-fw"></i></div>
                <div>{{ __('Messages') }}</div>
                @if ($messageCount > 0)
                    <span
                        class="badge badge-primary float-end new-messages">{{ $messageCount > 99 ? '99+' : $messageCount }}</span>
                @endif
            </a>
        </div>
        @if ($enable_banners)
            <div class="col">
                <a class="btn external" target="_blank"
                    href="https://a.medfoodsafety.com/loader?a=4794318&s=4781094&t=101&p=4800">
                    <div class="icon"><i class="fas fa-video"></i></div>
                    <div>{{ __('Cams') }}</div>
                </a>
            </div>
            <div class="col">
                <a class="btn external" target="_blank"
                    href="https://a.medfoodsafety.com/loader?a=4794238&s=4781094&t=94&p=4800">
                    <div class="icon"><i class="fas fa-heart fa-fw"></i></div>
                    <div>{{ __('Dating') }}</div>
                </a>
            </div>
        @endif
    </div>
</div>
