@extends('theme.'. $domain->theme .'.layouts.master')
@section('content')

<div class="content">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-7">
                <div class="pay-method">
                    <div class="failed-message">
                        <div class="fpay-icon p-0">
                            <img src="{{ cdn($domain) }}/images/verification-image.jpg" class="rounded-circle"/>
                        </div>
                        <h2>
                            {{ __('Last step...you\'re almost there') }}
                        </h2>
                        <p class="mb-4">
                            {{ __('Complete your registration and gain free access to') }} <img src="{{ cdn($domain) }}/images/{{ strtolower($domain->name) }}.png" class="logo-small"/>
                        </p>
                    </div>
                    <div class="all-password" style="padding-top:0px!important;">
                        <form id="verify-registration" class="recaptcha-form" method="POST" action="{{ route('verify') }}">
                            @csrf
                            <div class="row form-group">
                                <div class="col-sm-4 col-md-5 col-lg-3 col-xl-4">
                                    <label class="form-label">{{ __('E-mail') }}</label>
                                </div>
                                <div class="col-sm-8 col-md-7 col-lg-9 col-xl-8">
                                    <input value="{{ old('email', $user->email) }}" name="email" id="email" class="form-control" type="email" required/>
                                </div>
                            </div>
                            <div class="row form-group">
                                <div class="col-sm-4 col-md-5 col-lg-3 col-xl-4">
                                    <label class="form-label">{{ __('Your date of birth') }}</label>
                                </div>
                                <div class="col-sm-8 col-md-7 col-lg-9 col-xl-8">
                                    <div class="age-selector m-0">
                                        @include('theme.' . $domain->theme . '.partials.age-selector')
                                    </div>
                                </div>
                            </div>
                            <div class="row form-group">
                                <div class="col-sm-4 col-md-5 col-lg-3 col-xl-4">
                                    <label class="form-label">{{ __('City of residence') }}</label>
                                </div>
                                <div class="col-sm-8 col-md-7 col-lg-9 col-xl-8">
                                    <div class="input position-relative">
                                        <input type="text" name="cityAutocomplete" value="" class="ui-autocomplete-input match_location_input form-control" id="cityAutocomplete" autocomplete="off">
                                        <input type="hidden" name="city_id" value="" id="cityInput" data-field="city_id">
                                        <div id="results-container" class="d-none">
                                            <ul class="results"></ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 city-error mt-3 d-none">
                                    <div class="alert alert-danger" role="alert">
                                        {{ __('Please select a city from the drop down menu.') }}
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-4 justify-content-end">
                                <div class="col-sm-8 col-md-7 col-lg-9 col-xl-8">
                                    <button type="submit" class="btn w-100 btn-green text-uppercase">{{ __('Verify Now!') }}</button>
                                </div>
                            </div>
                            <div class="row text-center mt-4">
                                <div class="col-6">
                                    <small class="fw-bold mt-5 mb-2 d-block">{{ __('Voted as the best adult dating contact site in 2023') }}</small>
                                    <img src="{{ cdn($domain) }}/images/stars.png" class="logo-stars"/>
                                </div>
                                <div class="col-6">
                                    <small class="fw-bold mt-5 mb-2 d-block">{{ __('We proudly work with') }}</small>
                                    <div class="row row-cols-3 row-cols-md-4 g-0 px-2 justify-content-center">
                                        <div class="col"><img src="{{ asset('global/img/cc/visa.png') }}" alt="Visa"/></div>
                                        <div class="col"><img src="{{ asset('global/img/cc/mastercard.png') }}" alt="Mastercard" /></div>
                                        @if($domain->country->code == 'US')
                                        <div class="col"><img src="{{ asset('global/img/cc/apple_pay.png') }}" alt="Apple Pay" /></div>
                                        <div class="col"><img src="{{ asset('global/img/cc/google_pay.png') }}" alt="Google Pay" /></div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
    $('#verify-registration').on('submit', function(ev) {
        if ($('#cityInput').val() == '') {
            ev.preventDefault();
            $('.city-error').removeClass('d-none');
        }
    });
</script>
@endpush
