@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
<!-- content start -->
<div class="content mt-5 mt-md-5">
    <div class="active-block mt-5 mt-md-0">
        <div class="container">
            <div class="row row-cols-1 text-center justify-content-center">
                <div class="col-auto position-relative">
                    <div class="steps-wrapper d-flex justify-content-center align-items-center">
                        <div class="step active position-relative">
                            1
                            <div class="stepInfo position-absolute">{{ __('Your details') }}</div>
                        </div>
                        <div class="step-divider mx-2"></div>
                        <div class="step active position-relative">
                            2
                            <div class="stepInfo position-absolute">{{ __('Your preferences') }}</div>
                        </div>
                        <div class="step-divider mx-2"></div>
                        <div class="step position-relative">
                            3
                            <div class="stepInfo position-absolute">{{ __('Verify email') }}</div>
                        </div>
                    </div>
                    <div class="row chatter-wrapper">
                        <div class="col-auto position-relative">
                            <div class="chatter-message">
                                {{ __('I\'m waiting for you...') }}
                                <i class="bi bi-play-fill"></i>
                            </div>
                            <div class="position-relative d-inline-block">
                                <img src="{{ cdn($domain) }}/images/olivia_02.png" class="chatter-img">
                                <div class="chatter-online position-absolute"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-2 mb-4">
                    <h2 class="mb-4">{!! __('Hey you... almost there! Please verify<br>your email') !!}</h2>
                    <p>
                        {!! __('You received an email to <strong>:email</strong><br>Click the link to verify your account.', ['email' => $user->email]) !!}
                    </p>
                </div>
                <div class="col-12 btn-container mb-5">
                    @if ($inboxImage)
                    <div class="col-12 mb-5">
                        <a class="btn btn-primary btn-signup px-4" href="{{ $inboxLink }}" target="_blank">
                            <span>{!! __('Open email inbox') !!}</span>
                        </a>
                    </div>
                    @endif
                </div>
                <div class="col-12 sub-btns mb-4">
                    <h2>{{ __('Can\'t find email?') }}</h2>
                    <p class="activation-info">{!! __('First <a data-bs-toggle="modal" data-bs-target="#email-in-spam-modal" class="spam">check your spam folder</a> if it\'s not in your inbox.') !!}</p>
                    <div class="row justify-content-center g-2 mb-2">
                        <div class="col-12 col-md-6">
                            <button id="update-email" class="btn btn-secondary px-4 w-100" data-bs-toggle="modal" data-bs-target="#update-email-modal">
                                {!! __('Update email') !!}
                            </button>
                        </div>
                        <div class="col-12 col-md-6">
                            <form id="resend-email" class="verification d-none" action="{{ route('resendActivation') }}" method="POST">
                                @csrf
                            </form>
                            <button type="submit" id="submit-resend" class="btn btn-secondary px-4 w-100">
                                {!! __('Resend email') !!}
                            </button>
                        </div>
                    </div>
                    @if(session()->has('success'))
                    <p class="activation-info-small">
                        {!! __('Note, it may take up to <strong>2 minutes</strong> for the verification email to arrive in your inbox.') !!}
                    </p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@include('theme.' . $domain->theme . '.popup.update-email')
@include('theme.' . $domain->theme . '.popup.email-in-spam')
<!-- content end -->
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        $('#submit-resend').on('click', function(ev) {
            ev.preventDefault();
            var ts = Math.round((new Date()).getTime() / 1000);
            var resend = sessionStorage.getItem('resend-email');

            if (resend !== null && (ts - resend) < 300) {
                toastr.error(
                    '{{ __('Please wait 5 minutes before trying to resend the activation email again') }}'
                );
                return false;
            }
            sessionStorage.setItem('resend-email', ts);
            $('#resend-email').submit();
        });

        Livewire.on('email-updated', () => {
            setTimeout(function() {
                $('#update-email-modal').modal('hide');
                window.location.reload();
            }, 2000);
        });
    });

</script>
@endpush

