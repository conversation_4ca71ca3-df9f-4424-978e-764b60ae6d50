@extends('theme.' . $domain->theme . '.layouts.master')
@section('content')
    <div class="content">
        <div class="container">
            <div class="pay-method">
                <div class="row failed-message">
                    <div class="col-12 fpay-icon">
                        <img src="{{ cdn($domain) }}/images/pay-fail.png" alt="pay-fail" class="bundal-round">
                    </div>
                    <div class="col-12">
                        <h2>{{ __('Payment Failed') }}</h2>
                        <p class="mb-4">{{ __('Oops...something went wrong. Are you experiencing payment issues and do you need help?') }}</p>
                    </div>
                </div>
                <div class="row mt-2 g-1 justify-content-center">
                    @if(isset($order) && $order !== null && $paymentMethod !== null)
                    <div class="col-md-auto">
                        @if ($paymentMethod->type->name === 'credit_card_hosted')
                            <button class="btn btn-action" type="button" class="retry" onclick="window.location.href='{{ route($order->isForDefaultProduct() ? 'creditsOffer' : 'premiumOffer', ['offer_id' => $order->product_id]) }}'">
                                @if($domain->country->code == 'US')
                                    <span>{{ __('Retry payment with :payment_method', ['payment_method' => $paymentMethod->type->title]) }}</span>
                                @else
                                    <span>{{ __('Retry payment') }}</span>
                                @endif
                            </button>
                        @else
                            <form action="{{ $order->getRetryRoute() }}" method="post" id="transaction">
                                @csrf
                                <input type="hidden" name="payment_method_id" value="{{ $paymentMethod->id }}"
                                    id="{{ $paymentMethod->id }}"
                                    data-payment_type="{{ $paymentMethod->type->name }}"
                                    data-payment_provider="{{ $paymentMethod->provider->name }}"/>
                                <input type="hidden" name="product_id" value="{{ $order->product_id }}"/>
                                <button type="button" class="btn btn-action retry buy-button">
                                    @if($domain->country->code == 'US')
                                        <span>{{ __('Retry payment with :payment_method', ['payment_method' => $paymentMethod->type->title]) }}</span>
                                    @else
                                        <span>{{ __('Retry payment') }}</span>
                                    @endif
                                </button>
                            </form>
                        @endif
                    </div>
                    <div class="col-md-auto">
                        <a class="btn btn-primary" href="{{ $order->getProductPageRoute() }}">
                            <span>{{ __('Pick another package') }}</span>
                        </a>
                    </div>
                @endif
                    <div class="col-md-auto">
                        <a class="btn btn-primary" href="{{ route('cmsContact') }}">
                            <span>{{ __('Contact us') }}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('scripts')
    @include('global.payment.scripts.payment-scripts')
    @if(isset($order) && $order !== null && $paymentMethod !== null)
        @if (View::exists('global.payment.scripts.' . $paymentMethod->type->name))
            @include('global.payment.scripts.' . $paymentMethod->type->name)
        @endif
    @endif
@endpush
