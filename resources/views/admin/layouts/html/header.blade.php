<div class="navbar navbar-inverse navbar-fixed-top" role="navigation">
  <div class="container">
    <div class="navbar-header">
      <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
        <span class="sr-only">Toggle navigation</span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
      </button>
      <a class="navbar-brand" href="{{ action([\App\Http\Controllers\DashboardController::class, 'index']) }}">Admin</a>
    </div>
    <div class="collapse navbar-collapse">
      <ul class="nav navbar-nav">
        @if(Auth::user()->role == 'admin' || Auth::user()->role == 'stats')
          <li{{ (isset($menuHome)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\DashboardController::class, 'index']) }}">Dashboard</a>
          </li>
        @endif
        @if(Auth::user()->role == 'profielbeheer_planner')
          <li>
            <a href="{{ action([\App\Http\Controllers\PlannerController::class, 'create']) }}">Create custom poke</a>
          </li>
        @endif
        @if((Auth::user()->role == 'profielbeheer_planner' && Auth::user()->alias == 'intern') || Auth::user()->role == 'schedule_planner')
          <li{{ (isset($menuSchedulePlanner)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\PlannerController::class, 'nextInSchedule']) }}">Schedule new poke</a>
          </li>
        @endif
        @if(Auth::user()->role == 'admin')
          <li{{ (isset($menuSchedule)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\ScheduleController::class, 'index']) }}">Schedules</a>
          </li>
        @endif
        @if(Auth::user()->role == 'admin')
            <li{{ (isset($menuDefaultMessage)) ? ' class="active"' : '' }}>
                <a href="{{ action([\App\Http\Controllers\DefaultMessageController::class, 'index']) }}">Default messages</a>
            </li>
        @endif
        @if(Auth::user()->role == 'admin' || Auth::user()->role == 'profielbeheer_planner' || Auth::user()->role == 'planner_supervisor' || Auth::user()->role == 'planner_coach' || Auth::user()->role == 'schedule_planner' || Auth::user()->role == 'qualitycontrol')
          <li{{ (isset($menuPlanner)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\PlannerController::class, 'index']) }}">Planner</a>
          </li>
        @endif
        @if(Auth::user()->role == 'admin' || Auth::user()->role == 'profielbeheer' || Auth::user()->role == 'profielbeheer_planner' || Auth::user()->role == 'profile_creator' || Auth::user()->role == 'planner_supervisor' || Auth::user()->role == 'planner_coach' || Auth::user()->role == 'qualitycontrol' || Auth::user()->role == 'schedule_planner' || Auth::user()->role == 'admin_basic')
          <li{{ (isset($menuProfiles)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\ProfileController::class, 'index']) }}">Profiles</a>
          </li>
        @endif
        @if(Auth::user()->role == 'admin' || Auth::user()->role == 'customerservice' || Auth::user()->role == 'admin_basic')
          <li{{ (isset($menuMembers)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\MemberController::class, 'index']) }}">{{ __('Members') }}</a>
          </li>
        @endif
        @if(Auth::user()->role == 'admin')
          <li{{ (isset($menuApprove)) ? ' class="active"' : '' }}>
            <a href="{{ route('getApprove') }}">Approval
              <?php $approveCount = DB::table('user_approval_queue')->distinct()->count('user_id'); ?>
              @if($approveCount > 0)
                <span class="label label-danger" style="color: #fff;">{{ $approveCount }}</span>
              @endif
            </a>
          </li>
        @endif
        @if(Auth::user()->role == 'approval')
          <li class="dropdown {{ (isset($menuApprove)) ? 'active' : '' }}">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Approval
                <?php $approveCount = DB::table('user_approval_queue')->distinct()->count('user_id'); ?>
                @if($approveCount > 0)
                  <span class="label label-danger" style="color: #fff;">{{ $approveCount }}</span>
                @endif
                <span class="caret"></span>
            </a>
            <ul class="dropdown-menu">
                <li><a href="{{ route('getApprove') }}">All approvals</a></li>
                <li><a href="{{ route('getApprove', ['gender' => 'female']) }}">Female approvals</a></li>
            </ul>
          </li>
        @endif
        @if(Auth::user()->role == 'admin' || Auth::user()->role == 'approval' || Auth::user()->role == 'qualitycontrol')
          <li{{ (isset($menuBlacklist)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\BlacklistController::class, 'index']) }}">Blacklist
              <?php $blacklistCount = Blacklist::count(); ?>
              @if($blacklistCount > 0)
                <span class="label label-danger" style="color: #fff;">{{ $blacklistCount }}</span>
              @endif
            </a>
          </li>
        @endif
        @if(Auth::user()->role == 'admin' || Auth::user()->role == 'reported_messages' || Auth::user()->role == 'admin_basic')
            <li{{ (isset($menuReportMessage)) ? ' class="active"' : '' }}>
                <a href="{{ route('reportedMessages') }}">
                    Reported messages
					<?php $reportedMessagesQueueCount = DB::table('messages_reported')->where('processed', false)->count(); ?>
                    @if($reportedMessagesQueueCount > 0)
                        <span class="label label-danger" style="color: #fff;">{{ $reportedMessagesQueueCount }}</span>
                    @endif
                </a>
            </li>
        @endif
        @if(Auth::user()->role == 'admin' || (Auth::user()->role == 'profielbeheer_planner' && Auth::user()->id == 23) || Auth::user()->role == 'admin_basic')
          <li class="dropdown {{ (isset($menuOperator)) ? 'active' : '' }}">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Operators <span class="caret"></span></a>
            <ul class="dropdown-menu">
                <li><a href="{{ action([\App\Http\Controllers\OperatorController::class, 'index']) }}">All operators</a></li>
                <li><a href="{{ route('onlineOperators') }}">Online operators</a></li>
                <li><a href="{{ route('showBlockedChatters') }}">Blocked operators</a></li>
                <li><a href="{{ route('createOperatorAlert') }}">Send alert</a></li>
            </ul>
          </li>
        @elseif(Auth::user()->role == 'qualitycontrol')
            <li{{ (isset($menuOperator)) ? ' class="active"' : '' }}>
              <a href="{{ action([\App\Http\Controllers\OperatorController::class, 'index']) }}">Operators</a>
            </li>
        @endif
        {{--
        @if(Auth::user()->role == 'admin')
          <li{{ (isset($whitelabels)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\WhitelabelController::class, 'index']) }}">Whitelabels</a>
          </li>
        @endif
        --}}
        {{--
        @if(Auth::user()->role == 'admin')
          <li{{ (isset($menuPayments)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\PayoutController::class, 'index']) }}">Payments</a>
          </li>
        @endif
        --}}
        @if(Auth::user()->role == 'admin')
          <li{{ (isset($menuQueue)) ? ' class="active"' : '' }}>
            <a href="{{ route('lastMessages') }}">Queue
              <?php $queueCount = DB::table('messages_queue')->where('read', 0)->count('message_id'); ?>
              @if($queueCount > 0)
                <span class="label label-danger" style="color: #fff;">{{ $queueCount }}</span>
              @endif
            </a>
          </li>
        @endif
        @if(Auth::user()->role == 'admin' || Auth::user()->role == 'stats_affiliates' || Auth::user()->role == 'affiliate_manager')
          <li{{ (isset($menuAffiliates)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\AffiliateController::class, 'index']) }}">{{ __('Affiliates') }}</a>
          </li>
        @endif
        @if(Auth::user()->role == 'admin' || Auth::user()->role == 'customerservice' || Auth::user()->role == 'admin_basic')
          <li{{ (isset($menuOrders)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\OrderController::class, 'index']) }}">Orders</a>
          </li>
        @endif
        @if(Auth::user()->role == 'admin' || Auth::user()->role == 'stats' || Auth::user()->role == 'profielbeheer_planner' || Auth::user()->role == 'planner_supervisor' || Auth::user()->role == 'stats_affiliates' || Auth::user()->role == 'planner_coach' || Auth::user()->role == 'supervisor' || Auth::user()->role == 'schedule_planner' || Auth::user()->id == 109 || Auth::user()->role == 'qualitycontrol')
          <li class="dropdown {{ (isset($stats)) ? 'active' : '' }}">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Statistics <span class="caret"></span></a>
            <ul class="dropdown-menu">
                @if(Auth::user()->role == 'admin' || Auth::user()->role == 'stats' || Auth::user()->role == 'stats_affiliates' || Auth::user()->role == 'supervisor')
                    <li><a href="/statistics">All statistics</a></li>
                @endif
                @if(Auth::user()->role == 'admin' || Auth::user()->role == 'stats' || Auth::user()->role == 'stats_affiliates' || Auth::user()->role == 'supervisor')
                    <li><a href="/statistics/affiliates">Affiliate stats</a></li>
                @endif
                @if(Auth::user()->role == 'admin' || Auth::user()->role == 'stats')
                    <li><a href="/statistics/chat">Operator stats</a></li>
                @endif
                @if(Auth::user()->role == 'admin')
                    <li><a href="{{ route('operatorChatterStats') }}">Chat report stats</a></li>
                @endif
                @if(Auth::user()->role == 'admin' || Auth::user()->role == 'stats' || Auth::user()->role == 'stats_affiliates' || Auth::user()->role == 'supervisor')
                    <li><a href="/statistics/landing">Landing stats</a></li>
                @endif
                @if(Auth::user()->role == 'admin' || Auth::user()->role == 'stats' || Auth::user()->role == 'profielbeheer_planner' || Auth::user()->role == 'planner_supervisor' || Auth::user()->role == 'planner_coach' || Auth::user()->role == 'supervisor' || Auth::user()->role == 'schedule_planner')
                    <li><a href="/statistics/pokers">Poker stats</a></li>
                @endif
                @if(Auth::user()->role == 'admin' || Auth::user()->role == 'stats' || Auth::user()->role == 'supervisor' || Auth::user()->role == 'planner_supervisor' || Auth::user()->id == 13 || Auth::user()->id == 109 || Auth::user()->role == 'qualitycontrol' || (Auth::user()->role == 'profielbeheer_planner' && Auth::user()->alias == 'intern') || (Auth::user()->role == 'schedule_planner' && Auth::user()->alias == 'intern'))
                    <li><a href="/statistics/pokes">Top pokes</a></li>
                @endif
                @if(Auth::user()->role == 'admin')
                    <li><a href="{{ route('statsFakeProfiles') }}">Profiles stats</a></li>
                @endif
            </ul>
          </li>
        @elseif(Auth::user()->role == 'stats_affiliate' || Auth::user()->role == 'affiliate_manager')
          <li{{ (isset($stats)) ? ' class="active"' : '' }}>
            <a href="/affiliate_stats">Statistics</a>
          </li>
        @elseif(Auth::user()->role == 'stats_signups')
          <li{{ (isset($stats)) ? ' class="active"' : '' }}>
            <a href="/statistics">Statistics</a>
          </li>
          <li{{ (isset($menuMembers)) ? ' class="active"' : '' }}>
            <a href="{{ route('disableEmailOfMembersForm') }}">Spamtraps</a>
          </li>
        @endif
        @if(Auth::user()->role == 'schedule_planner')
            <li{{ (isset($menuScheduleHelp)) ? ' class="active"' : '' }}>
              <a href="{{ action([\App\Http\Controllers\ScheduleController::class, 'showHelp']) }}">Help</a>
            </li>
        @endif
        {{--
        @if(Auth::user()->role == 'admin' || Auth::user()->role == 'profielbeheer_planner')
          <li{{ (isset($menuTranslate)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\TranslationController::class, 'index']) }}">Pokes</a>
          </li>
        @endif
        --}}
        @if(Auth::user()->role == 'admin' || Auth::user()->role == 'banners')
          <li{{ (isset($menuBanners)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\BannerController::class, 'index']) }}">Banners</a>
          </li>
        @endif
        @if(Auth::user()->role == 'admin')
          <li{{ (isset($menuUsers)) ? ' class="active"' : '' }}>
            <a href="{{ action([\App\Http\Controllers\DashboardController::class, 'showUsers']) }}">Users</a>
          </li>
        @endif
        @if(Auth::user()->role === 'admin' )
            <li{{ (isset($menuDataList)) ? ' class="active"' : '' }}>
                <a href="{{ action([\App\Http\Controllers\DashboardController::class, 'showDataList']) }}">List data</a>
            </li>
        @endif
        @if(Auth::user()->role == 'stats_affiliates')
            <li{{ (isset($menuMembers)) ? ' class="active"' : '' }}>
              <a href="{{ action([\App\Http\Controllers\MemberController::class, 'exportLeads']) }}">Export leads</a>
            </li>
            <li{{ (isset($menuMembers)) ? ' class="active"' : '' }}>
              <a href="{{ action([\App\Http\Controllers\MemberController::class, 'multiblockShow']) }}">Block leads</a>
            </li>
        @endif
        <li><a href="{{ route('logout') }}">{{ __('Logout') }}</a></li>
      </ul>
    </div><!--/.nav-collapse -->
  </div>
</div>
