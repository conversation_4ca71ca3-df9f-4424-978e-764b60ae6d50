<script>
    function mapFormDataToObject(event) {
        const formData = new FormData(event.target);

        const formDataObject = {};
        formData.forEach((value, key) => (formDataObject[key] = value));

        return formDataObject;
    }

    $("{{ $selector }}").submit(function (event) {
        event.preventDefault();
        grecaptcha.ready(function () {
            grecaptcha.execute('{{ $domain->recaptcha_site_key }}', {action: 'submit'}).then(function (token) {
                var form = $("{{ $selector }}")

                @if($ajax)
                const formDataObject = mapFormDataToObject(event);
                formDataObject['g-recaptcha-response'] = token

                $.post({
                    type: "POST",
                    url: event.target.action,
                    data: formDataObject,
                    success: function (data) {
                        form.removeAttr('disabled');

                        if (data.status === 'success') {
                            toastr.success(data.message);
                        } else {
                            toastr.error(data.message);
                        }
                    }
                })
                @else
                form.append('<input id="g-recaptcha-response" type="hidden" name="g-recaptcha-response" value="' + token + '" />');
                event.currentTarget.submit()

                @endif
            });
        });
    });
</script>
