<p>
  We appreciate that you like our service and we hope that you will keep enjoying it. All members who spend more then £ 200 on our platform must perform an extra authorization check.
</p>
<p>
  Please fill in the form below and upload all required documents. Our Risk & Compliance team will review all authorization forms within 2 hours. All information will be kept in accordance with the terms of our Privacy Policy.
</p>

@include('global.messages')
{{ Html::form()->route('verifyAccount')->acceptsFiles()->addClass(['form-inline', 'verify'])->open() }}
  <div class="form-group">
    {{ Html::label('Firstname', 'firstname')->addClass('col-sm-4 control-label') }}
    <div class="col-sm-8">
      {{ Html::text('firstname')->maxlength(100)->addClass('form-control') }}
    </div>
  </div>
  <div class="form-group">
    {{ Html::label('Lastname', 'lastname')->addClass('col-sm-4 control-label') }}
    <div class="col-sm-8">
      {{ Html::text('lastname')->maxlength(100)->addClass('form-control') }}
    </div>
  </div>
  <div class="form-group">
    {{ Html::label('Address', 'address')->addClass('col-sm-4 control-label') }}
    <div class="col-sm-8">
      {{ Html::text('address')->maxlength(100)->addClass('form-control') }}
    </div>
  </div>
  <div class="form-group">
    {{ Html::label('Zip code', 'zipcode')->addClass('col-sm-4 control-label') }}
    <div class="col-sm-8">
      {{ Html::text('zipcode')->maxlength(100)->addClass('form-control') }}
    </div>
  </div>
  <div class="form-group">
    {{ Html::label('Upload proof of address', 'file_address')->addClass('col-sm-4 control-label') }}
    <div class="col-sm-8">
      {{ Html::file('proof_of_address') }}
      <span class="help-block">*Max 2MB</span>
    </div>
  </div>
  <div class="form-group">
    {{ Html::label('Upload ID', 'file_id')->addClass('col-sm-4 control-label') }}
    <div class="col-sm-8">
      {{ Html::file('id') }}
      <span class="help-block">*Max 2MB</span>
    </div>
  </div>
  <div class="form-group submit">
    <button type='submit' class='btn btn-success btn-order-now submit'>Submit verification check</button>
  </div>
  <small>If you are having difficulties with uploading files or have any questions please mail us at support[at]{{ Config::get('app.name_url') }}</small>
{{ Html::form()->close() }}

@push('scripts')
  <script>
    $(function(){
      $('button.submit').click(function(){
        $(this).attr('disabled', true);
        $(this).html('Uploading...');
        $('form.verify').submit();
      });
    });
  </script>
@endpush
