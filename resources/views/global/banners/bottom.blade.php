@if ($enable_banners)
    <div class="container adban adban-bottom mb-3">
        <div class="row">
            @if ($browser->isMobile())
                <div class="col-12 d-md-none text-center">
                    <script async type="application/javascript" src="https://a.magsrv.com/ad-provider.js"></script><ins class="eas6a97888e10" data-zoneid="5499134" data-sub="{{ $affiliateSubId }}"></ins>
                    <script>
                        (AdProvider = window.AdProvider || []).push({
                            "serve": {}
                        });
                    </script>
                </div>
            @else
                <div class="col-12 d-none d-md-block text-center">
                    <script async type="application/javascript" src="https://a.magsrv.com/ad-provider.js"></script><ins class="eas6a97888e2" data-zoneid="5499756" data-sub="{{ $affiliateSubId }}"></ins>
                    <script>
                        (AdProvider = window.AdProvider || []).push({
                            "serve": {}
                        });
                    </script>
                    <script async type="application/javascript" src="https://a.magsrv.com/ad-provider.js"></script><ins class="eas6a97888e2" data-zoneid="5499758" data-sub="{{ $affiliateSubId }}"></ins>
                    <script>
                        (AdProvider = window.AdProvider || []).push({
                            "serve": {}
                        });
                    </script>
                    <script async type="application/javascript" src="https://a.magsrv.com/ad-provider.js"></script><ins class="eas6a97888e2" data-zoneid="5499760" data-sub="{{ $affiliateSubId }}"></ins>
                    <script>
                        (AdProvider = window.AdProvider || []).push({
                            "serve": {}
                        });
                    </script>
                </div>
            @endif
        </div>
    </div>
@endif
