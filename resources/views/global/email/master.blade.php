<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8">
    <meta name="robots" content="nofollow, noindex">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<title>{{ (isset($subject)) ? $subject : '' }}</title>
<style>
/* -------------------------------------
		GLOBAL
------------------------------------- */
* {
	margin: 0;
	padding: 0;
	font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
	font-size: 100%;
	line-height: 1.6;
}

img {
	max-width: 100%;
}

body {
	-webkit-font-smoothing: antialiased;
	-webkit-text-size-adjust: none;
	width: 100%!important;
	height: 100%;
	font-size: 14px;
}


/* -------------------------------------
		ELEMENTS
------------------------------------- */
a {
	color: #348eda;
}

.btn-primary {
	text-decoration: none;
	color: #000;
	background-color: #FF005A;
	border: solid #FF005A;
	border-width: 10px 20px;
	line-height: 2;
	font-weight: bold;
	margin-right: 10px;
	text-align: center;
	cursor: pointer;
	display: inline-block;
	border-radius: 25px;
}

.btn-secondary {
	text-decoration: none;
	color: #FFF;
	background-color: #aaa;
	border: solid #aaa;
	border-width: 10px 20px;
	line-height: 2;
	font-weight: bold;
	margin-right: 10px;
	text-align: center;
	cursor: pointer;
	display: inline-block;
	border-radius: 25px;
}

.last {
	margin-bottom: 0;
}

.first {
	margin-top: 0;
}

.padding {
	padding: 10px 0;
}


/* -------------------------------------
		BODY
------------------------------------- */
table.body-wrap {
	width: 100%;
	padding: 20px;
}

table.body-wrap .container {
	border: 1px solid #f0f0f0;
}


/* -------------------------------------
		FOOTER
------------------------------------- */
table.footer-wrap {
	width: 100%;
	clear: both!important;
}

.footer-wrap .container p {
	font-size: 12px;
	color: #666;

}

table.footer-wrap a {
	color: #999;
}


/* -------------------------------------
		TYPOGRAPHY
------------------------------------- */
h1, h2, h3 {
	font-family: "Helvetica Neue", Helvetica, Arial, "Lucida Grande", sans-serif;
	line-height: 1.1;
	margin-bottom: 15px;
	color: #000;
	margin: 0 0 10px;
	line-height: 1.2;
	font-weight: 200;
}

h1 {
	font-size: 36px;
}
h2 {
	font-size: 28px;
}
h3 {
	font-size: 22px;
}

p, ul, ol {
	margin-bottom: 10px;
	font-weight: normal;
	font-size: 14px;
}

ul li, ol li {
	margin-left: 5px;
	list-style-position: inside;
}

/* ---------------------------------------------------
		RESPONSIVENESS
		Nuke it from orbit. It's the only way to be sure.
------------------------------------------------------ */

/* Set a max-width, and make it display as block so it will automatically stretch to that width, but will also shrink down on a phone or something */
.container {
	display: block!important;
	max-width: 600px!important;
	margin: 0 auto!important; /* makes it centered */
	clear: both!important;
}

/* Set the padding on the td rather than the div for Outlook compatibility */
.body-wrap .container {
	padding: 20px;
}

/* This should also be a block element, so that it will fill 100% of the .container */
.content {
	max-width: 600px;
	margin: 0 auto;
	display: block;
}

/* Let's make sure tables in the content area are 100% wide */
.content table {
	width: 100%;
}

</style>
</head>

<body bgcolor="#f6f6f6">
<!-- body -->
<table class="body-wrap">
	<tr>
		<td></td>
		<td align="center" style="padding-bottom: 10px;">
            {!! \App\Helpers\Functions::appLogo($domain) !!}
		</td>
	</tr>
	<tr>
		<td></td>
		<td class="container" bgcolor="#FFFFFF">

			<div class="content">
			<table>
				<tr>
					<td style="-ms-word-break: break-all; word-break: break-all; word-break: break-word; -webkit-hyphens: auto; -moz-hyphens: auto; -ms-hyphens: auto; hyphens: auto;">
						@yield('content')
					</td>
				</tr>
			</table>
			</div>

		</td>
		<td></td>
	</tr>
</table>

<table class="footer-wrap">
	<tr>
		<td></td>
		<td class="container">

			<div class="content">
				<table>
                    @if(isset($to))
                        <tr>
                            <td align="center">
                                <?php $unsubscribe = $domain->full_url . '/support/unsubscribe?token=' . $user->getAutologinToken().''; ?>
                                <p style="font-size: 12px; color: #999;">
                                    You are receiving this e-mail, because you have signed up on {{ $domain->name_url }} with e-mail address <a href="#" style="box-sizing: border-box; color: #3869D4;text-decoration: underline;">{{ $to }}</a> | If you don't want to receive e-mails from us, please unsubscribe by clicking <a href="{{ $unsubscribe }}" style="box-sizing: border-box; color: #3869D4;text-decoration: underline;">here</a>. If you want to adjust your mailing settings, click <a href="{{ $unsubscribe }}" style="box-sizing: border-box; color: #3869D4;text-decoration: underline;">here</a>.
                                </p>

                            </td>
                        </tr>
                    @endif

                    <tr>
                        <td align="center">
                            <p style="font-size: 11px; color: #999;">
                                <a href="{{ $domain->full_url }}/about" style="color: #3869D4;">{{ __('About us') }}</a> |
                                <a href="{{ $domain->full_url }}/privacy" style="color: #3869D4;">{{ __('Privacy Policy') }}</a> |
                                <a href="{{ $domain->full_url }}/agreement" style="color: #3869D4;">{{ __('Terms & Conditions') }}</a> |
                                <a href="{{ $domain->full_url }}/disclaimer" style="color: #3869D4;">{{ __('Disclaimer') }}</a> |
                                <a href="{{ $domain->full_url }}/contact" style="color: #3869D4;">{{ __('Customer service') }}</a>
                            </p>
                        </td>
                    </tr>

				</table>
			</div>
		</td>
		<td></td>
	</tr>
</table>

</body>
</html>
