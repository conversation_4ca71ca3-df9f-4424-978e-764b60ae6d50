<div class="lander_description">
    <h5 class="text-center">{{ __('Only for men: FREE MEMBERSHIP. Register now!') }}</h5>
    <div class="text-center">{{ __('Time remaining') }}: <span class="timer">8:00</span></div>
    <div class="text-center">{{ __('Warning: you might find nude pictures of someone you know on this page') }}:</div>
</div>
@push('child-scripts')
    <script>
        /* timer */
        var timer2 = "8:00";
        var interval = setInterval(function() {
            var timer = timer2.split(':');
            //by parsing integer, I avoid all extra string processing
            var minutes = parseInt(timer[0], 10);
            var seconds = parseInt(timer[1], 10);
            --seconds;
            minutes = (seconds < 0) ? --minutes : minutes;
            if (minutes < 0) clearInterval(interval);
            seconds = (seconds < 0) ? 59 : seconds;
            seconds = (seconds < 10) ? '0' + seconds : seconds;
            //minutes = (minutes < 10) ?  minutes : minutes;
            $('.timer').html(minutes + ':' + seconds);
            timer2 = minutes + ':' + seconds;
        }, 1000);
    </script>
@endpush