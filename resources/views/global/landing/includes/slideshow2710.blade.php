<div id="slideshow" class="background-images">
    <div class="slide">
        <img class="image-right" src="{{ $domain->cdn_url_landing }}/global/img/landing2710/a1.png"/>
    </div>
    <div class="slide">
        <img class="image-right" src="{{ $domain->cdn_url_landing }}/global/img/landing2710/a2.png"/>
    </div>
    <div class="slide">
        <img class="image-right" style="right: 0%;" src="{{ $domain->cdn_url_landing }}/global/img/landing2710/a3.png"/>
    </div>
    <div class="slide">
        <img class="image-right" src="{{ $domain->cdn_url_landing }}/global/img/landing2710/a4.png"/>
    </div>
    <div class="slide">
        <img class="image-right" src="{{ $domain->cdn_url_landing }}/global/img/landing2710/a7.png"/>
    </div>
    <div class="slide">
        <img class="image-left" src="{{ $domain->cdn_url_landing }}/global/img/landing2710/a6.png"/>
    </div>
    <div class="slide">
        <img class="image-right" style="right: 0%; bottom:-20%;"   src="{{ $domain->cdn_url_landing }}/global/img/landing2710/a5.png"/>
    </div>
    <div class="slide">
        <img class="image-right" src="{{ $domain->cdn_url_landing }}/global/img/landing2710/a8.png"/>
    </div>
    <div class="slide">
        <img class="image-right" src="{{ $domain->cdn_url_landing }}/global/img/landing2710/a11.png"/>
    </div>
    <div class="slide">
        <img class="image-left" style="left:0px; bottom:0px;" src="{{ $domain->cdn_url_landing }}/global/img/landing2710/a10.png"/>
    </div>
    <div class="slide">
        <img class="image-left" style="left:0px; bottom:0px;"  src="{{ $domain->cdn_url_landing }}/global/img/landing2710/a9.png"/>
    </div>
</div>

@if(!$errors->any())
    @push('child-scripts')
    <script>
        $("#slideshow > .slide:gt(0)").hide();
        $('body').on('afterShowStep', function(e) {
            $('#slideshow > .slide:first')
            .fadeOut(800)
            .next()
            .fadeIn(800)
            .end()
            .appendTo('#slideshow');
        });
    </script>
    @endpush
@else
    @push('child-scripts')
    <script>
        $("#slideshow > .slide:gt(0)").hide();
        setInterval(function() {
            $('#slideshow > .slide:first')
            .fadeOut(2000)
            .next()
            .fadeIn(2000)
            .end()
            .appendTo('#slideshow');
        }, 6000);
    </script>
    @endpush
@endif

@push('child-styles')
<style>
    .slideshow{
        background: linear-gradient(to bottom left, #ffe2ef 0%, #ffe2ef 50%, #e79dc1 100%);
    }
    .background-images {
        position:absolute;
        width: 100%;
        height: 100%;
        top: 0px;
        left: 0px;
        overflow: hidden;
    }
    .slide {
        position: absolute;
        top: 25px;
        bottom: 0;
        left: 0;
        right: 0;
        overflow: hidden;
        pointer-events: none;
        z-index: 100;
    }
    .image-left,
    .image-right {
        position: absolute;
        width: auto;
        position: absolute;
        display: block;
        min-height: 95%;
        /* top: 2.5%; */
        height: calc(150% - 40px);
    }
    .image-left {
        left: 10px;
    }
    .image-right {
        right: 10%;
    }
</style>
@endpush