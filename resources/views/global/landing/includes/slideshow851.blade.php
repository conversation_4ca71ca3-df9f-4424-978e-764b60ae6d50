<div id="slideshow" class="background-images">
    <div class="slide">
        <img class="image-left" src="{{ $domain->cdn_url_landing }}/global/img/landing851/1a.png"/>
        <img class="image-right" src="{{ $domain->cdn_url_landing }}/global/img/landing851/1b.png"/>
    </div>
    <div class="slide">
        <img class="image-left" src="{{ $domain->cdn_url_landing }}/global/img/landing851/2a.png"/>
        <img class="image-right" src="{{ $domain->cdn_url_landing }}/global/img/landing851/2b.png"/>
    </div>
    <div class="slide">
        <img class="image-left" src="{{ $domain->cdn_url_landing }}/global/img/landing851/3a.png"/>
        <img class="image-right" src="{{ $domain->cdn_url_landing }}/global/img/landing851/3b.png"/>
    </div>
</div>

@push('child-scripts')
<script>
    $("#slideshow > .slide:gt(0)").hide();
    setInterval(function() {
        $('#slideshow > .slide:first')
            .fadeOut(1200)
            .next()
            .fadeIn(1200)
            .end()
            .appendTo('#slideshow');
    }, 5000);
</script>
@endpush

@push('child-styles')
<style>
.background-images {
    position:fixed;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    overflow: hidden;
    background-color: #dcdae5;
    z-index: -1;
}

.slide {
    position: absolute;
    height:90%;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden;
    pointer-events: none;
    z-index: 100;
}

.image-left,
.image-right {
    position: absolute;
    width: auto;
    display: block;
    height: 100%;
    top: 2.5%;
}

.image-left {
    left: 5%;
}

.image-right {
    right: 5%;
}

@media (max-width:1200px) {
    .slide {
        z-index: 0;
        height:70%;
    }

    .image-left,
    .image-right {
        
    }
    .image-left {
        left: -100px; */
    }

    .image-right {
        right: -100px; */
    }
}

</style>
@endpush

