<div id="slideshow" class="background-images">
    <div class="slide">
        <img class="image-left" src="{{ $domain->cdn_url_landing }}/global/img/landing8/1.png"/>
        <img class="image-right" src="{{ $domain->cdn_url_landing }}/global/img/landing8/2.png"/>
    </div>
    <div class="slide">
        <img class="image-left" src="{{ $domain->cdn_url_landing }}/global/img/landing8/3.png"/>
        <img class="image-right" src="{{ $domain->cdn_url_landing }}/global/img/landing8/4.png"/>
    </div>
</div>

@push('child-scripts')
<script>
    $("#slideshow > .slide:gt(0)").hide();
    setInterval(function() {
        $('#slideshow > .slide:first')
            .fadeOut(1000)
            .next()
            .fadeIn(1000)
            .end()
            .appendTo('#slideshow');
    }, 3000);
</script>
@endpush

@push('child-styles')
<style>
.background-images {
    position:absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    overflow: hidden;
}

.slide {
    position: absolute;
    top: 25px;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden;
    pointer-events: none;
    z-index: 100;
}

.image-left,
.image-right {
    position: absolute;
    width: auto;
    display: block;
    min-height: 150%;
    top: 2.5%;
}

.image-left {
    left: 5%;
}

.image-right {
    right: 5%;
}

@media (max-width:900px) {
    .slide {
        z-index: 0;
    }

    .image-left,
    .image-right {
        position: absolute;
        min-height: 100%;
        position: absolute;
        display: block;
        min-height: 95%;
        top: 2.5%;
    }
    .image-left {
        left: -100px;
    }

    .image-right {
        right: -100px;
    }
}

</style>
@endpush

