<div class="background-video">
    <video class="video-blur" id="background-video-desktop" autoplay playsinline muted loop disablePictureInPicture controlsList="nodownload">
        <source src="{{ $domain->cdn_url_landing }}/global/img/landing24/video-desktop-4.mp4" type="video/mp4">
    </video>
    <video class="video-blur" id="background-video-mobile" autoplay playsinline muted loop disablePictureInPicture controlsList="nodownload">
        <source src="{{ $domain->cdn_url_landing }}/global/img/landing24/video-mobile-4.mp4" type="video/mp4">
    </video>
</div>
<div class="background-cover"></div>

@push('child-styles')
<style>
.background-video {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
    background-color:white;
    -moz-box-shadow: inset 0 0 80px 30px rgba(0,0,0,1);
    -webkit-box-shadow: inset 0 0 80px 30px rgba(0,0,0,1);
    box-shadow: inner 0 0 80px 30px rgba(0,0,0,1);
    z-index:-1;
}

.background-video video {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    min-width: 100%;
    min-height: 100%;
    overflow: hidden;
}

#background-video-desktop {
    display:block;
}

#background-video-mobile {
    display:none;
}

.video-blur {
    -webkit-filter: blur(12px)!important;
    -moz-filter: blur(12px)!important;
    -o-filter: blur(12px)!important;
    -ms-filter: blur(12px)!important;
    filter: blur(12px)!important;
    -webkit-transition: 1s -webkit-filter linear;
    -o-transition: 1s -o-filter linear;
    transition: 1s filter linear;
}

.background-cover {
    display: block;
    position: fixed;
    width: 100%;
    height: 100%;
    top:0px;
    left:0px;
    background-repeat: repeat;
    background-color: rgba(0, 0, 0, 0.08);
    -moz-box-shadow: inset 0 0 180px 150px rgba(0,0,0,0.9)!important;
    -webkit-box-shadow: inset 0 0 180px 150px rgba(0,0,0,0.9)!important;
    box-shadow: inner 0 0 180px 150px rgba(0,0,0,0.9)!important;
    opacity: 0.7;
}

@media (max-width:900px) {
    #background-video-desktop {
        display:none;
    }

    #background-video-mobile {
        display:block;
    }

    .background-cover{
        -moz-box-shadow: inset 0 0 180px 150px rgba(0,0,0,0.7)!important;
        -webkit-box-shadow: inset 0 0 180px 150px rgba(0,0,0,0.7)!important;
        box-shadow: inner 0 0 180px 150px rgba(0,0,0,0.7)!important;
        opacity: 0.3!important;
    }
}
</style>
@endpush