<div class="instructions">
    <div class="title-guide text-start fw-bold">
        {{ __('Sexting made easy!') }}
    </div>
    <div class="slideshow-instructions">
        <div class="slide">
            <img src="{{ $domain->cdn_url_landing }}/global/img/landing24/avatar-a-1.jpg" class="rounded-circle border border-white border-1 d-block">
            <div class="instruction-text text-start fst-italic">
                <span class="fw-bold text-decoration-underline fst-normal">{{ __('Step') }} 1:</span> {{ __('Register now for free!') }}
            </div>
        </div>
        <div class="slide">
            <img src="{{ $domain->cdn_url_landing }}/global/img/landing24/avatar-a-2.jpg" class="rounded-circle border border-white border-1 d-block">
            <div class="instruction-text text-start fst-italic">
                <span class="fw-bold text-decoration-underline fst-normal">{{ __('Step') }} 2:</span> {{ __('Browse through our members and find someone you like.') }}
            </div>
        </div>
        <div class="slide">
            <img src="{{ $domain->cdn_url_landing }}/global/img/landing24/avatar-a-3.jpg" class="rounded-circle border border-white border-1 d-block">
            <div class="instruction-text text-start fst-italic">
                <span class="fw-bold text-decoration-underline fst-normal">{{ __('Step') }} 3:</span> {{ __('If the feeling is mutual, start texting and/or sexting!') }}
            </div>
        </div>
    </div>
</div>
@push('child-scripts')
<script>
    $(".slideshow-instructions > div:gt(0)").hide();
    setInterval(function() {
        $('.slideshow-instructions > div:first')
            .fadeOut(1000)
            .next()
            .fadeIn(1000)
            .end()
            .appendTo('.slideshow-instructions');
    }, 3000);
</script>
@endpush

@push('child-styles')
<style>
    .slideshow-instructions {
        position:relative;
        height: 150px;
        width: 100%;
    }

    .slideshow-instructions > div {
        position:absolute;
        top:30px;
        left:0px;
        width: 100%;
    }

    .instructions {
        display: block;
        position: relative;
        width: 100%;
        padding-top: 40px;
    }

    .instructions img {
        width: 110px;
        height: 110px;
    }

    .instructions .title-guide {
        padding: 0px 8px 4px 4px;
        position: absolute;
        left: 115px;
        top: 70px;
    }

    .instruction-text {
        position: relative;
        bottom: 75px;
        left: 110px;
        font-size: 20px;
        font-weight: 600;
        padding: 8px 8px 8px 10px;
        border-top: 2px dotted #FFFFFF;
        color: #FFFFFF;
        width: calc(100% - 100px);
    }

    .instruction-text span {
        font-size: 22px;
        color: #aadadf;
    }
</style>
@endpush
