@extends('global.layouts.landing2')
@section('content')
    @include('global.landing.includes.progress')

    <div class="container">
        @include('global.landing.includes.landing-logo')
        @if ($landing->slideshow)
            @include('global.landing.includes.' . $landing->slideshow)
        @endif
        @if ($landing->video)
            @include('global.landing.includes.' . $landing->video)
        @endif
        @if ($landing->top_of_page)
            @include('global.landing.includes.' . $landing->top_of_page)
        @endif
        <div class="row justify-content-center py-3 py-md-5 position-relative landing-container">
            <div class="col landing-content">
                @if ($landing->before_content)
                    @include('global.landing.includes.' . $landing->before_content)
                @endif
                <form id="recaptcha-form-{{ $landing_id }}" method="POST" action="{{ route('postRegister') }}"
                    accept-charset="UTF-8" role="form">
                    @csrf
                    @if ($errors->hasBag('register'))
                        @include('global.landing.steps.step-error')
                    @else
                        @foreach ($landing->steps as $index => $step)
                            @if ($step == 'google' && !$google_signup)
                                @continue
                            @endif
                            @include('global.landing.steps.step-' . $step, [
                                'stepIndex' => $index,
                            ])
                        @endforeach
                    @endif
                    <input type="hidden" name="home" value="1" />
                    <input type="hidden" name="landing" value="{{ $landing_id }}" />
                    <input type="hidden" name="google_id" value="{{ old('google_id') }}" />
                    <input type="hidden" name="image_url" value="{{ old('image_url') }}" />
                </form>
            </div>
        </div>
    </div>
@endsection
@section('scripts')
    @if ($domain->recaptcha_secret)
        <x-forms.scripts.recaptcha selector="#recaptcha-form-{{ $landing_id }}" />
    @endif
@endsection
