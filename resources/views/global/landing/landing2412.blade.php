@extends('global.layouts.landing')
@section('styles')
    <link rel="stylesheet" href="{{ $domain->cdn_url_landing }}/global/css/landing/landing24.css">
    <style>
        .background-cover{
            background-image: url("{{ $domain->cdn_url_landing }}/global/img/landing24/grid.png");
        }
    </style>
@endsection
@section('content')
<!-- new code 2 -->
<div class="progress">
    <div class="progress-bar" role="progressbar" aria-valuenow="10" aria-valuemin="0" aria-valuemax="100" style="width:10%">
        <span class="progress-bar-percentage">20%</span>
    </div>
</div>

<div class="background-video">
    <video class="video-blur" id="background-video-desktop" autoplay playsinline muted loop disablePictureInPicture controlsList="nodownload">
        <source src="{{ $domain->cdn_url_landing }}/global/img/landing24/video-desktop.mp4" type="video/mp4">
    </video>
    <video class="video-blur" id="background-video-mobile" autoplay playsinline muted loop disablePictureInPicture controlsList="nodownload">
        <source src="{{ $domain->cdn_url_landing }}/global/img/landing24/video-mobile.mp4" type="video/mp4">
    </video>
</div>
<div class="background-cover"></div>

<form method="POST" action="{{ route('postRegister') }}" accept-charset="UTF-8" role="form" class="website-container">
@csrf
<div class="website-title">
    <img src="{{ $domain->theme_url }}/images/{{ strtolower($domain->name) }}-dark.png"/>
</div>

@if($errors->hasBag('register'))
    <div class="step step_0">
        <div class="website-text-1">
            {!! __('<span>Oops</span>, you made <span>a mistake</span> somewhere!') !!}
        </div>

        <div class="input-1">
            @if($errors->any()
            && $errors->setFormat(':message<br />'))
                <div class="alert alert-danger">
                    @foreach($errors->all() as $error)
                        {!! $error !!}
                    @endforeach
                </div>
            @endif
            <div class="error-title">{{ __('Username') }}</div>
            <input maxlength="15" placeholder="{{ __('Username') }}" id="username" class="form-control" name="username" type="text" value="{{ old('username') }}">
            <div class="error-title">{{ __('Password') }}</div>
            <input maxlength="100" placeholder="{{ __('Password') }}" id="password" name="password" type="password" value="" class="form-control">
            <div class="error-title">{{ __('E-mail') }}</div>
            <input type="text" name="email" value="{{ old('email') }}" id="email" placeholder="{{ __('E-mail (e.g. ..@gmail.com)') }}" maxlength="100" class="form-control" />
            <div style="clear:both"></div>
        </div>
        <label class="terms_and_conditions">
            <input name="terms" type="checkbox" value="1">
            {!! __('By signing up I certify that (1) I am at least 18 years old and have reached the age of majority in my jurisdiction to use this adult fantasychat, (2) have read and agree to the <a href="/agreement" target="_blank" rel="nofollow">Terms & Conditions</a>, and I understand how the Website will process my personal data in accordance with the <a href="/privacy" target="_blank" rel="nofollow">Privacy Statement</a>.') !!}
        </label>

        <input type="hidden" name="region_id" value="{{ old('region_id') }}">
        <input type="hidden" name="gender" required="" value="{{ old('gender') }}">
        <input type="hidden" name="looking_for_age_min" required="required" value="{{ old('looking_for_age_min') }}">
        <input type="hidden" name="looking_for_age_max" required="required" value="{{ old('looking_for_age_max') }}">
        <input type="hidden" name="seek" required="" value="{{ old('seek') }}">
        <input type="hidden" name="birth_day" value="{{ old('birth_day') }}" />
        <input type="hidden" name="birth_month" value="{{ old('birth_month') }}" />
        <input type="hidden" name="birth_year" value="{{ old('birth_year') }}" />
        <input type="hidden" name="marital_status" value="{{ old('marital_status') }}" />
        <input type="hidden" name="eye_color" value="{{ old('eye_color') }}" />
        <input type="hidden" name="hair_color" value="{{ old('hair_color') }}" />
        <input type="hidden" name="ethnicity" value="{{ old('ethnicity') }}" />
        <input type="hidden" name="body_type" value="{{ old('body_type') }}" />
        <input type="hidden" name="length" value="{{ old('length') }}" />
        @foreach(trans('preferences.' . $domain->theme) as $key => $value)
        @if(isset(old('interests')[$key]))
        <input type="hidden" name="interests[{{ $key }}]" value="1">
        @endif
        @endforeach
        <input type="hidden" name="home" value="1" />
        <input type="hidden" name="landing" value="2412" />
        <input type="hidden" name="sub_id" id="sub_id_input" />

        <div class="website-answer">
            <button class="button-type-1">
                {{ __('Start Now!') }} <i class="fas fa-chevron-right"></i>
            </button>
            <div style="clear:both"></div>
        </div>
        <div style="clear:both"></div>
    </div>

@else

    <div class="step step_0">
        <div class="website-text-1">
            {!! __('You need to be <span>18 years old</span> or older to continue. <span>Only proceed if you are.</span>') !!}
        </div>
        <div class="website-answer">
				<span class="button-type-1 button-small-text nextbutton">
					{{ __('Yes, I\'m 18+') }} <i class="fas fa-chevron-right"></i>
				</span>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_1">
        <div class="website-text-1">
            {!! __('These women want discreet <span>one-on-one</span> contact! <span>Want to find out more?</span>') !!}
        </div>
        <div class="website-answer">
            <span class="button-type-1 button-small-text nextbutton">
                {{ __('Yes, let me in!') }}<span class="countdown"></span> <i class="fas fa-chevron-right"></i>
            </span>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_2">
        <div class="website-text-1">
            {!! __('What is your <span>gender</span>?') !!}
        </div>
        <div class="step-looking-for">
            <div class="step-looking-for-left">
                <input type = "radio" name = "gender" id = "gender-male" value = "male" class="check-with-label" checked="checked">
                <label for="gender-male" class="btn-primary preferred-button nextbutton">{{ __('Male') }}</label>
                <div style="clear:both"></div>
            </div>

            <div class="step-looking-for-right">
                <input type = "radio" name = "gender" id = "gender-female" value = "female" class="check-with-label">
                <label for="gender-female" class="btn-primary nextbutton">{{ __('Female') }}</label>
                <div style="clear:both"></div>
            </div>
            <div style="clear:both"></div>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_3">
        <div class="website-text-1">
            {!! __('Have you ever been on a <span>sexdate</span>?') !!}
        </div>
        <div class="step-looking-for">
            <div class="step-looking-for-left">
                <span class="btn-primary nextbutton">{{ __('Yes') }}</span>
                <div style="clear:both"></div>
            </div>
            <div class="step-looking-for-right">
                <span class="btn-primary preferred-button nextbutton">{{ __('No') }}</span>
                <div style="clear:both"></div>
            </div>
            <div style="clear:both"></div>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_4">
        <div class="website-text-1">
            {!! __('Do you use a <span>condom</span>?') !!}
        </div>
        <div class="step-looking-for">
            <div class="step-looking-for-left">
                <span class="btn-primary preferred-button nextbutton">{{ __('Yes') }}</span>
                <div style="clear:both"></div>
            </div>
            <div class="step-looking-for-right">
                <span class="btn-primary nextbutton">{{ __('No') }}</span>
                <div style="clear:both"></div>
            </div>
            <div style="clear:both"></div>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_5">
        <div class="website-text-1">
            {!! __('Are you looking for <span>men or women</span>?') !!}
        </div>
        <div class="step-looking-for">
            <div class="step-looking-for-left">
                <input type = "radio" name = "seek" id = "seek-male" value = "male" class="check-with-label">
                <label for="seek-male" class="btn-primary nextbutton">{{ __('Men') }}</label>
                <div style="clear:both"></div>
            </div>
            <div class="step-looking-for-right">
                <input type = "radio" name = "seek" id = "seek-female" value = "female" class="check-with-label" checked="checked">
                <label for="seek-female" class="btn-primary preferred-button nextbutton">{{ __('Women') }}</label>
                <div style="clear:both"></div>
            </div>
            <div style="clear:both"></div>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_6">
        <div class="website-text-1">
            {!! __('What is the <span>ideal age you prefer</span>?') !!}
        </div>
        <div class="step-looking-for">
            <div class="step-looking-for-left">
                <span>{{ __('Between') }}:</span>
                <select class="form-control" name="looking_for_age_min" id="chosen_age_1">
                    @for($i = 18; $i <= 99; $i++)
                        <?php
                        $selected = "";
                        if ($i == 30) {
                            $selected = " selected='selected'";
                        }
                        ?>
                        <option value="{{ $i }}" {{ $selected }}>{{ $i }}</option>
                    @endfor
                </select>
                <div style="clear:both"></div>
            </div>
            <div class="step-looking-for-right">
                <span>{{ __('And') }}:</span>
                <select class="form-control" name="looking_for_age_max" id="chosen_age_2">
                    @for($i = 18; $i <= 99; $i++)
                        <?php
                        $selected = "";
                        if ($i == 60) {
                            $selected = " selected='selected'";
                        }
                        ?>
                        <option value="{{ $i }}" {{ $selected }}>{{ $i }}</option>
                    @endfor
                </select>
                <div style="clear:both"></div>
            </div>
            <div style="clear:both"></div>
        </div>
        <div class="website-answer">
            <span class="button-type-1 nextbutton">
                {{ __('Next Step') }} <span class="countdown"></span> <i class="fas fa-chevron-right"></i>
            </span>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_7">
        <div class="website-text-1">
            {{ __('What do you like? (multiple answers possible)') }}
        </div>
        <div class="interest-box">
                @foreach(trans('preferences.' . $domain->theme) as $key => $value)
                <input name="interests[{{ $key }}]" type="checkbox" value="1" class="check-with-label" id="{{ $key }}">
                <label class="label-for-checking" for="{{ $key }}">{{ __($value) }}</label>
                @endforeach
            <div style="clear:both"></div>
        </div>
        <div class="website-answer">
            <span class="button-type-1 nextbutton">
                {{ __('Next Step') }} <span class="countdown"></span> <i class="fas fa-chevron-right"></i>
            </span>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_8">
        <div class="website-text-1">
            {!! __('Are you in a <span>relationship</span>?') !!}
        </div>
        <div class="answer-box">
            @foreach(trans('lists.marital_status') as $key => $value)
            <input type="radio" class="check-with-label" id="{{ $key }}" name="marital_status" value="{{ $key }}">
            <label class="label-for-checking" for="{{ $key }}">{{ __($value) }}</label>
            @endforeach
        <div style="clear:both"></div>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_9">
        <div class="col-md-12 text-center m-fix">
            <div class="website-text-1">
                {!! __('What is the <span>color of your eyes</span>?') !!}
            </div>
            <div class="answer-box">
                @foreach(trans('lists.eye_colors') as $key => $value)
                <input type="radio" class="check-with-label" id="eye_color-{{ $key }}" name="eye_color" value="{{ $key }}">
                <label class="label-for-checking" for="eye_color-{{ $key }}">{{ __($value) }}</label>
                @endforeach
            <div style="clear:both"></div>
            </div>
            <div style="clear:both"></div>
        </div>
    </div>

    <div class="step step_10">
        <div class="col-md-12 text-center m-fix">
            <div class="website-text-1">
                {!! __('Which <span>hair color</span> do you have?') !!}
            </div>
            <div class="answer-box">
                @foreach(trans('lists.hair_colors') as $key => $value)
                <input type="radio" class="check-with-label" id="hair_color-{{ $key }}" name="hair_color" value="{{ $key }}">
                <label class="label-for-checking" for="hair_color-{{ $key }}">{{ __($value) }}</label>
                @endforeach
            <div style="clear:both"></div>
            </div>
            <div style="clear:both"></div>
        </div>
    </div>

    <div class="step step_11">
        <div class="col-md-12 text-center m-fix">
            <div class="website-text-1">
                {!! __('What is your <span>skin color / ethnicity</span>?') !!}
            </div>
            <div class="answer-box">
                @foreach(trans('lists.ethnicity') as $key => $value)
                <input type="radio" class="check-with-label" id="ethnicity-{{ $key }}" name="ethnicity" value="{{ $key }}">
                <label class="label-for-checking" for="ethnicity-{{ $key }}">{{ __($value) }}</label>
                @endforeach
            <div style="clear:both"></div>
            </div>
            <div style="clear:both"></div>
        </div>
    </div>

    <div class="step step_12">
        <div class="col-md-12 text-center m-fix">
            <div class="website-text-1">
                {!! __('What <span>posture</span> do you have?') !!}
            </div>
            <div class="answer-box">
                @foreach(trans('lists.body_types') as $key => $value)
                <input type="radio" class="check-with-label" id="body_type-{{ $key }}" name="body_type" value="{{ $key }}">
                <label class="label-for-checking" for="body_type-{{ $key }}">{{ __($value) }}</label>
                @endforeach
            <div style="clear:both"></div>
            </div>
            <div style="clear:both"></div>
        </div>
    </div>

    <div class="step step_13">
        <div class="col-md-12 text-center m-fix">
            <div class="website-text-1">
                {!! __('How <span>tall</span> are you?') !!}
            </div>
            <div class="answer-box">
                @foreach(trans('lists.heights_grouped_short') as $key => $value)
                <input type="radio" class="check-with-label" id="{{ $key }}" name="length" value="{{ $key }}">
                <label class="label-for-checking" for="{{ $key }}">{{ __($value) }}</label>
                @endforeach
            <div style="clear:both"></div>
            </div>
            <div style="clear:both"></div>
        </div>
    </div>

    <div class="step step_14">
        <div class="website-text-1">
            {!! __('Please select your <span>region</span> from the dropdown') !!}
        </div>
        <div class="input-1">
            <div class="alert alert-danger alert-extra">
                {{ __('Please select your region from the dropdown list') }}
            </div>
            <select autocomplete="off" class="form-control homepage-form-section-right" id="region" name="region_id">
                <option value="">{{ __('Select a state') }}</option>
                @foreach($regions as $region)
                <option value="{{ $region['id'] }}" @if(old('region_id') == $region['id']) selected @endif>{{ $region['name'] }}</option>
                @endforeach
            </select>
        </div>
        <div class="field-note mt-2">{{ __('Your location is important to find the right match in your area') }}</div>
        <div class="website-answer">
            <span class="button-type-1 nextbutton">
                {{ __('Next Step') }} <span class="countdown"></span> <i class="fas fa-chevron-right"></i>
            </span>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_15">
        <div class="website-text-1">
            {!! __('What is your <span>birthday</span>?') !!}
        </div>
        <div class="alert alert-danger alert-extra">
            {{ __('Please fill in your birthday') }}
        </div>
        @include('global.landing.includes.birthday')
        <div class="website-answer">
            <span class="button-type-1 nextbutton">
                {{ __('Next Step') }} <span class="countdown"></span> <i class="fas fa-chevron-right"></i>
            </span>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_16">
        <div class="results-box-new hide show1">
            <div class="results-box-new-title">
                {{ __('Results') }}:
            </div>
            <div class="results-box-new-content hide show2">
                <div class="radar">
                    <div class="cross1"></div>
                    <div class="cross2"></div>
                    <div class="circle1"></div>
                    <div class="circle2"></div>
                    <div class="line"></div>
                </div>
                <div class="results-box-new-subtitle-1 hide show3">
                    {{ __('Searching for an available spot') }}
                </div>
                <div class="box-2 results-box-new-list-item hide show4">
                    <i class="fab fa-gripfire"></i> {{ __('You are eligible to register') }}
                </div>
                <div class="box-3 results-box-new-list-item hide show5">
                    <i class="fab fa-gripfire"></i> {!! __('There are <span>217</span> women that match your chosen preference') !!}
                </div>
                <div class="box-4 results-box-new-list-item hide show6">
                    <i class="fab fa-gripfire"></i> {{ __('No previous registration found') }}
                </div>
                <div class="box-5 results-box-new-list-item hide show7">
                    <i class="fab fa-gripfire"></i> {!! __('Only <span>3 spots available</span> at this moment') !!}
                </div>
            </div>
            <div class="results-box-new-content hide show8">
                <div class="results-box-new-subtitle-2">{{ __('You are eligible to create an account!') }}</div>
                <div class="results-box-new-subtitle-2b hide show9">{{ __('Comply to the following rules to proceed') }}</div>
                <ol class="results-box-new-list hide show10">
                    <li>1. {{ __('If you recognize someone you know, keep it to yourself.') }}</li>
                    <li>2. {{ __('Keeping yourself safe against STDs is your own responsibility.') }}</li>
                    <li>3. {{ __('Respect all members! Our members are normal men and women, not pornstars or prostitutes.') }}</li>
                </ol>
                <div class="website-answer">
                    <span class="button-type-1 nextbutton">
                        {{ __('Next Step') }} <span class="countdown hide show11"></span> <i class="fas fa-chevron-right"></i>
                    </span>
                </div>
            </div>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_17">
        <div class="website-text-1">
            {!! __('Enter username. <span>Don\'t use your real name</span>, we care about your privacy.') !!}
        </div>
        <div class="input-1">
            <div class="alert alert-danger alert-extra">
                {{ __('Please fill in a username that is at least 6 characters long') }}
            </div>
            <input maxlength="15" placeholder="{{ __('Username') }}" id="username" class="form-control" name="username" type="text" value="{{ old('username') }}">
        </div>
        <div class="website-answer">
            <span class="button-type-1 nextbutton">
                {{ __('Next Step') }} <span class="countdown"></span> <i class="fas fa-chevron-right"></i>
            </span>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_18">
        <div class="website-text-1">
            {!! __('Create your <span>password</span>') !!}
        </div>

        <div class="input-1">
            <div class="alert alert-danger alert-extra">
                {{ __('Please fill in a password') }}
            </div>
            <input maxlength="100" placeholder="{{ __('Password') }}" id="password" name="password" type="password" value="">
        </div>

        <div class="website-answer">
            <span class="button-type-1 nextbutton">
                {{ __('Next Step') }} <span class="countdown"></span> <i class="fas fa-chevron-right"></i>
            </span>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_19">
        <div class="website-text-1">
            {!! __('What is your <span>email address</span>?') !!}
        </div>
        <div class="info-additional">
			{{ __('Please enter your email address to:') }}
				<ul>
					<li><i class="fas fa-check"></i> {{ __('Receive nude pics from women') }}</li>
					<li><i class="fas fa-check"></i> {{ __('Search through profiles') }}</li>
					<li><i class="fas fa-check"></i> {{ __('Receive messages from hot women') }}</li>
					<li><i class="fas fa-check"></i> {{ __('Get sex contact tonight!') }}</li>
                    <li><i class="fas fa-check"></i> {{ __('No subscription!') }}</li>
				</ul>
			</div>
        <div class="input-1">
            <div class="alert alert-danger alert-extra">
                {{ __('Please fill in your email address') }}
            </div>
            <input type="text" name="email" value="{{ old('email') }}" id="email" placeholder="{{ __('E-mail address') }}" maxlength="100" />
        </div>

        <div class="website-answer">
            <span class="button-type-1 nextbutton">
                {{ __('Next Step') }} <span class="countdown"></span> <i class="fas fa-chevron-right"></i>
            </span>
        </div>
        <div style="clear:both"></div>
    </div>

    <div class="step step_20">
        <div class="website-text-1 website-text-terms">
            {{ __('Terms & Conditions') }}
        </div>
        <div class="alert alert-danger alert-extra">
            {{ __('Please accept the Terms & Conditions, Privacy Policy and Cookie Policy.') }}
        </div>
        <label class="terms_and_conditions">
            <input name="terms" type="checkbox" value="1">
            {!! __('By signing up I certify that (1) I am at least 18 years old and have reached the age of majority in my jurisdiction to use this adult fantasychat, (2) have read and agree to the <a href="/agreement" target="_blank" rel="nofollow">Terms & Conditions</a>, and I understand how the Website will process my personal data in accordance with the <a href="/privacy" target="_blank" rel="nofollow">Privacy Statement</a>.') !!}
        </label>

        <input type="hidden" name="home" value="1" />
        <input type="hidden" name="landing" value="2412" />
        <input type="hidden" name="sub_id" id="sub_id_input" />

        <div class="website-answer">
            <button class="button-type-1">
                {{ __('Start Now!') }} <span class="countdown"></span> <i class="fas fa-chevron-right"></i>
            </button>
        </div>
        <div style="clear:both"></div>
    </div>
@endif

<div class="instructions">
    <div class="avatar">
        <img src="{{ $domain->cdn_url_landing }}/global/img/landing24/avatar-a-1.jpg">
        <img src="{{ $domain->cdn_url_landing }}/global/img/landing24/avatar-a-2.jpg">
        <img src="{{ $domain->cdn_url_landing }}/global/img/landing24/avatar-a-3.jpg">
    </div>
    <div class="title-guide">{{ __('Sexting made easy!') }}</div>
    <div class="instructions-animated">
        <div>
            <span>{{ __('Step') }} 1:</span> {{ __('Register now for free!') }}
        </div>
        <div>
            <span>{{ __('Step') }} 2:</span> {{ __('Browse through our members and find someone you like.') }}
        </div>
        <div>
            <span>{{ __('Step') }} 3:</span> {{ __('If the feeling is mutual, start texting and/or sexting!') }}
        </div>
    </div>
    <div style="clear:both"></div>
</div>

<div style="clear:both"></div>
</form>
@endsection
@section('scripts')
<script>
    /* 3 step instructions at the bottom */
    $(".instructions-animated > div:gt(0)").hide();
    setInterval(function() {
        $('.instructions-animated > div:first')
            .fadeOut(1000)
            .next()
            .fadeIn(1000)
            .end()
            .appendTo('.instructions-animated');
    }, 5000);
    $(".avatar > img:gt(0)").hide();
    setInterval(function() {
        $('.avatar > img:first')
            .fadeOut(1000)
            .next()
            .fadeIn(1000)
            .end()
            .appendTo('.avatar');
    }, 5000);

    /* prevent the user from using the enter button */
    $('form input').keydown(function (e) {
        if (e.keyCode == 13) {
            e.preventDefault();
            return false;
        }
    });
    @if(!$errors->any())
    $('form').on('submit',function(ev){
        if($("input[name='terms']").prop("checked") == false) {
            ev.preventDefault();
            $(".alert").css("display", "block");
            return false;
        }
    });
    @endif

    /* new code 3 */
    $(".progress-bar").attr("aria-valuenow", 20).css("width", "10%");
    $(".progress-bar-percentage").html("10%");

    /* hide alerts */
    $(".alert").css("display", "none");
    $(".step_0 .alert").css("display", "block");

    var currentStep = 0;
    $(".step").css("display", "none");
    $(".step_0").css("display", "block");

    $(".answer-box label").click( function() {
        toNextStepIfPossible();
    });

    $(".nextbutton").click( function() {
        toNextStepIfPossible();
    });

    function toNextStepIfPossible(){

        /* remove blur */
        $('.video-blur').css({
            "-webkit-filter":"blur(0px)",
            "-moz-filter":"blur(0px)",
            "-o-filter":"blur(0px)",
            "-ms-filter":"blur(0px)",
            "filter":"blur(0px)"
        });

        /* reset timer */
        timer2 = "00:25";
        $('.countdown').css("display", "inline-block");
        $('.countdown').html("(25)");

        /* go to next step */
        if (currentStep == 0){

            /* update step visibility */
            $(".step").css("display", "none");
            $(".step_1").css("display", "block");

            /* update current step */
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 15).css("width", "15%");
            $(".progress-bar-percentage").html("15%");

        } else if (currentStep == 1){

            /* update step visibility */
            $(".step").css("display", "none");
            $(".step_2").css("display", "block");

            /* update current step */
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 20).css("width", "20%");
            $(".progress-bar-percentage").html("20%");

        } else if (currentStep == 2){

            /* update step visibility */
            $(".step").css("display", "none");
            $(".step_3").css("display", "block");

            /* update current step */
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 25).css("width", "25%");
            $(".progress-bar-percentage").html("25%");
        } else if (currentStep == 3){

            /* place input in summary */
            $('.chosen_age_input_1').text($("#chosen_age_1").val());
            $('.chosen_age_input_2').text($("#chosen_age_2").val());

            /* update step visibility */
            $(".step").css("display", "none");
            $(".step_4").css("display", "block");

            /* update current step */
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 30).css("width", "30%");
            $(".progress-bar-percentage").html("30%");
        } else if (currentStep == 4){

            /* update step visibility */
            $(".step").css("display", "none");
            $(".step_5").css("display", "block");

            /* update current step */
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 35).css("width", "35%");
            $(".progress-bar-percentage").html("35%");
        } else if (currentStep == 5){

            /* update step visibility */
            $(".step").css("display", "none");
            $(".step_6").css("display", "block");

            /* update current step */
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 40).css("width", "40%");
            $(".progress-bar-percentage").html("40%");
        } else if (currentStep == 6){

            /* update step visibility */
            $(".step").css("display", "none");
            $(".step_7").css("display", "block");

            /* update current step */
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 45).css("width", "45%");
            $(".progress-bar-percentage").html("45%");
        } else if (currentStep == 7){

            /* update step visibility */
            $(".step").css("display", "none");
            $(".step_8").css("display", "block");

            /* update current step */
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 50).css("width", "50%");
            $(".progress-bar-percentage").html("50%");
        } else if (currentStep == 8){

            /* update step visibility */
            $(".step").css("display", "none");
            $(".step_9").css("display", "block");

            /* update current step */
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 55).css("width", "55%");
            $(".progress-bar-percentage").html("55%");
        } else if (currentStep == 9){

            /* update step visibility */
            $(".step").css("display", "none");
            $(".step_10").css("display", "block");

            /* update current step */
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 60).css("width", "60%");
            $(".progress-bar-percentage").html("60%");
        } else if (currentStep == 10){

            /* update step visibility */
            $(".step").css("display", "none");
            $(".step_11").css("display", "block");

            /* update current step */
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 65).css("width", "65%");
            $(".progress-bar-percentage").html("65%");
        } else if (currentStep == 11){

            /* update step visibility */
            $(".step").css("display", "none");
            $(".step_12").css("display", "block");

            /* update current step */
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 70).css("width", "70%");
            $(".progress-bar-percentage").html("70%");
        } else if (currentStep == 12){

            /* update step visibility */
            $(".step").css("display", "none");
            $(".step_13").css("display", "block");

            /* update current step */
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 75).css("width", "75%");
            $(".progress-bar-percentage").html("75%");
        } else if (currentStep == 13){

            $(".step").css("display", "none");
            $(".step_14").css("display", "block");
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 80).css("width", "80%");
            $(".progress-bar-percentage").html("80%");
        } else if (currentStep == 14){

            if ($("#cityInput").val() == "") {
                $(".alert").css("display", "block");
                return;
            }
            $(".alert").css("display", "none");

            $(".step").css("display", "none");
            $(".step_15").css("display", "block");
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 85).css("width", "85%");
            $(".progress-bar-percentage").html("85%");
        } else if (currentStep == 15){

            if ($("#day").val() == "" || $("#month").val() == "" || $("#year").val() == "") {
                $(".alert").css("display", "block");
                return;
            }
            $(".alert").css("display", "none");

            $(".step").css("display", "none");
            $(".step_16").css("display", "block");
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 90).css("width", "90%");
            $(".progress-bar-percentage").html("90%");

            /* code for results page*/
            setTimeout(function(){ $(".show1").show(); }, 0);
            setTimeout(function(){ $(".show2").show(); }, 0);
            setTimeout(function(){ $(".show3").show(); }, 0);
            setTimeout(function(){ $(".show4").show(); }, 3000);
            setTimeout(function(){ $(".show5").show(); }, 4000);
            setTimeout(function(){ $(".show6").show(); }, 5000);
            setTimeout(function(){ $(".show7").show(); }, 6000);
            setTimeout(function(){ $(".show2").hide(); }, 10000);
            setTimeout(function(){ $(".show8").show(); }, 10000);
            setTimeout(function(){ $(".show9").show(); }, 11000);
            setTimeout(function(){ $(".show10").show(); }, 12000);
            setTimeout(function(){ $(".show11").css("display", "inline-block");}, 12000);
        }

        else if (currentStep == 16){
            $(".step").css("display", "none");
            $(".step_17").css("display", "block");
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 93).css("width", "93%");
            $(".progress-bar-percentage").html("93%");

        } else if (currentStep == 17){

            if ($("#username").val() == "" || $("#username").val().length < 6) {
                $(".alert").css("display", "block");
                return;
            }
            $(".alert").css("display", "none");

            $(".step").css("display", "none");
            $(".step_18").css("display", "block");
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 95).css("width", "95%");
            $(".progress-bar-percentage").html("95%");
        } else if (currentStep == 18){
            if ($("#password").val() == "") {
                $(".alert").css("display", "block");
                return;
            }
            $(".alert").css("display", "none");

            $(".step").css("display", "none");
            $(".step_19").css("display", "block");
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 97).css("width", "97%");
            $(".progress-bar-percentage").html("97%");
        } else if (currentStep == 19){
            if ($("#email").val() == "") {
                $(".alert").css("display", "block");
                return;
            }
            $(".alert").css("display", "none");
            $(".step").css("display", "none");
            $(".step_20").css("display", "block");
            currentStep = currentStep + 1;

            /* new code 3 */
            $(".progress-bar").attr("aria-valuenow", 99).css("width", "99%");
            $(".progress-bar-percentage").html("99%");
        }

    }
</script>

@if($errors->hasBag('register'))
    <script>
        /* remove blur slowly */
        $('.video-blur').css({
            "-webkit-transition":"3s -webkit-filter linear",
            "-o-transition":"3s -o-filter linear",
            "transition":"3s filter linear",

            "-webkit-filter":"blur(0px)",
            "-moz-filter":"blur(0px)",
            "-o-filter":"blur(0px)",
            "-ms-filter":"blur(0px)",
            "filter":"blur(0px)"
        });
        /* new code 3 */
        $(".progress-bar").attr("aria-valuenow", 99).css("width", "99%");
        $(".progress-bar-percentage").html("99%");
    </script>
@else
    <script>

        /* initiate timer */
        var timer2 = "00:25";
        var interval = setInterval(function() {
            var timer = timer2.split(':');
            var minutes = parseInt(timer[0], 10);
            var seconds = parseInt(timer[1], 10);
            --seconds;
            minutes = (seconds < 0) ? --minutes : minutes;
            if (minutes < 0) clearInterval(interval);
            seconds = (seconds < 0) ? 59 : seconds;
            secondsAlt = seconds;
            seconds = (seconds < 10) ? '0' + seconds : seconds;

            $('.countdown').html("("+secondsAlt+")");
            timer2 = minutes + ':' + seconds;
            if (timer2 == "0:00"){ timer2 = "00:25";
                $(".countdown").css("display", "none");
                $('.video-blur').css({
                    "-webkit-filter":"blur(15px)",
                    "-moz-filter":"blur(15px)",
                    "-o-filter":"blur(15px)",
                    "-ms-filter":"blur(15px)",
                    "filter":"blur(15px)"
                });
            }
        }, 1000);

    </script>
@endif
@endsection
