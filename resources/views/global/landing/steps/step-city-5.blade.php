<div class="row step d-none step-city">
    <div class="col-12 step-title">
        <h1 class="main-title mb-2">
            {!! __('Where do you live?') !!}
        </h1>
    </div>
    <div class="col-12">
        <div class="alert alert-danger alert-extra alert-city">
            {{ __('Please select your city from the dropdown list') }}
        </div>
    </div>
    <div class="col-12">
        <input class="form-control form-control-lg text-center m-auto" data-href="{{ route('cityAutocomplete') }}" data-position="below" type="text" name="geoLocation" value="" placeholder="{{ __('Type 3 or more characters') }}" class="form-control" id="geoLocation" autocomplete="off">
        <input type="hidden" name="city_id" value="{{ old('city_id') }}" id="cityInput" data-field="city_id">
        <input type="hidden" name="region_id" value="{{ old('region_id') }}" id="regionInput" data-field="region_id">
    </div>
    <div class="col-12 step-button">
        <div class="row justify-content-center">
            <div class="col-6 mt-3">
                <button type="button" class="btn btn-primary btn-lg button-preferred button-next" dusk="default-button-city-5">
                    {{ __('Next') }}
                    <span class="button-countdown d-none"></span>
                    @if($landing->button_icon)<i class="fas {{ $landing->button_icon }}"></i>@endif
                </button>
            </div>
        </div>
    </div>
</div>
