<div class="row step d-none step-terms step-last">
    <div class="col-12 step-title">
        <h1 class="main-title mb-2 mb-md-4">
            {{ __('Check your data') }}
        </h1>
    </div>
    <div class="col-12">
        <div class="alert alert-danger alert-extra alert-terms">
            {{ __('Please accept the Terms & Conditions, Privacy Policy and Cookie Policy.') }}
        </div>
    </div>
    <div class="col-12 summary">
        <div class="row">
            <div class="col-12">
                <i class="fa fa-check-square" aria-hidden="true"></i>
                <span class="fw-bold">{!! __('Username') !!}:</span> <span class="filled-username"></span>
            </div>
            <div class="col-12">
                <i class="fa fa-check-square" aria-hidden="true"></i>
                <span class="fw-bold">{!! __('Email') !!}:</span> <span class="filled-email"></span>
            </div>
        </div>
    </div>
    <div class="col-12">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" value="1" name="terms" id="terms" required>
            <label class="form-check-label small-print" for="terms">
                {!! __('By signing up I certify that (1) I am at least 18 years old and have reached the age of majority in my jurisdiction to use this adult fantasychat, (2) have read and agree to the <a href="/agreement" target="_blank" rel="nofollow">Terms & Conditions</a>, and I understand how the Website will process my personal data in accordance with the <a href="/privacy" target="_blank" rel="nofollow">Privacy Statement</a>.') !!}
            </label>
        </div>
    </div>
    <div class="col-12 step-button">
        <div class="row justify-content-center">
            <div class="col-md-6 mt-3">
                <button type="submit" class="btn btn-primary btn-lg" dusk="register-landing-button">{{ __('Confirm') }}</button>
            </div>
        </div>
    </div>
</div>

@push('child-scripts')
<script>
$(".button-next").click( function() {
    $(".filled-username").text(document.getElementById('username').value);
    $(".filled-email").text(document.getElementById('email').value);
});
</script>
@endpush
