<div class="row step d-none step-city">
    <div class="col-12 text-box">
        <div class="row">
            <div class="col-auto">
                <div class="position-relative">
                    <img src="{{ $domain->cdn_url_landing }}/global/img/landing{{ $landing->number }}/image-admin.png"
                        class="avatar">
                    <div class="online-dot"></div>
                </div>
            </div>
            <div class="col">
                <div class="show-first">
                    <h1 class="main-title">
                        <span class="blinking">...</span>
                    </h1>
                </div>
                <div class="show-last">
                    <h1 class="main-title">
                        {!! __('Where do you live?') !!}
                    </h1>
                </div>
            </div>
        </div>
    </div>
    <div class="col-auto">
        <div class="alert alert-danger alert-extra alert-city">
            {{ __('Please select your city from the dropdown list') }}
        </div>
    </div>
    <div class="col-12 answer-box">
        <input class="form-control form-control-lg text-center m-auto" data-href="{{ route('cityAutocomplete') }}"
            data-position="below" type="text" name="geoLocation" value=""
            placeholder="{{ __('Type 3 or more characters') }}" class="form-control" id="geoLocation"
            autocomplete="off">
        <input type="hidden" name="city_id" value="{{ old('city_id') }}" id="cityInput" data-field="city_id">
        <input type="hidden" name="region_id" value="{{ old('region_id') }}" id="regionInput" data-field="region_id">
    </div>
    <div class="col-12 answer-box step-button">
        <div class="row justify-content-center">
            <div class="col-6 mt-3">
                <button type="button" class="btn btn-primary btn-lg button-preferred button-next" dusk="default-button-city-chat">
                    {{ __('Continue') }}
                    <span class="button-countdown d-none"></span>
                    @if ($landing->button_icon)
                        <i class="fas {{ $landing->button_icon }}"></i>
                    @endif
                </button>
            </div>
        </div>
    </div>
</div>
