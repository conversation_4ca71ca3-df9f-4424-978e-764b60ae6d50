<div class="row step d-none step-username">
    <div class="col-12 step-title">
        <h1 class="main-title mb-2">
            {!! __('What nickname do you want?') !!}
        </h1>
    </div>
    <div class="col-12">
        <div class="alert alert-danger alert-extra alert-username">
            {{ __('Please fill in a username that is at least 6 characters long') }}
        </div>
    </div>
    <div class="col-12 mb-4">
        <input minlength="6" maxlength="15" placeholder="{{ __('Username') }}" id="username" class="form-control form-control-lg text-center m-auto" name="username" type="text" value="{{ old('username') }}">
    </div>
    <div class="col-12">
        <div class="row justify-content-center">
            <div class="col-6">
                <button type="button" class="btn btn-primary btn-lg button-next" dusk="default-button-username-4">
                    {{ __('Next') }}
                    <span class="button-countdown d-none"></span>
                    @if($landing->button_icon)<i class="fas {{ $landing->button_icon }}"></i>@endif
                </button>
            </div>
        </div>
    </div>
</div>
