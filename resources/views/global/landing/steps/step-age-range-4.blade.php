@php
$ageMin = 30;
$ageMax = 60;
@endphp
<div class="row step step-age-range age-range d-none">
    <div class="col-12 step-title">
        <h1 class="main-title mb-2 mb-md-4">
            {!! __('What\'s the ideal age of your new match?') !!}
        </h1>
    </div>
    <div class="col-6">
        <span class="field-title">{{ __('Between') }}:</span>
        <select class="form-control form-control-lg age-min" name="looking_for_age_min" id="chosen_age_1">
            @for($i = 18; $i <= 99; $i++)
                <option value="{{ $i }}" @if($i == $ageMin) selected @endif @if($i > $ageMax) disabled @endif>{{ $i }}</option>
            @endfor
        </select>
    </div>
    <div class="col-6">
        <span class="field-title">{{ __('And') }}:</span>
        <select class="form-control form-control-lg age-max" name="looking_for_age_max" id="chosen_age_2">
            @for($i = 18; $i <= 99; $i++)
                <option value="{{ $i }}" @if($i == $ageMax) selected @endif @if($i < $ageMin) disabled @endif>{{ $i }}</option>
            @endfor
        </select>
    </div>
    <div class="col-12 step-buttons">
        <div class="row justify-content-center">
            <div class="col-md-6 mt-3">
                <button type="button" class="btn btn-primary btn-lg button-next" dusk="default-button-age-range-4">
                    {{ __('Next') }}
                    <span class="button-countdown d-none"></span>
                    @if($landing->button_icon)<i class="fas {{ $landing->button_icon }}"></i>@endif
                </button>
            </div>
        </div>
    </div>
</div>
