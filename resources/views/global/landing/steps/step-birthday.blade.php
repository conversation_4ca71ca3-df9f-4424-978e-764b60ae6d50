<div class="row step d-none step-birthday">
    <div class="col-12 step-title">
        <h1 class="main-title mb-2 mb-md-4">
            {!! __('What is your <span>birthday</span>?') !!}
        </h1>
    </div>
    <div class="col-12">
        <div class="alert alert-danger alert-extra alert-birthday">
            {{ __('Please fill in your birthday') }}
        </div>
    </div>
    <div class="col-12">
        <div class="alert alert-danger alert-extra alert-underage">
            {{ __('Registration is only allowed when you are at least 18 years old') }}
        </div>
    </div>
    <div class="col-12">
        <div class="row age-selector">
            @if($domain->locale == 'en_US')
            <div class="col-4">
                <select class="form-control form-control-lg" name="birth_month" id="month">
                    <option value="">{{ __('MM') }}</option>
                    @for($m = 1; $m < 13; $m++)
                    <option value="{{ sprintf("%02d", $m) }}" @if(old('birth_month') == sprintf("%02d", $m)) selected @endif>{{ date('M', strtotime('2010-'.$m.'-01')) }}</option>
                    @endfor
                </select>
            </div>
            @endif
            <div class="col-4">
                <select class="form-control form-control-lg" name="birth_day" id="day">
                    <option value="">{{ __('DD') }}</option>
                    @for($d = 1; $d < 32; $d++)
                    <option value="{{ sprintf("%02d", $d) }}" @if(old('birth_day') == sprintf("%02d", $d)) selected @endif>{{ sprintf("%02d", $d) }}</option>
                    @endfor
                </select>
            </div>
            @if($domain->locale != 'en_US')
            <div class="col-4">
                <select class="form-control form-control-lg" name="birth_month" id="month">
                    <option value="">{{ __('MM') }}</option>
                    @for($m = 1; $m < 13; $m++)
                    <option value="{{ sprintf("%02d", $m) }}" @if(old('birth_month') == sprintf("%02d", $m)) selected @endif>{{ ucfirst(now()->parse('2010-'.$m.'-01')->locale(app()->getLocale())->getTranslatedShortMonthName()) }}</option>
                    @endfor
                </select>
            </div>
            @endif
            <div class="col-4">
                <select class="form-control form-control-lg" name="birth_year" id="year">
                    <option value="">{{ __('YYYY') }}</option>
                    @for($y = date('Y') - 18; $y > date('Y') - 101; $y--)
                    <option value="{{ $y }}" @if(old('birth_year') == $y) selected @endif>{{ $y }}</option>
                    @endfor
                </select>
            </div>
        </div>
    </div>
    <div class="col-12 step-button">
        <div class="row justify-content-center">
            <div class="col-md-6 mt-3">
                <button type="button" class="btn btn-primary btn-lg button-next" dusk="default-button-birthday">
                    {{ __('Next') }}
                    <span class="button-countdown d-none"></span>
                    @if($landing->button_icon)<i class="fas {{ $landing->button_icon }}"></i>@endif
                </button>
            </div>
        </div>
    </div>
</div>
