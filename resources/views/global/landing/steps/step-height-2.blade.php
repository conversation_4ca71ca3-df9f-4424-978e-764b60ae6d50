<div class="row step step-height d-none">
    <div class="col-12 step-title">
        <h1 class="main-title mb-2">
            {!! __('How <span>tall</span> are you?') !!}
        </h1>
    </div>
    <div class="col-9 mb-5">
        <select name="length" class="form-select">
            @foreach(trans('lists.heights_grouped_short') as $key => $value)
            <option value="{{ $key }}">{{ __($value) }}</option>
            @endforeach
        </select>
    </div>
    <div class="col-6">
        <button type="button" class="btn btn-secondary btn-lg button-back">
            @if($landing->button_icon)<i class="fas fa-arrow-left"></i>@endif
            {{ __('Back') }}
        </button>
    </div>
    <div class="col-6">
        <button type="button" class="btn btn-primary btn-lg button-next" dusk="default-button-height-2">
            {{ __('Next') }}
            <span class="button-countdown d-none"></span>
            @if($landing->button_icon)<i class="fas {{ $landing->button_icon }}"></i>@endif
        </button>
    </div>
</div>
