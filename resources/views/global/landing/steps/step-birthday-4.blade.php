<div class="row step d-none step-birthday">
    <div class="col-12 step-title">
        <h1 class="main-title mb-2 mb-md-4">
            {!! __('My birthday is') !!}
        </h1>
    </div>
    <div class="col-12">
        <div class="alert alert-danger alert-extra alert-birthday">
            {{ __('Please fill in a valid birthday') }}
        </div>
    </div>
    <div class="col-12">
        <div class="alert alert-danger alert-extra alert-underage">
            {{ __('Registration is only allowed when you are at least 18 years old') }}
        </div>
    </div>
    <div class="col-12">
        <div class="row age-selector-2 align-items-center g-2">
            @if($domain->locale == 'en_US')
            <div class="col">
                <input class="form-control form-control-lg text-center m-auto observe-typing" type="text" name="birth_month" value="" id="month" placeholder="{{ __('MM') }}" maxlength="2" minlength="2" dusk="month-input" required/>
            </div>
            <div class="col-auto">
                /
            </div>
            @endif
            <div class="col">
                <input class="form-control form-control-lg text-center m-auto observe-typing" type="number" name="birth_day" value="" id="day" placeholder="{{ __('DD') }}" maxlength="2" minlength="2" dusk="day-input" required/>
            </div>
            <div class="col-auto">
                /
            </div>
            @if($domain->locale != 'en_US')
            <div class="col">
                <input class="form-control form-control-lg text-center m-auto observe-typing" type="number" name="birth_month" value="" id="month" placeholder="{{ __('MM') }}" maxlength="2" minlength="2" dusk="month-input" required/>
            </div>
            <div class="col-auto">
                /
            </div>
            @endif
            <div class="col-4">
                <input class="form-control form-control-lg text-center m-auto observe-typing" type="number" name="birth_year" value="" id="year" placeholder="{{ __('YYYY') }}" maxlength="4" minlength="4" dusk="year-input" required/>
            </div>
        </div>
    </div>
    <div class="col-12 step-button">
        <div class="row justify-content-center">
            <div class="col-md-6 mt-3">
                <button type="button" class="btn btn-primary btn-lg button-next enable-when-valid" dusk="default-button-birthday-4" disabled>
                    {{ __('Next') }}
                    <span class="button-countdown d-none"></span>
                    @if($landing->button_icon)<i class="fas {{ $landing->button_icon }}"></i>@endif
                </button>
            </div>
        </div>
    </div>
</div>
