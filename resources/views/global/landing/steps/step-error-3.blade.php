<div class="row step step_error">
    <div class="col-12 step-title">
        <h1 class="main-title mb-2 mb-md-4 desktop-only">
            {{ __('Oops, something went wrong.') }}
        </h1>
    </div>
    <div class="col-12">
        @if($errors->hasBag('register'))
            <div class="alert alert-danger">
                @foreach($errors->register->all() as $error)
                    {!! $error !!}
                @endforeach
            </div>
        @endif
    </div>
    <div class="col-12 mb-2">
        <div class="field-title">{{ __('Username') }}</div>
        <input maxlength="15" placeholder="{{ __('Username') }}" id="username" class="form-control form-control-lg text-center" name="username" type="text" value="{{ old('username') }}">
    </div>
    <div class="col-12 mb-2">
        <div class="field-title">{{ __('Password') }}</div>
        <input class="form-control form-control-lg text-center" maxlength="100" placeholder="{{ __('Password') }}" id="password" name="password" type="password" value="{{ old('password') }}">
    </div>
    <div class="col-12 mb-2">
        <div class="field-title">{{ __('E-mail') }}</div>
        <input class="form-control form-control-lg text-center" type="email" name="email" value="{{ old('email') }}" id="email" placeholder="{{ __('E-mail (e.g. ..@gmail.com)') }}" maxlength="100"/>
    </div>
    <div class="col-12 mt-4">
        <div class="form-check">
            <input class="form-check-input" type="checkbox" value="1" name="terms" id="terms" required>
            <label class="form-check-label small-print" for="terms">
                {!! __('By signing up I certify that (1) I am at least 18 years old and have reached the age of majority in my jurisdiction to use this adult fantasychat, (2) have read and agree to the <a href="/agreement" target="_blank" rel="nofollow">Terms & Conditions</a>, and I understand how the Website will process my personal data in accordance with the <a href="/privacy" target="_blank" rel="nofollow">Privacy Statement</a>.') !!}
            </label>
        </div>
    </div>
    <div class="col-12 mt-4">
        <button type="submit" class="btn btn-primary btn-lg">{{ __('Start Now!') }} @if($landing->button_icon)<i class="fas {{ $landing->button_icon }}"></i>@endif</button>
    </div>
</div>
<input type="hidden" name="city_id" value="{{ old('city_id') }}" required="">
<input type="hidden" name="region_id" value="{{ old('region_id') }}" required="">
<input type="hidden" data-field="gender" name="gender" required="" value="{{ old('gender') }}">
@if(old('looking_for_age_min'))
<input type="hidden" data-age="min" name="looking_for_age_min" required="required" value="{{ old('looking_for_age_min') }}">
@endif
@if(old('looking_for_age_max'))
<input type="hidden" data-age="max" name="looking_for_age_max" required="required" value="{{ old('looking_for_age_max') }}">
@endif
<input type="hidden" data-field="seek" name="seek" required="" value="{{ old('seek') }}">
<input type="hidden" name="birth_day" value="{{ old('birth_day') }}" />
<input type="hidden" name="birth_month" value="{{ old('birth_month') }}" />
<input type="hidden" name="birth_year" value="{{ old('birth_year') }}" />
<input type="hidden" name="marital_status" value="{{ old('marital_status') }}" />
<input type="hidden" name="eye_color" value="{{ old('eye_color') }}" />
<input type="hidden" name="hair_color" value="{{ old('hair_color') }}" />
<input type="hidden" name="ethnicity" value="{{ old('ethnicity') }}" />
<input type="hidden" name="body_type" value="{{ old('body_type') }}" />
<input type="hidden" name="length" value="{{ old('length') }}" />
@foreach(trans('preferences.' . $domain->theme) as $key => $value)
@if(isset(old('interests')[$key]))
<input type="hidden" name="interests[{{ $key }}]" value="1">
@endif
@endforeach
