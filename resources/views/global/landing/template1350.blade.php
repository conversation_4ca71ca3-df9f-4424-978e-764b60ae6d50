@extends('global.layouts.landing2')
@section('content')
@include('global.landing.includes.progress')
@include('global.landing.includes.background')
@if($landing->show_logo)
<section class="position-relative bg-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-auto">
                <img class="logo" src="{{ $domain->theme_url }}/images/{{ strtolower($domain->name) }}.png"/>
            </div>
        </div>
    </div>
</section>
@endif
<section>
    @if($landing->slideshow)
        @include('global.landing.includes.'.$landing->slideshow)
    @endif
    @if($landing->video)
        @include('global.landing.includes.'.$landing->video)
    @endif
    <div class="container p-4">
        @if($landing->top_of_page)
        @include('global.landing.includes.'.$landing->top_of_page)
        @endif
        <div class="row justify-content-center py-3 py-md-5 landing-container">
            <div class="col-md-7 landing-content">
                @if($landing->before_content)
                @include('global.landing.includes.'.$landing->before_content)
                @endif
                <form id="recaptcha-form-{{ $landing_id }}" method="POST" action="{{ route('postRegister') }}" accept-charset="UTF-8" role="form">
                    @csrf
                    @if($errors->hasBag('register'))
                        @include('global.landing.steps.step-error')
                    @else
                        @foreach($landing->steps as $step)
                        @if($step == 'google' && !$google_signup) @continue @endif
                        @include('global.landing.steps.step-'.$step)
                        @endforeach
                    @endif
                    <input type="hidden" name="home" value="1" />
                    <input type="hidden" name="landing" value="{{ $landing_id }}" />
                    <input type="hidden" name="google_id" value="{{ old('google_id') }}" />
                    <input type="hidden" name="image_url" value="{{ old('image_url') }}" />
                </form>
                @if($landing->instructions)
                @include('global.landing.includes.'.$landing->instructions)
                @endif
                @if($landing->after_content)
                @include('global.landing.includes.'.$landing->after_content)
                @endif
            </div>
        </div>
        @if($landing->bottom_of_page)
        @include('global.landing.includes.'.$landing->bottom_of_page)
        @endif
    </div>
</section>
@endsection
@section('scripts')
    @if($domain->recaptcha_secret)
        <x-forms.scripts.recaptcha selector="#recaptcha-form-{{ $landing_id }}"/>
    @endif
@endsection
