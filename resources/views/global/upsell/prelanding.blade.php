<!doctype html>
<html>
<head>
	<meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<title>{{ __('Search for match') }}</title>
	<!-- Bootstrap -->
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
	<!-- Fontawesome -->
	<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous">
	<style>
		.website-container{
			display:block;
			margin-left:auto;
			margin-right:auto;
			width:100%;
			max-width:650px;
			min-height:20px;
			padding: 50px 10px 10px 10px;
		}
		.website-content{
			display:block;
			width:100%;
			border-color: rgb(207, 207, 207);
			border-style:solid;
			border-width:1px;
			border-radius:10px;
			padding:20px 10px;
			font-size:22px;
			text-align: center;
			background-color: grey;
			color:white;
		}
		.searching{
			font-size: 34px;
			margin-bottom:40px;
			animation: blinking 2.2s infinite!important;
			-webkit-animation: blinking 2.2s infinite!important;
			line-height: 38px;
			padding:10px;
		}
		.list{
			display: block;
			position: relative;
			width:100%;
			max-width: 320px;
			text-align: left;
			margin-left:auto;
			margin-right:auto;
		}
		.list > div > span{
			font-weight:bold;
		}
		.list > div > i{
			color: yellowgreen;
		}
		.title{
			font-size: 40px;
			margin-top:40px;
		}
		.logo{
			display:block;
			position:relative;
			margin-left:auto;
			margin-right:auto;
			width:100%;
			max-width:300px;
			border-top-width:1px;
			border-top-style:dashed;
			border-top-color:white;
			padding-top:10px;
			margin-top:10px;
		}
		.website-content > div, .list > div, .website-content > a > img{
			display:none;
			animation: fadein 1.2s;
			-webkit-animation: fadein 1.2s;
		}
		#div0{
			display:block;
		}
		@-webkit-keyframes blinking {
			0% {
				opacity:0.4;
			}
			50% {
				opacity:1;
			}
			100% {
				opacity:0.4;
			}
		}
		@keyframes blinking {
			0% {
				opacity:0.4;
			}
			50% {
				opacity:1;
			}
			100% {
				opacity:0.4;
			}
		}
		@-webkit-keyframes fadein {
			0% {
				opacity:0;
			}
			100% {
				opacity:1;
			}
		}
		@keyframes fadein {
			0% {
				opacity:0;
			}
			100% {
				opacity:1;
			}
		}
	</style>
</head>
<body>
	<div class="website-container">
		<div class="website-content">
			<div class="searching" id="div0">{{ __('Searching for best match...') }}</div>
			<div class="searching" id="div1">{{ __('Search complete! You are being redirected to') }} <span id="field-label"></span>.</div>
			<span class="list">
				<div id="div2">
					<i class="fas fa-check-square"></i> <span id="field-gender"></span> {{ __('looking for') }} <span id="field-seek"></span>
				</div>
				<div id="div3">
					<i class="fas fa-check-square"></i> {{ __('Location') }}: <span id="field-location"></span>
				</div>
				<div id="div4">
					<i class="fas fa-check-square"></i> {{ __('Date of birth') }}: <span id="field-birth_day"></span>-<span id="field-birth_month"></span>-<span id="field-birth_year"></span>
				</div>
				<span style="display:block; position:relative; clear:both"> </span>
			</span>
			<div id="div5" class="title">{{ __('Best match:') }}</div>
			<a href="" id="redirect_url">
                <img id="div6" class="logo" src="">
            </a>
			<span style="display:block; position:relative; clear:both"></span>
		</div>
		<span style="display:block; position:relative; clear:both"></span>
	</div>
	<!-- Bootstrap + JQuery scripts-->
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
	<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js" integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6" crossorigin="anonymous"></script>

    @if($everflowOfferId)
        <script type="text/javascript" src="https://www.aht42trk.com/scripts/sdk/everflow.js"></script>
        <script type="text/javascript">
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                }
            });

            EF.click({
                offer_id: {{ $everflowOfferId }},
                affiliate_id: {{ $user->sub1 }},
                @if(isset($user->transaction_id))
                    sub1: '{{ $user->transaction_id }}',
                @endif
                @if(isset($user->sub2))
                    sub2: '{{ $user->sub2 }}',
                @endif
                @if(isset($user->sub3))
                    sub3: '{{ $user->sub3 }}',
                @endif
                sub4: 'waterfall',
            });

            var efOid = '';

            function getAndSetSubId() {
                efOid = EF.getTransactionId({{ $everflowOfferId }});
                if(efOid != '') {
                    $.ajax({
                        url: "{{ url('updatesubid') }}",
                        type: 'POST',
                        data: {
                            user_id: {{ $user->id }},
                            email: '{{ $user->email }}',
                            sub_id: efOid
                        },
                        success: function(res) {
                            stopInterval();
                        }
                    });
                }
            }

            var getSubIdInterval = setInterval(getAndSetSubId,1000);

            function stopInterval() {
                clearInterval(getSubIdInterval);
            }

        </script>
    @endif

	<script>
		var label = "{{ $fallback_domain->name }}";
		var gender = "{{ ucfirst($user->gender) }}";
		var seek = "{{ ucfirst($user->seek) }}";
		var city = "{{ $user_info->region }}";
		var birth_day = "{{ explode("-", $user_info->birthdate)[2] }}";
		var birth_month = "{{ explode("-", $user_info->birthdate)[1] }}";
		var birth_year = "{{ explode("-", $user_info->birthdate)[0] }}";
		var logo_location = "{{ $fallback_domain->https ? 'https' : 'http' }}:{{ $fallback_domain->cdn_url }}/theme/{{ $fallback_domain->theme }}/images/{{ $fallback_domain->theme }}.png";
        var redirect_url = "{{ $fallback_domain->https ? 'https' : 'http' }}://{{ $fallback_domain->url }}/redirect-offer/{{ $fallback_domain->label_id }}/{{ $user->id }}?auth={{ $auth }}";

		$('#field-label').text(label);
		$('#field-gender').text(gender);
		$('#field-seek').text(seek);
		$('#field-location').text(city);
		$('#field-birth_day').text(birth_day);
		$('#field-birth_month').text(birth_month);
		$('#field-birth_year').text(birth_year);
		$(".logo").attr("src",logo_location);
        $("#redirect_url").attr("href",redirect_url);

		setTimeout(function(){
			$(".website-content > #div0").css("display", "none");
			$(".website-content > #div1").css("display", "block");
		}, 7500);
		setTimeout(function(){ $(".list > #div2").show(); }, 1500);
		setTimeout(function(){ $(".list > #div3").show(); }, 3000);
		setTimeout(function(){ $(".list > #div4").show(); }, 4500);
		setTimeout(function(){ $(".website-content > #div5").show(); }, 6000);
		setTimeout(function(){ $(".website-content > a > #div6").show(); }, 7500);
        setTimeout(function(){ window.location.replace(redirect_url); }, 9500);
	</script>
</body>
</html>
