<div class="wp-optin-dialog-container" style="color: black;">
    <div class="wp-optin-dialog-body">
        <div class="wp-optin-dialog-icon"
            style="background-image: url('https://wildflings.com/theme/003/images/push_subscribe_icon.webp');">
        </div>
        <div class="wp-optin-dialog-text">
            <div class="wp-optin-dialog-title">Hey you! Can I ask something in private?</div>
            <div class="wp-optin-dialog-message">Women nearby are looking for No Strings attached fun ❤</div>
        </div>
    </div>
    <div class="wp-optin-dialog-buttons"><a href="#"
            class="wp-optin-dialog-button wp-optin-dialog-negativeButton"><label>No</label></a><a href="#"
            class="wp-optin-dialog-button wp-optin-dialog-positiveButton"><label>Yes!</label></a></div><a href="#"
        class="wp-optin-dialog-close"></a>
</div>

    <style type="text/css" scoped>
        .wp-optin-dialog-container {
            z-index: 10000;
            width: 420px;
            max-width: 100%;
            box-sizing: border-box;
            background-image: linear-gradient(to bottom, #eae9ea, #eeedee);
            position: fixed;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            padding: 15px 20px 10px 20px;
            border: 1px solid #dfdfdf;
            box-shadow: 0 0 20px #888
        }

        .wp-optin-dialog-container .wp-optin-dialog-body {
            display: flex;
            flex-direction: row
        }

        .wp-optin-dialog-container .wp-optin-dialog-icon {
            flex-shrink: 0;
            flex-basis: 64px;
            width: 64px;
            height: 64px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: 50% 50%;
            margin-right: 20px
        }

        .wp-optin-dialog-container .wp-optin-dialog-text {
            display: flex;
            flex-direction: column;
            flex-grow: 1
        }

        .wp-optin-dialog-container .wp-optin-dialog-text .wp-optin-dialog-title {
            font: bold 16px arial;
            margin-bottom: 15px
        }

        .wp-optin-dialog-container .wp-optin-dialog-text .wp-optin-dialog-message {
            font: 12px arial
        }

        .wp-optin-dialog-container .wp-optin-dialog-buttons {
            display: flex;
            flex-direction: row;
            margin-top: 1em
        }

        .wp-optin-dialog-container .wp-optin-dialog-powered-by {
            flex-grow: 1;
            align-self: flex-end;
            font: 10px arial;
            color: #333;
            opacity: .8;
            background-position: bottom right;
            background-repeat: no-repeat;
            text-decoration: none;
            margin-right: .5em
        }

        .wp-optin-dialog-container .wp-optin-dialog-button {
            flex-grow: 1;
            border-radius: 4px;
            border: 1px solid #d3d3d3;
            background: #fff;
            text-decoration: none;
            font: 15px arial;
            color: #333;
            text-align: center;
            display: flex;
            min-width: 5em;
            padding: .25em .5em
        }

        .wp-optin-dialog-container .wp-optin-dialog-button label {
            cursor: inherit;
            margin: auto
        }

        .wp-optin-dialog-container .wp-optin-dialog-button.wp-optin-dialog-positiveButton {
            border-color: #46a4f3;
            color: #fff;
            background-image: linear-gradient(to bottom, #62b3f4, #0486f6);
            margin-left: .5em
        }

        .wp-optin-dialog-container .wp-optin-dialog-close {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 10px;
            height: 10px;
            text-decoration: none;
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAJCAYAAADgkQYQAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyRpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYxIDY0LjE0MDk0OSwgMjAxMC8xMi8wNy0xMDo1NzowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNS4xIE1hY2ludG9zaCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo1MDVDQUVGMDQzOUIxMUU3ODE2N0YyM0EyNDc4QTZCNyIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo1MDVDQUVGMTQzOUIxMUU3ODE2N0YyM0EyNDc4QTZCNyI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjI4NjdCN0UxNDM5QTExRTc4MTY3RjIzQTI0NzhBNkI3IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjI4NjdCN0UyNDM5QTExRTc4MTY3RjIzQTI0NzhBNkI3Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+JRvgJAAAAEdJREFUeNpiYmBgOAPEDP///8eKYfIMuBQiK8CqEEUBVABFIYYJSDoZsCkAyTGhmQhSYIJsNQNON6C5Eacv0K0+Q8iNAAEGAG9QVX0F8yI2AAAAAElFTkSuQmCC);
            background-size: cover;
            background-repeat: no-repeat;
            background-position: 50% 50%
        }
    </style>
