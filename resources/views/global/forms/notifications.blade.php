<form id="notification-form" class="" action="{{ route('updateNotifications') }}" method="POST">
    {{ Html::hidden('user', Crypt::encrypt($user->id)) }}
    @csrf
    @if ($title != '')
        @if (!isset($title_container))
            <div class="title notification-title">
                {{ $title }}
                <span
                    style="display:block; font-size:0.8rem; margin-top: 0.5rem;">{{ __('Notifications will be sent to: :email', ['email' => $user->email]) }}</span>
            </div>
        @else
            <{{ $title_container }}>
                {{ $title }}
                <span
                    style="display:block; font-size:0.8rem; margin-top: 0.5rem;">{{ __('Notifications will be sent to: :email', ['email' => $user->email]) }}</span>
                </{{ $title_container }}>
        @endif
    @endif

    <div class="notification-wrapper">
        <div class="form-check">
            <input class="form-check-input" type="radio" name="email_frequency" id="email_frequency_full_experience"
                value="all"
                {{ old('email_frequency', $emailSettings['frequency'] ?? 'all') == 'all' ? 'checked' : '' }}>
            <label class="form-check-label" for="email_frequency_full_experience">
                {{ __('Full experience (highly recommended)') }}
            </label>
        </div>
        <div class="form-check">
            <input class="form-check-input" type="radio" name="email_frequency" id="email_frequency_three_per_day"
                value="three_per_day"
                {{ old('email_frequency', $emailSettings['frequency'] ?? '') == 'three_per_day' ? 'checked' : '' }}>
            <label class="form-check-label" for="email_frequency_three_per_day">
                {{ __('Three times per day') }}
            </label>
        </div>
        <div class="form-check">
            <input class="form-check-input" type="radio" name="email_frequency" id="email_frequency_once_per_day"
                value="once_per_day"
                {{ old('email_frequency', $emailSettings['frequency'] ?? '') == 'once_per_day' ? 'checked' : '' }}>
            <label class="form-check-label" for="email_frequency_once_per_day">
                {{ __('Once per day') }}
            </label>
        </div>
        <div class="form-check">
            <input class="form-check-input" type="radio" name="email_frequency" id="email_frequency_once_per_two_days"
                value="once_per_two_days"
                {{ old('email_frequency', $emailSettings['frequency'] ?? '') == 'once_per_two_days' ? 'checked' : '' }}>
            <label class="form-check-label" for="email_frequency_once_per_two_days">
                {{ __('Once per two days') }}
            </label>
        </div>
        <div class="form-check">
            <input class="form-check-input" type="radio" name="email_frequency" id="email_frequency_none"
                value="none"
                {{ old('email_frequency', $emailSettings['frequency'] ?? '') == 'none' ? 'checked' : '' }}>
            <label class="form-check-label" for="email_frequency_none">
                {{ __('I don\'t want to receive any e-mails') }}
            </label>
        </div>
        <div class="alert alert-warning align-items-center mt-3{{ old('email_frequency', $emailSettings['frequency'] ?? '') == 'none' ? ' d-flex' : ' d-none' }}"
            role="alert">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                class="bi bi-exclamation-triangle-fill flex-shrink-0 me-2" viewBox="0 0 16 16" role="img"
                aria-label="Warning:">
                <path
                    d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z" />
            </svg>
            <div>
                {{ __('Warning: You won\'t be able to be notified when members want to connect with you.') }}
            </div>
        </div>
    </div>
    <div class="notification-wrapper {{ old('email_frequency', $emailSettings['frequency'] ?? '') == 'all' || old('email_frequency', $emailSettings['frequency'] ?? '') == 'none' ? 'd-none' : '' }}"
        id="notification_times_wrapper" style="margin-top: 10px;">
        @if (!isset($title_container))
            <div class="title notification-title">
                {{ __('When would you like to receive these e-mails?') }}:
            </div>
        @else
            <{{ $title_container }}>
                {{ __('When would you like to receive these e-mails?') }}:
                </{{ $title_container }}>
        @endif
        <div class="form-check">
            <input class="form-check-input" type="radio" name="email_timeframe" id="email_timeframe_morning"
                value="morning"
                {{ old('email_timeframe', $emailSettings['timeframe'] ?? '') == 'morning' ? 'checked' : '' }}>
            <label class="form-check-label" for="email_timeframe_morning">
                {{ __('In the morning') }}
            </label>
        </div>
        <div class="form-check">
            <input class="form-check-input" type="radio" name="email_timeframe" id="email_timeframe_afternoon"
                value="afternoon"
                {{ old('email_timeframe', $emailSettings['timeframe'] ?? '') == 'afternoon' ? 'checked' : '' }}>
            <label class="form-check-label" for="email_timeframe_afternoon">
                {{ __('In the afternoon') }}
            </label>
        </div>
        <div class="form-check">
            <input class="form-check-input" type="radio" name="email_timeframe" id="email_timeframe_evening"
                value="evening"
                {{ old('email_timeframe', $emailSettings['timeframe'] ?? '') == 'evening' ? 'checked' : '' }}>
            <label class="form-check-label" for="email_timeframe_evening">
                {{ __('In the evening') }}
            </label>
        </div>
    </div>

    <script>
        (function() {
            const radios = document.getElementsByName('email_frequency');
            const div = document.getElementById('notification_times_wrapper');
            const alertWarning = document.querySelector('.alert-warning');
            const notificationRadios = document.getElementsByName('email_timeframe');
            const defaultNotificationTimeframe = 'email_timeframe_morning';

            /**
             * Handles the visibility of the notification time section and the warning message.
             * @param {string} value - The value of the selected email frequency option.
             */
            function handleVisibility(value) {
                const isNoneOrAll = value === 'all' || value === 'none';

                div.classList.toggle('d-none', isNoneOrAll);
                alertWarning.classList.toggle('d-flex', value === 'none');
                alertWarning.classList.toggle('d-none', value !== 'none');

                if (!isNoneOrAll) {
                    setDefaultNotificationTimeframe();
                }
            }

            function setDefaultNotificationTimeframe() {
                const selectedNotificationRadio = Array.from(notificationRadios).find(function(radio) {
                    return radio.checked;
                });

                if (!selectedNotificationRadio) {
                    document.getElementById(defaultNotificationTimeframe).checked = true;
                }
            }

            function initialize() {
                const checkedRadio = Array.from(radios).find(function(radio) {
                    return radio.checked;
                });
                if (checkedRadio) {
                    handleVisibility(checkedRadio.value);
                }
            }

            radios.forEach(function(radio) {
                radio.addEventListener('change', function(e) {
                    handleVisibility(e.target.value);
                });
            });

            initialize();
        })();
    </script>
    <div class="btn-wrapper">
        @if ($theme == '004')
            <div class="row justify-content-end my-3">
                <div class="col-auto">
                    <button class="notification-btn btn btn-primary">{{ __('Save') }}</button>
                </div>
            </div>
        @else
            <button class="notification-btn btn btn-primary">{{ __('Save') }}</button>
        @endif
    </div>
</form>
