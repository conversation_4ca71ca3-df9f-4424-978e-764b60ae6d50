@if ($method->type->name === 'credit_card_hosted')
<div class="col-12">
    <h4>{{ __('Enter new card or select saved card') }}</h4>
</div>
<div class="col-12 expand expand-{{ $method->id }} d-none">
    <div class="nav nav-tabs" id="cc-tabs" role="tablist">
        <a class="nav-link active" id="new-cc" data-bs-toggle="tab"
            href="#hosted-fields" role="tab"
            data-bs-target="#hosted-fields">{{ __('New card') }}</a>
        <a class="nav-link" id="saved-cc" data-bs-toggle="tab" href="#saved-cards"
            role="tab"
            data-bs-target="#saved-cards">{{ __('Loading saved cards') }}</a>
    </div>
    <div class="tab-content" id="cc-tabcontent">
        <div class="tab-pane fade show active hosted-fields"
            id="hosted-fields" role="tabpanel" aria-labelledby="new-cc">
            <div class="hf-row">
                <div class="hf-col-2">
                    <div class="hf-form-control">
                        <label
                            for="new-card--CARD_HOLDER_NAME">{{ __('Card Holder') }}</label>
                        <div class="hf-form-field" id="new-card--CARD_HOLDER_NAME">
                        </div>
                    </div>
                </div>
            </div>
            <div class="hf-row">
                <div class="hf-col-2">
                    <div class="hf-form-control">
                        <label
                            for="new-card--CARD_NUMBER">{{ __('Card Number') }}</label>
                        <div class="hf-form-field" id="new-card--CARD_NUMBER">
                        </div>
                    </div>
                </div>
            </div>
            <div class="hf-row">
                <div class="hf-col-1">
                    <div class="hf-form-control">
                        <label
                            for="new-card--EXPIRY_FIELD">{{ __('Expiry Date') }}</label>
                        <div class="hf-form-field" id="new-card--EXPIRY_FIELD">
                        </div>
                    </div>
                </div>
                <div class="hf-col-1">
                    <div class="hf-form-control">
                        <label for="new-card--CARD_CVV">{{ __('CVV') }}</label>
                        <div class="hf-form-field" id="new-card--CARD_CVV">
                        </div>
                    </div>
                </div>
            </div>
            <div class="hf-row">
                <div class="hf-col-2">
                    <div class="form-check ms-2">
                        <input class="form-check-input" type="checkbox" value="1" id="save-cc-info" checked="checked">
                        <label class="form-check-label" for="save-cc-info">
                            {{ __('Add this card to my saved cards for quick payments') }}
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-pane fade saved-cards" id="saved-cards" role="tabpanel"
            aria-labelledby="saved-cc"></div>
    </div>
</div>
@endif
@include('global.payment.scripts.hosted-celeris')
