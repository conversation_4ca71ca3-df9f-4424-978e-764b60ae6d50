@push('scripts')
<script async src="https://pay.google.com/gp/p/js/pay.js" onload="onGooglePayLoaded()"></script>
<script>
    const baseRequest = {
        apiVersion: 2,
        apiVersionMinor: 0
    };
    const allowedCardNetworks = ["MASTERCARD", "VISA"];
    const allowedCardAuthMethods = ["PAN_ONLY", "CRYPTOGRAM_3DS"];

    const baseCardPaymentMethod = {
        type: 'CARD',
        parameters: {
            allowedAuthMethods: allowedCardAuthMethods,
            allowedCardNetworks: allowedCardNetworks
        }
    };

    let paymentsClient = null;

    function getGoogleIsReadyToPayRequest() {
        return Object.assign({},
            baseRequest, {
                allowedPaymentMethods: [baseCardPaymentMethod]
            }
        );
    }

    function getGooglePaymentDataRequest(payment) {
        const tokenizationSpecification = {
            type: 'PAYMENT_GATEWAY',
            parameters: {
                'gateway': payment.details.gateway,
                'gatewayMerchantId': payment.details.gatewayMerchantId
            }
        };
        const cardPaymentMethod = Object.assign({},
            baseCardPaymentMethod, {
                tokenizationSpecification: tokenizationSpecification
            }
        );

        const paymentDataRequest = Object.assign({}, baseRequest);
        paymentDataRequest.allowedPaymentMethods = [cardPaymentMethod];
        paymentDataRequest.transactionInfo = getGoogleTransactionInfo(payment);
        paymentDataRequest.merchantInfo = {
            merchantId: payment.details.merchantId,
            merchantName: payment.details.paymentReference
        };

        return paymentDataRequest;
    }

    function getGooglePaymentsClient() {
        if (paymentsClient === null) {
            paymentsClient = new google.payments.api.PaymentsClient({
                environment: 'PRODUCTION'
            });
        }
        return paymentsClient;
    }

    function onGooglePayLoaded() {
        const paymentsClient = getGooglePaymentsClient();
        paymentsClient.isReadyToPay(getGoogleIsReadyToPayRequest())
            .then(function(response) {
                if (response.result) {
                    $('input[name="payment_method_id"]').each(function(idx, elem) {
                        if ($(elem).data('payment_type') === 'google_pay') {
                            $(elem).parent().removeClass('d-none');
                            $(elem).next('label').removeClass('d-none');
                            $(elem).closest('label').removeClass('d-none'); // In case the label is the parent element of the input
                        }
                    });
                }
            })
            .catch(function(err) {});
    }

    function getGoogleTransactionInfo(payment) {
        return {
            countryCode: payment.details.countryCode,
            currencyCode: payment.details.currencyAlphaCode,
            totalPriceStatus: 'FINAL',
            totalPrice: payment.details.amount
        };
    }

    function startGooglePayment(paymentData, payment) {

        let paymentToken = paymentData.paymentMethodData.tokenizationData.token;
        const headers = {
            "Content-Type": "application/json",
            "Authorization": payment.details.token,
            "IdempotencyKey": payment.details.IdempotencyKey,
        };

        const body = {
            transaction: {
                transactionUniqueIdentifier: "",
                merchantReference: payment.details.merchantReference,
                paymentReference: payment.details.paymentReference,
                amount: {
                    totalAmount: payment.details.amount,
                    currencyAlphaCode: payment.details.currencyAlphaCode,
                },
                "source": "ECOMMERCE"
            },
            merchant: {
                descriptor: payment.details.url,
                mid: payment.details.mid,
                tid: payment.details.tid,
            },
            card: {
                tokenPayment: {
                    token: btoa(JSON.stringify(JSON.parse(paymentToken.token))),
                    merchantShopperReference: payment.details.paymentReference,
                    tokenProvider: "GooglePay",
                    googlePaySettings: {
                        gatewayMerchantIdentifier: payment.details.gatewayMerchantIdentifier,
                    }
                }
            },
            terminal: {
                terminalType: "NONE"
            },
            customer: {
                contact: {
                    email: payment.details.email,
                }
            }
        }
        fetch("https://unify.truevo.com/payments/sale", {
                method: "POST",
                body: body ? JSON.stringify(body) : null,
                headers: headers,
            })
            .then(res => {
                verifyPayment(res, payment.details.token).then(function(verify) {
                    window.location.href = verify.url;
                }).catch((error) => {
                    window.location.href = payment.details.cancelUrl;
                });
            })
            .catch(err => {
                window.location.href = payment.details.cancelUrl;
            });
    }
</script>
@endpush
