@push('scripts')
    <script>
        if (performance.navigation.type == 2) {
            location.reload(true);
        }
    </script>
    <script src="{{ asset('global/js/sdk.bundle.js') }}"></script>
    <script src="{{ asset('global/js/fingerprint-datapicker-lib.js') }}"></script>
    <script>
        $(document).ready(function() {
            let payment_method_id = $('input[data-payment_type="credit_card_hosted"]').val();
            let urlTransactionForm = $("#transaction").attr('action');
            let selectedSavedCard = {};

            $('#hosted-fields').block({
                message: '<div class="spinner-border"></div>',
                overlayCSS: {
                    backgroundColor: 'var(--bg-body)'
                },
                css: {
                    border: '0px',
                    backgroundColor: 'var(--bg-body)',
                }
            });
            $("#saved-cards").block({
                message: '<div class="spinner-border"></div>',
                overlayCSS: {
                    backgroundColor: 'var(--bg-body)'
                },
                css: {
                    border: '0px',
                    backgroundColor: 'var(--bg-body)',
                }
            });

            @if (isset($iframePayment) && is_array($iframePayment))
                var fieldStyles = {
                        'placeholder-color': '#ababab',
                        'font-size': '16px',
                        'padding': '0 0 0 17px',
                        'line-height': '28px',
                        'color': '#814482',
                        'font-weight': 'bold',
                    };

                getSavedCreditCardsList(payment_method_id);
                getRemoveCardsPopup(payment_method_id);

                let newCardForm = hostedFieldsSdk.initialize({
                    iframeSrc: '{{ $iframePayment['url'] }}',
                    requestID: '{{ $iframePayment['requestID'] }}',
                    fields: {
                        CARD_HOLDER_NAME: {
                            containerId: 'new-card--CARD_HOLDER_NAME',
                            placeholder: '{{ __('Enter card holder name') }}',
                            styles: fieldStyles
                        },
                        EXPIRY_FIELD: {
                            containerId: 'new-card--EXPIRY_FIELD',
                            placeholder: 'MM/YY',
                            styles: fieldStyles
                        },
                        CARD_CVV: {
                            containerId: 'new-card--CARD_CVV',
                            placeholder: '{{ __('Enter CVV') }}',
                            styles: fieldStyles
                        },
                        CARD_NUMBER: {
                            containerId: 'new-card--CARD_NUMBER',
                            placeholder: '1234-1234-1234-1234',
                            styles: fieldStyles
                        },
                    }
                }).then(function(sdkInstance) {
                    $(".hosted-fields").unblock();
                    return sdkInstance;
                }).catch(function(error) {
                    window.location.reload();
                });
            @endif

            $('.buy-now').on('click', function(event) {
                event.preventDefault();
                $('#form-loader').addClass('d-none');
                $('#payment-form').removeClass('d-none');
                let product_id = $(".product-radio:checked").val();
                if (product_id === undefined) {
                    product_id = $(this).data('product_id');
                }
                if (product_id === undefined) {
                    product_id = $("input[name=product_id]").val()
                }
                let buyNow = $(this);
                let buyNowHtml = $(this).html();
                if ($('.payment-method-radio:checked').data("payment_type") == "credit_card_hosted") {
                    if ($('#new-cc').hasClass('active')) {
                        newCardForm.then(function(sdkInstance) {
                            buyNow.html('{{ __('Please wait...') }}');
                            sdkInstance.getHostedFieldsState(function(state) {
                                if (areFieldsValid(state)) {
                                    $(this).attr('disabled', true);
                                    toastr.success(
                                        "{{ __('Submitting payment') }}");
                                    sdkInstance.requestTokenization({
                                            saveCard: $('#save-cc-info').is(':checked')
                                        })
                                        .then(function(tokenizedResponse) {
                                            getPayment(product_id, payment_method_id,
                                                urlTransactionForm).then(function(
                                                payment) {
                                                window.location.href =
                                                    generateTokenPaymentLink(
                                                        payment.orderId,
                                                        tokenizedResponse.tokenID);
                                            });
                                        })
                                        .catch(function(tokenizationError) {
                                            if (!tokenizationError.canBeReused) {
                                                toastr.error(
                                                    "{{ __('These fields can no longer be reused') }}"
                                                );
                                                window.location.reload();
                                            }
                                        })
                                } else {
                                    buyNow.html(buyNowHtml);
                                    toastr.error(
                                        "{{ __('Form is not valid! Please enter valid details!') }}"
                                    );
                                    return;
                                }
                            })
                        })
                    }
                    if ($('#saved-cc').hasClass('active')) {
                        let card = $('.saved-card.selected').first();
                        let id = card.data('card-id');
                        if (id === undefined){
                            toastr.error("{{ __('Please select a card') }}");
                            return;
                        }
                        let cvvField = $(card).find('.cvv-field');
                        let cvvContainerId = $(card).find('.cvv-field').prop('id');
                        selectedSavedCard[cvvContainerId].getHostedFieldsState(function(state) {
                            var keys = Object.keys(state);
                            var fieldState = state[keys[0]];
                            if (fieldState.isValid) {
                                $(this).attr('disabled', true);
                                selectedSavedCard[cvvContainerId].requestTokenUpdate({
                                    tokenID: id
                                }).then(function(tokenizedResponse) {
                                    toastr.success("{{ __('Submitting payment') }}");
                                    getPayment(product_id, payment_method_id, urlTransactionForm)
                                        .then(function(
                                            payment) {
                                            window.location.href =
                                                generateTokenPaymentLink(
                                                    payment.orderId,
                                                    tokenizedResponse.tokenID
                                                );
                                        });
                                }).catch(function(tokenizationError) {
                                    if (!tokenizationError.canBeReused) {
                                        toastr.error("{{ __('These fields can no longer be reused') }}");
                                        window.location.reload();
                                    }
                                })
                            } else {
                                $(cvvField).addClass('hosted-field-isTouched');
                                toastr.error("{{ __('Please enter a valid CVV code!') }}");
                            }
                            buyNow.html(buyNowHtml);
                        });
                    };
                };
            });

            $(document).on("click", ".saved-card", function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).siblings('.saved-card').removeClass('selected');
                $(this).toggleClass('selected');
                let cvvField = $(this).find('.cvv-field');
                if (cvvField.is(':empty')) {
                    getSavedCardCvv(cvvField.prop('id'), $(this).data('card-id'));
                }
            });

            async function getSavedCardCvv(cvvContainerId, cardId) {
                if ($("#" + cvvContainerId).hasClass('in-progress')) {
                    return false;
                }
                $("#" + cvvContainerId).addClass('in-progress');
                $("#" + cvvContainerId).block({
                    message: '<div class="spinner-border"></div>',
                    overlayCSS: {
                        backgroundColor: 'var(--bg-body)'
                    },
                    css: {
                        border: '0px',
                        backgroundColor: 'var(--bg-body)',
                    }
                });
                getHostedFields(cardId).then(function(response) {
                    hostedFieldsSdk.initialize({
                        iframeSrc: response.url,
                        requestID: response.requestID,
                        fields: {
                            CARD_CVV: {
                                containerId: cvvContainerId,
                                placeholder: '{{ __('Enter CVV') }}',
                                styles: fieldStyles
                            },
                        }
                    }).then(function(sdkInstance) {
                        $("#" + cvvContainerId).unblock();
                        $("#" + cvvContainerId).removeClass('in-progress');
                        selectedSavedCard[cvvContainerId] = sdkInstance;
                    });
                });
            }

            function generateTokenPaymentLink(orderId, tokenId) {
                let url = new URL("{{ route('celerispayTokenizationPayment') }}");
                url.searchParams.set('orderId', orderId);
                url.searchParams.set('tokenId', tokenId);
                let fingerprintFields = __gateway.getFingerprintString();
                url.searchParams.set('fingerprintFields', fingerprintFields);

                return url.toString();
            }

            $(document).on('submit', '.remove-card-form', function(event) {
                event.preventDefault();
                let tokenId = this.id.value;
                let _token = $("input[name=_token]").val();
                let payment_method_id = $('input[data-payment_type="credit_card_hosted"]').val();
                $.ajax({
                    type: 'DELETE',
                    url: '{{ route('celerispayDeleteToken') }}',
                    data: {
                        _token: _token,
                        tokenId: tokenId,
                        payment_method_id: payment_method_id
                    },
                    success: function(response) {
                        if (response.success === true) {
                            $('.modal').modal('hide');
                            toastr.success("{{ __('Card successfully deleted') }}");
                            $("#cc-" + tokenId).remove();
                            $("#label-" + tokenId).remove();
                            if ($('.saved-card').length == 0) {
                                $('#saved-cc').html('{{ __('No saved cards') }}');
                                $('#saved-cc').addClass('disabled');
                                $('#new-cc').tab('show');
                            }
                        } else {
                            $('.modal').modal('hide');
                            toastr.error(
                                "{{ __('Card could not be deleted, please try again later') }}"
                                );
                            return;
                        }
                    },
                    errors: function() {
                        $('.modal').modal('hide');
                        toastr.error(
                            "{{ __('Card could not be deleted, please try again later') }}"
                            );
                        return;
                    }
                })
            });

            function areFieldsValid(state) {
                var isValid = true;
                var keys = Object.keys(state);
                for (var i = 0; i < keys.length; i++) {
                    var fieldState = state[keys[i]];
                    isValid = isValid && fieldState.isValid;
                    if (!isValid) {
                        $('#new-card--' + keys[i]).addClass('hosted-field-isTouched');
                    }
                }

                return isValid;
            }

            async function getHostedFields(tokenId) {
                let _token = $("input[name=_token]").val();
                let payment_method_id = $('input[data-payment_type="credit_card_hosted"]').val();
                let hostedFields;
                await $.ajax({
                    type: "POST",
                    url: '{{ route('celerispayGetTokens') }}',
                    data: {
                        _token: _token,
                        tokenId: tokenId,
                        payment_method_id: payment_method_id
                    },
                    success: function(response) {
                        hostedFields = response;
                    }
                });

                return hostedFields;
            }

            function modalClosed() {
                $('.buy-now').html('{{ __('Buy Now!') }}');
            }

            function getSavedCreditCardsList(payment_method_id) {
                let _token = $("input[name=_token]").val();
                $.ajax({
                    type: "POST",
                    url: '{{ route('celerispayGetSavedCardsList') }}',
                    data: {
                        _token: _token,
                        payment_method_id: payment_method_id,
                        @if (isset($productType) && $productType == 'premium')
                            premium: true
                        @endif
                    },
                    success: function(response) {
                        $('#saved-cards').html(response.result);
                        if ($('.saved-card').length == 0) {
                            $('#saved-cc').html('{{ __('No saved cards') }}');
                            $('#saved-cc').addClass('disabled');
                            $('#new-cc').tab('show');
                        } else {
                            $('#saved-cc').html('{{ __('Saved cards') }}');
                            $('#saved-cc').tab('show');
                            $('.saved-card').first().trigger('click');
                        }
                    },
                    error: function(response) {
                        $(".hosted-fields").unblock();
                    }
                });
                $(".hosted-fields").unblock();
            }

            function getRemoveCardsPopup(payment_method_id) {
                let _token = $("input[name=_token]").val();
                $.ajax({
                    type: "POST",
                    url: '{{ route('celerispayGetRemoveCardPopup') }}',
                    data: {
                        _token: _token,
                        payment_method_id: payment_method_id
                    },
                    success: function(response) {
                        if ($('.remove-card-modal').length == 0) {
                            $('body').append(response.result);
                        } else {
                            $('.remove-card-modal').replaceWith(response.result);
                        }
                        $('.remove-card-modal').modal('show');
                    }
                });
            }

            $(document).on('click', '.payment-method-label .nav-link, .payment-method-label .saved-card, .payment-method-label .hf-row', function() {
                $('#' + $(this).parents('label').attr('for')).prop('checked', true);
            });

        })
    </script>
@endpush
