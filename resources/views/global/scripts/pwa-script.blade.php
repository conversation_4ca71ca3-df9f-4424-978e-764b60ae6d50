<script>
    let installPrompt = null;
    const installButtonWrapper = document.querySelector("#install");
    const installBanner = document.querySelector("#install-banner");
    const installButton = document.querySelector(".install-app");
    const installButtonClose = document.querySelector(".install-pwa-close");
    const bannerButtonClose = document.querySelector(".pwa-banner-close");
    const disableAppShow = localStorage.getItem('disableAppShow');

    // Detects if device is on iOS
    const isIos = () => {
        const userAgent = window.navigator.userAgent.toLowerCase();
        return /iphone|ipad|ipod/.test( userAgent );
    }
    // Detects if device is in standalone mode
    const isInStandaloneMode = () => ('standalone' in window.navigator) && (window.navigator.standalone);

    // Checks if should display install popup notification:
    if (isIos() && !isInStandaloneMode()) {
        if(!disableAppShow) {
            installBanner.classList.remove("d-none");
        }
    } else {
        //init install prompt on android
        window.addEventListener("beforeinstallprompt", (event) => {
            event.preventDefault();
            installPrompt = event;
            if(!disableAppShow) {
                installButtonWrapper.classList.remove("d-none");
            }
        });
    }

    installButton.addEventListener("click", async () => {
        if (!installPrompt) {
            return;
        }
        const result = await installPrompt.prompt();
        console.log(`Install prompt was: ${result.outcome}`);
        disableInAppInstallPrompt();
    });

    installButtonClose.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        installButtonWrapper.classList.add("d-none");
        localStorage.setItem('disableAppShow', 1);
    })

    bannerButtonClose.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        installBanner.classList.add("d-none");
        localStorage.setItem('disableAppShow', 1);
    })

    function disableInAppInstallPrompt() {
        installPrompt = null;
        installButtonWrapper.classList.add('d-none');
    }

    window.addEventListener("appinstalled", () => {
        disableInAppInstallPrompt();
    });

    //load sw and trigger app install
    window.addEventListener("load", () => {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/global/js/sw.js').then(
                (registration) => {},
                (error) => {
                    console.log('Service worker registration failed:', error)
                }
            )
        } else {
            console.log('Service workers are not supported.')
        }
    });
</script>
